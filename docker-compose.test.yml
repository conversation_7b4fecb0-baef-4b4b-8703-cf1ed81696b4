version: "3.9"

services:
  postgres-auth-gateway-test:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - auth-gateway/.env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    expose:
      - 5432

  auth-gateway-test:
    command: gradle integrationTest ${TEST_CMD}
    build:
      context: ./auth-gateway/
      dockerfile: Dockerfile
    env_file:
      - auth-gateway/.env
    depends_on:
      - postgres-auth-gateway-test
    volumes:
      - ./auth-gateway/build:/project/build/
    ports:
      - 5005:5005


#Environment for this will be decided when we will finish working on auth
  user-infrastructure-test:
    #  -DREMOTEDEBUG=true
    command: gradle integrationTest ${TEST_CMD}
    build:
      context: ./user-infrastructure/
      dockerfile: Dockerfile
    env_file:
      - user-infrastructure/.env
    depends_on:
      - postgres-user-infrastructure-test
    volumes:
      - ./user-infrastructure/build:/project/build/
    ports:
      - 5006:5005


  postgres-user-infrastructure-test:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - ./user-infrastructure/example.test.env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    expose:
      - 5432
