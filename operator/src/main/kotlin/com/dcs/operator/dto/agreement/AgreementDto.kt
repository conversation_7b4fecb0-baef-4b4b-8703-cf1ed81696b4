package com.dcs.operator.dto.agreement

import java.time.LocalDate

data class AgreementDto (
    val agreementID: String = "",
    val agreementHash: String = "",
    val avatarID: String = "",
    val agreementDate: String = "",
    val adviser: String = "",
    val avatar: String = "",
    val platform: String = "",
    val investment: String = "",
    val fee: String = "",
    val status: String = "",
    val declinedBy: String = "",
    val declineReason: String = "",
    var agreementType: AgreementType? = null,
    var filePath: String? = ""
) {

}

enum class AgreementType {
    INVESTMENT,
    NODE_ACQUISITION
}