package com.dcs.operator.service

import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.stereotype.Service
import java.util.*
import java.util.function.Function

@Service
class JwtService {

    @Value("\${jwt.expirationMs}")
    val jwtTokenValidity: Int = 3600000

    @Value("\${jwt.secret}")
    var secret: String? = "404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970"

    fun getUsernameFromToken(token: String?): String {
        return getClaimFromToken(token) { obj: Claims? -> obj!!.subject }
    }


    fun getRoleeFromToken(jwt: String?): String {
        return getClaimFromToken(jwt) { obj: Claims? -> obj!!["role"] as String }
    }

    fun getExpirationDateFromToken(token: String?): Date? {
        return getClaimFromToken(token) { obj: Claims? -> obj!!.expiration }
    }

    fun <T> getClaimFromToken(token: String?, claimsResolver: Function<Claims?, T>): T {
        val claims: Claims = getAllClaimsFromToken(token)
        return claimsResolver.apply(claims)
    }

    fun getAllClaimsFromToken(token: String?): Claims {
        return Jwts.parser().setSigningKey(secret?.let { encodeSecret(it) }).parseClaimsJws(token).body
    }


    fun isTokenExpired(token: String): Boolean {
        val expiration = getExpirationDateFromToken(token)
        return expiration!!.before(Date())
    }

    //generate token for user
    fun generateToken(id: Long, username: String, role: String): String {
        val claimMap = mapOf(
            "username" to username,
            "role" to role,
        )
        val claims: Claims = Jwts.claims(claimMap)
        return doGenerateToken(claims, id)
    }

    fun doGenerateToken(claims: Claims, subject: Long): String {

        return Jwts.builder().setClaims(claims).setSubject(subject.toString())
            .setIssuedAt(Date(System.currentTimeMillis()))
            .setExpiration(Date(System.currentTimeMillis() + jwtTokenValidity!!))
            .signWith(SignatureAlgorithm.HS512, secret?.let { encodeSecret(it) }).compact()
    }

    //validate token
    fun validateToken(token: String, userDetails: UserDetails): Boolean {
        val username: String = getUsernameFromToken(token)
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token))
    }

    fun getProfileNameFromToken(jwt: String?): String {
        return getClaimFromToken(jwt) { obj: Claims? -> obj!!["username"] as String }
    }

    fun JwtService.encodeSecret(secret: String): String? {
        return Base64.getEncoder().encodeToString(secret.toByteArray() )
    }

}
