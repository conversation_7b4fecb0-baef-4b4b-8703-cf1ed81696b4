version: "3.9"

services:
  postgres-auth-gateway:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - auth-gateway/.env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - "5436:5432"

  auth-gateway:
    command: gradle bootRun
    build:
      context: ./auth-gateway/
      dockerfile: Dockerfile
    env_file:
      - auth-gateway/.env
    depends_on:
      - postgres-auth-gateway
    links:
      - redis
    ports:
      - 8080:8080
      - 5005:5005

  frontend-auth:
    image: frontend
    env_file:
      - ./frontend/.env.auth
    depends_on:
      - auth-gateway
    volumes:
      - './frontend:/usr/src/app'
    ports:
      - 3000:3000
    command: bash -c "yarn install && yarn start"
    environment:
      - VIRTUAL_HOST=dcs.quantumobile.com
      - VIRTUAL_PORT=3000
      - LETSENCRYPT_HOST=dcs.quantumobile.com
      - LETSENCRYPT_EMAIL=<EMAIL>

  frontend-cabinet:
    image: frontend
    env_file:
      - ./frontend/.env.cabinet
    volumes:
      - './frontend:/usr/src/app'
    ports:
      - 3001:3000
    command: bash -c "yarn install && yarn start"
    environment:
      - VIRTUAL_HOST=polity-cabinet.quantumobile.com
      - VIRTUAL_PORT=3000
      - LETSENCRYPT_HOST=polity-cabinet.quantumobile.com
      - LETSENCRYPT_EMAIL=<EMAIL>

  pgadmin:
    container_name: pgadmin-local
    image: dpage/pgadmin4:latest
    ports:
      - 5050:5050
    depends_on:
      - postgres-auth-gateway
    env_file:
      - ./pgadmin.env

  postgres-user-infrastructure:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - ./user-infrastructure/.env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - "5435:5432"

  user-infrastructure:
    # -DREMOTEDEBUG=true
    command: gradle bootRun -DREMOTEDEBUG=false
    build:
      context: ./user-infrastructure/
      dockerfile: Dockerfile
    env_file:
      - user-infrastructure/.env
    depends_on:
      - postgres-user-infrastructure
      - auth-gateway
    ports:
      - 8081:8080
      - 5006:5005

  operator:
    # -DREMOTEDEBUG=true
    command: gradle bootRun
    build:
      context: ./operator/
      dockerfile: Dockerfile
#    env_file:
#      - ./portal/.env
    ports:
      - 8082:8080
      - 5007:5005

  postgres-polity-vault:
    image: postgres:12.0-alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - ./polity_vault/.env
    ports:
      - "5434:5432"

  polity-vault:
    build:
      context: ./polity_vault/
      dockerfile: Dockerfile
    env_file:
      - ./polity_vault/.env
    ports:
      - "8000:8000"
    depends_on:
      - postgres-polity-vault
      - redis

  wallet-infrastructure-service:
    build:
      context: ./wallet-infrastructure-service/
      target: release
      dockerfile: Dockerfile
    image: wallet-infrastructure-service
    volumes:
      - "./:/app"
    restart: always
    env_file:
      - ./wallet-infrastructure-service/.env
    ports:
      - "8083:8083"
    depends_on:
      - wallet-infrastructure-storage
      - polity-vault

  wallet-infrastructure-storage:
    image: postgres:15.4
    restart: always
    healthcheck:
      test: [ "CMD", "pg_isready", "-q", "-d", "$POSTGRES_DB", "-U", "$POSTGRES_USER" ]
      interval: 10s
      timeout: 3s
      retries: 5
    env_file:
      - ./wallet-infrastructure-service/.env
    ports:
      - "5433:5432"


  redis:
    image: 'bitnami/redis:latest'
    env_file:
      - ./polity_vault/.env
    expose:
      - '6379'
    environment:
      - REDIS_PASSWORD=26ecfc64-50d1-4317-b6a2-2f39aa521f23

  store-auction-storage:
    image: postgres:15.4
    restart: always
    healthcheck:
      test: [ "CMD", "pg_isready", "-q", "-d", "$POSTGRES_DB", "-U", "$POSTGRES_USER" ]
      interval: 10s
      timeout: 3s
      retries: 5
    env_file:
      - ./store-auction/.env
    ports:
      - "5437:5432"
      - "5438:5432"

  store-auction:
    build:
      context: ./store-auction/
      target: release
      dockerfile: Dockerfile
    image: store-auction
    volumes:
      - "./:/app"
    restart: always
    env_file:
      - ./store-auction/.env
    ports:
      - "8084:8084"
    depends_on:
      - store-auction-storage