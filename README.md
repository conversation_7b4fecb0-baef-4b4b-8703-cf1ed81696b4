# Polity (a.k.a Bastion, a.k.a DCS)

Start up instructions:

1) Add `.env` file to each directory and set all necessary params
2) Go to root directory
3) `make run-bwi` to run Wallet Infrastructure, Polity Vault, redis and celery

Please, wait for `celery@af0a6fcfeb43 ready.` in your logs

4) `docker-compose up --build user-infrastructure` to run Auth-Gateway and User-infrastructure services

Please, wait for `INFO 211 --- [           main] c.d.u.UserInfrastructureApplicationKt    : Started UserInfrastructureApplicationKt ` in your logs. It can take a time

5) `docker-compose up --build frontend-auth` to run frontend part for Auth-Gateway
6) `docker-compose up --build frontend-cabinet` to run frontend part for Cabinet


# Services details
## Polity Vault

Polity Vault is a service for storing credentials and managing lifecycles of wallets.

To view API docs go to `http://localhost:8000/swagger/` or `http://localhost:8000/redoc/`
If you log in APIs via Swagger don't forget to put `Bear<PERSON> <your_token>` while authenticating.

Endpoints are available at `http://localhost:8000/`

## Wallet Infrastructure

Wallet Infrastructure is a service for storing wallets and provides interaction between MPC wallet providers and Polity customers.

To view API docs go to `http://localhost:8083/docs/`
If you log in APIs via Swagger don't forget to put `Bearer <your_token>` while authenticating.

Endpoints are available at `http://localhost:8083/`


## Auth-gateway

Auth-gateway is a service for storing and managing user data.
To view API docs go to `http://localhost:8080/swagger-ui/index.html`

Endpoints are available at `http://localhost:8080/`


## User-infrastructure

User-infrastructure is a service for storing and managing data related to Corda and agreements.
To view API docs go to `http://localhost:8081/swagger-ui/index.html`

Endpoints are available at `http://localhost:8081/`


## Tests
|Full Tests|Pipeline tests|
|---|---|
|![Tests](https://dcs-ci.quantumobile.com/buildStatus/icon?job=test-job)|![Tests](https://dcs-ci.quantumobile.com/buildStatus/icon?job=pipeline-tests)|








