from django.db import models


class Product(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    category = models.CharField(max_length=50)
    merchant = models.CharField(max_length=100)
    min_price = models.IntegerField()
    description = models.CharField(max_length=200)

    class Meta:
        db_table = "products"
        managed = False

    def __str__(self):
        return self.name


class Permission(models.Model):
    id = models.AutoField(primary_key=True)
    category = models.CharField(max_length=50)
    action = models.CharField(max_length=50)

    class Meta:
        db_table = "permissions"
        managed = False

    def __str__(self):
        return self.category


class Tier(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    product_quantity = models.IntegerField()
    buy_now_price = models.IntegerField()
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    permission = models.Foreign<PERSON>ey(Permission, on_delete=models.CASCADE)

    class Meta:
        db_table = "tiers"
        managed = False

    def __str__(self):
        return self.name
