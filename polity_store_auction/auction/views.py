from django.http import Http404
from django.shortcuts import render

from .models import Product, Tier, Permission


def product_detail(request, product_id):
    try:
        product = Product.objects.get(pk=product_id)
    except Product.DoesNotExist:
        raise Http404("Product does not exist")
    return product


def permission_detail(request, permission_id):
    try:
        permission = Permission.objects.get(pk=permission_id)
    except Permission.DoesNotExist:
        raise Http404("Permission does not exist")
    return permission


def tier_detail(request, tier_id):
    try:
        tier = Tier.objects.get(pk=tier_id)
    except Tier.DoesNotExist:
        raise Http404("Tier does not exist")
    return tier
