POSTGRES_USER=user
POSTGRES_DB=store_auction_database
POSTGRES_PASSWORD=qwerty
POSTGRES_HOST=store-auction-storage
POSTGRES_PORT=5437
SECRET_KEY=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_PASSWORD=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DEBUG=True
DOMAIN_NAME=http://localhost
AWS_REGION=us-west-1
ENVIRONMENT=dev
REDIS_PASSWORD=password

# for prod
VAULT_NAMESPACE=vault_namespace
VAULT_ROLE_ID=vault_role
VAULT_SECRET_ID=vault_secret

# for local\prod
VAULT_URL=vault_url

# for local
VAULT_TOKEN=testtoken