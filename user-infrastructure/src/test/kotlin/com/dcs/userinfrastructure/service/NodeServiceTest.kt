//package com.dcs.userinfrastructure.service
//
//import com.dcs.userinfrastructure.dto.flow.FlowID
//import com.dcs.userinfrastructure.dto.flow.FlowRetrieveResponse
//import com.dcs.userinfrastructure.dto.flow.FlowStartResponse
//import com.dcs.userinfrastructure.enums.FlowStatus
//import com.dcs.userinfrastructure.model.Avatar
//import kong.unirest.GetRequest
//import kong.unirest.HttpRequestWithBody
//import kong.unirest.HttpResponse
//import kong.unirest.RequestBodyEntity
//import kong.unirest.Unirest
//import org.junit.jupiter.api.Assertions.assertEquals
//import org.junit.jupiter.api.Test
//import org.mockito.Mockito.*
//import org.springframework.mock.http.client.MockClientHttpResponse
//
//
//class NodeServiceTest {
//
//    val nodeService = NodeService()
//    val flowName = "flowname"
//    val agreementId = "111"
//    val paremetersInJson = "js parameters on"
//    val clientId = "clientid"
//    val avatar = Avatar(
//        commonName = "AvatarA",
//        organizationUnit = "INC",
//        locality = "London",
//        country = "GB",
//        port = 12119,
//        state = null
//    )
//
//    @Test
//    fun whenStartAndRetrieveFlow_thenReturnCompletedState() {
//
//        val flowStartResponse = FlowStartResponse(
//            FlowID(
//                "sad"
//            ),
//            "12"
//        )
//
//        val httpResponse = mock(HttpResponse::class.java) as HttpResponse<FlowStartResponse>
//        `when`(httpResponse .body).thenReturn(flowStartResponse)
////        every { httpResponse.body } returns flowStartResponse
//
//        val requestBodyEntity = mock(RequestBodyEntity::class.java)
//        `when`(requestBodyEntity.asObject(FlowStartResponse::class.java)).thenReturn(httpResponse)
//
//
//        val httpRequestWithBody = mock(HttpRequestWithBody::class.java)
//        val body = mapOf(
//            "rpcStartFlowRequest" to
//                    mapOf(
//                        "flowName" to flowName,
//                        "clientId" to clientId,
//                        "parameters" to mapOf("parametersInJson" to paremetersInJson)
//                    )
//        )
//        `when`(httpRequestWithBody.body(body)).thenReturn(requestBodyEntity)
//
//        mockStatic(
//            Unirest::class.java
//        )
//        val port = avatar.port
//        val url = nodeService.URL
//        `when`(Unirest.post("$url:$port/api/v1/flowstarter/startflow")).thenReturn(httpRequestWithBody)
//
//
//        val flowRetrieveResponse = FlowRetrieveResponse(
//            status = FlowStatus.COMPLETED
//        )
//
//        val httpResponseGet = mock(HttpResponse::class.java) as HttpResponse<FlowRetrieveResponse>
//        `when`(httpResponseGet.body).thenReturn(flowRetrieveResponse)
////        every { httpResponseGet.getBody() } returns flowRetrieveResponse
//
//        val getRequest = mock(GetRequest::class.java)
//        `when`(getRequest.asObject(FlowRetrieveResponse::class.java)).thenReturn(httpResponseGet)
//
//
//        `when`(Unirest.get("$url:$port/api/v1/flowstarter/flowoutcome/${flowStartResponse.flowId.uuid}")).thenReturn(getRequest)
//
//        //when
//        val result = nodeService.startAndRetrieveFlow(
//            avatar.port, flowName, paremetersInJson, clientId
//        )
//
//        assertEquals(result, "COMPLETED")
//        //then
////        verify(exactly = 1) { customerRepository.findByUsernameAndFetchRoutesEagerly(username) }
//    }
//}