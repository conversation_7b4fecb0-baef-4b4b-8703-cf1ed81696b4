package com.dcs.userinfrastructure

import com.dcs.userinfrastructure.dto.agreement.AgreementDto
import com.dcs.userinfrastructure.dto.agreement.AgreementType
import com.dcs.userinfrastructure.dto.agreement.ProposeAgreementRequest
import com.dcs.userinfrastructure.dto.avatar.NotifyPortalAvatarResponse
import com.dcs.userinfrastructure.model.Avatar
import com.dcs.userinfrastructure.model.Node
import com.dcs.userinfrastructure.repository.NodeRepository
import com.dcs.userinfrastructure.service.AgreementService
import com.dcs.userinfrastructure.service.AvatarService
import com.dcs.userinfrastructure.service.JwtService
import com.dcs.userinfrastructure.service.PortalService
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.github.oshai.kotlinlogging.KotlinLogging
import io.mockk.every
import io.mockk.mockk
import liquibase.exception.DatabaseException
import org.assertj.core.api.Assertions.*
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.context.annotation.Bean
import org.springframework.core.io.FileSystemResource
import org.springframework.http.*
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.jdbc.JdbcTestUtils
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

@ActiveProfiles("test")
@SpringBootTest(
    classes = arrayOf(UserInfrastructureApplication::class),
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class AdviserIntegrationTest {
    private val logger = KotlinLogging.logger {}

    @Value("\${corda.url}")
    val cordaUrl: String = ""

    @TestConfiguration
    class ControllerTestConfig {
        @Bean
        fun portalService() = mockk<PortalService>()
    }

    @MockkBean
    lateinit var portalService: PortalService

    @Autowired
    lateinit var restTemplate: TestRestTemplate

    @Autowired
    lateinit var nodeRepository: NodeRepository

    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate


    @Autowired
    lateinit var jwtService: JwtService

    @Autowired
    lateinit var avatarService: AvatarService


    @Autowired
    lateinit var agreementService: AgreementService

    val mapper = jacksonObjectMapper()

    var node: Node? = null
    var avatar: Avatar? = null
    var secondAvatar: Avatar? = null


    @BeforeEach
    @Throws(DatabaseException::class)
    fun setUp() {
        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "agreement", "avatar", "node")
        node = nodeRepository.save(
            Node(
                commonName = "AvatarA",
                organizationUnit = "INC",
                locality = "London",
                country = "GB",
                url = "$cordaUrl:12119",
            )
        )
        avatar = avatarService.save(
            Avatar(
                name = "AvatarA",
                node = node!!,
                photoURL = ""
            )
        )
        secondAvatar = avatarService.save(
            Avatar(
                name = "AvatarB",
                node = node!!,
                photoURL = ""
            )
        )
    }

//    @AfterEach
//    @Throws(DatabaseException::class)
//    fun tearDown() {
//    }

    @Test
    fun whenListAvatars_thenShouldReturnListOfAvatars() {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")

        headers.set("Authorization", "Bearer $jwtToken")
        val request = HttpEntity<Void>(headers)
        val result = restTemplate.exchange(
            "/api/v1/adviser/avatars", HttpMethod.GET, request, List::class.java
        )
        assertThat(result.statusCode).isEqualTo(HttpStatus.OK)
        assertThat(result.body!!.size).isEqualTo(2)
    }

    fun propose(): String {

        val headers = HttpHeaders()
        headers.contentType = MediaType.MULTIPART_FORM_DATA
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")
        headers.set("Authorization", "Bearer $jwtToken")

        val body: MultiValueMap<String, Any> = LinkedMultiValueMap()
        body.add("avatar", avatar!!.name)
        body.add("investment", "2000000")
        body.add("fee", "20%")
        body.add("agreementType", AgreementType.SAFEHERON_ACQUISITION.name)
        val requestEntity = HttpEntity<MultiValueMap<String, Any>>(body, headers)
        val result = restTemplate.postForEntity("/api/v1/adviser/agreements", requestEntity, AgreementDto::class.java)
        return result.body!!.agreementID

    }

    @Test
    fun whenProposeAgreement_thenShouldReturnStatus200AndChangeState() {

        val headers = HttpHeaders()
        headers.contentType = MediaType.MULTIPART_FORM_DATA
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")
        headers.set("Authorization", "Bearer $jwtToken")

        val body: MultiValueMap<String, Any> = LinkedMultiValueMap()
        body.add("avatar", avatar!!.name)
        body.add("investment", "2000")
        body.add("fee", "20%")
        body.add("agreementType", AgreementType.SAFEHERON_ACQUISITION.name)
        logger.info { "Proposing agreement $body" }
        val requestEntity = HttpEntity<MultiValueMap<String, Any>>(body, headers)
        val result = restTemplate.postForEntity("/api/v1/adviser/agreements", requestEntity, AgreementDto::class.java)
        assertThat(result.statusCode).isEqualTo(HttpStatus.OK)
    }

    @Test
    fun whenListAgreements_thenShouldReturnListOfAgreements() {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")

        headers.set("Authorization", "Bearer $jwtToken")
        val request = HttpEntity<Void>(headers)
        var result = restTemplate.exchange(
            "/api/v1/adviser/agreements", HttpMethod.GET, request, List::class.java
        )
        assertThat(result.statusCode).isEqualTo(HttpStatus.OK)
        val agreementListFirstRequestSize = result.body!!.size
        propose()
        result = restTemplate.exchange(
            "/api/v1/adviser/agreements", HttpMethod.GET, request, List::class.java
        )
        val agreementListSecondRequestSize = result.body!!.size
        assertThat(result.statusCode).isEqualTo(HttpStatus.OK)
        assertThat(agreementListFirstRequestSize + 1).isEqualTo(agreementListSecondRequestSize)
    }

    @Test
    fun whenGetAgreement_thenShouldReturnAgreement() {
        val agreementId = propose()
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")

        headers.set("Authorization", "Bearer $jwtToken")
        val request = HttpEntity<Void>(headers)
        val result = restTemplate.exchange(
            "/api/v1/adviser/agreements/${AgreementType.SAFEHERON_ACQUISITION.name}/$agreementId", HttpMethod.GET, request, AgreementDto::class.java
        )
        assertThat(result.statusCode).isEqualTo(HttpStatus.OK)
    }

    @Test
    fun whenGetNotExistingAgreement_thenShouldReturn404() {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")

        headers.set("Authorization", "Bearer $jwtToken")
        val request = HttpEntity<Void>(headers)
        val result = restTemplate.exchange(
            "/api/v1/adviser/agreements/ASDASD", HttpMethod.GET, request, Object::class.java
        )
        assertThat(result.statusCode).isEqualTo(HttpStatus.NOT_FOUND)
    }

    @Test
    fun whenProposeAcquisitionAgreement_thenShouldReturnStatus200AndChangeState() {
        val newAvatarName = avatar!!.name + "zxc"
        val proposeAgreementRequest =
            ProposeAgreementRequest(newAvatarName, "2000", "20%", AgreementType.NODE_ACQUISITION, sig = null)
        val jwtToken = jwtService.generateToken(1, "AdviserA", "ADVISER")

        every {
            portalService.portalURL
        }  answers { callOriginal() }
        every {
            portalService.notifyOfAcquisition(
                any(),
                newAvatarName
            )
        } returns NotifyPortalAvatarResponse(
            id = 4,
            route = "polity-cabinet.quantumobile.com",
            role = "AVATAR",
            username = newAvatarName,
            photo = "/uploads/69691C7BDCC3CE6D5D8A1361F22D04AC.jpg",
            hasAcquiredNode = true
        )

        val headers = HttpHeaders()

        headers.contentType = MediaType.MULTIPART_FORM_DATA
        headers.set("Authorization", "Bearer $jwtToken")
        val body: MultiValueMap<String, Any> = LinkedMultiValueMap()
        body.add("avatar", newAvatarName)
        body.add("fee", "200000")
        body.add("agreementType", AgreementType.NODE_ACQUISITION.name)
        body.add("pdfFile", getPdfFile())
        val requestEntity = HttpEntity<MultiValueMap<String, Any>>(body, headers)
        val result = restTemplate.postForEntity("/api/v1/adviser/agreements", requestEntity, AgreementDto::class.java)
        assertThat(result.statusCode).isEqualTo(HttpStatus.OK)
        assertThat(agreementService.getAgreements().size).isEqualTo(1)
    }

    private fun getPdfFile(): FileSystemResource {
        return FileSystemResource("src/main/resources/lorem-ipsum.pdf")
    }
}

