package com.dcs.userinfrastructure.integration

import com.dcs.userinfrastructure.UserInfrastructureApplication
import com.dcs.userinfrastructure.dto.notifications.NotificationRequest
import com.dcs.userinfrastructure.dto.notifications.NotificationsMetadata
import com.dcs.userinfrastructure.model.Avatar
import com.dcs.userinfrastructure.model.Node
import com.dcs.userinfrastructure.model.notification.NotificationType
import com.dcs.userinfrastructure.repository.NodeRepository
import com.dcs.userinfrastructure.repository.NotificationRepository
import com.dcs.userinfrastructure.service.AvatarService
import com.dcs.userinfrastructure.service.JwtService
import com.dcs.userinfrastructure.service.NotificationService
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import liquibase.exception.DatabaseException
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.jdbc.JdbcTestUtils
import java.util.*

@ActiveProfiles("test")
@SpringBootTest(
    classes = arrayOf(UserInfrastructureApplication::class),
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
class NotificationIntegrationTest {

    val mapper = jacksonObjectMapper()

    val date = Date(System.currentTimeMillis())

    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    lateinit var jwtService: JwtService

    @Autowired
    lateinit var avatarService: AvatarService

    @Autowired
    lateinit var notificationService: NotificationService

    @Autowired
    lateinit var notificationRepository: NotificationRepository

    @Autowired
    lateinit var restTemplate: TestRestTemplate

    @Autowired
    lateinit var nodeRepository: NodeRepository


    @Value("\${corda.url}")
    val cordaUrl: String = ""

    var node: Node? = null
    var avatar: Avatar? = null


    var notificationRequest: NotificationRequest<SamplePayload, NotificationsMetadata>? = null

    data class SamplePayload(
        val agreementId: String,
        val avatarName: String
    )

    @BeforeEach
    @Throws(DatabaseException::class)
    fun setUp() {
        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "notifications", "agreement", "avatar", "node")
        node = nodeRepository.save(
            Node(
                commonName = "AvatarA",
                organizationUnit = "INC",
                locality = "London",
                country = "GB",
                url = "$cordaUrl:12119"
            )
        )
        avatar = avatarService.save(
            Avatar(
                name = "AvatarA",
                node = node!!,
                photoURL = ""
            )
        )
        notificationRequest = NotificationRequest(
            NotificationType.SAFEHERON_ACQUISITION,
            UUID.randomUUID(),
            SamplePayload("123", "AvatarA"),
            NotificationsMetadata(),
        )
    }

    @Test
    fun whenPostNotification_thenReturn200AndSaveIt() {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val request = HttpEntity(mapper.writeValueAsString(notificationRequest), headers)
        val result = restTemplate.postForEntity(
            "/api/v1/notifications/webhook",
            request,
            Map::class.java
        )
        val notifications = notificationRepository.findAll()
        assertThat(result.statusCodeValue).isEqualTo(200)
        assertThat(notifications.size).isEqualTo(1)
    }

//    @Test
//    fun whenPostNotification_thenGetThemByDateAndJWTRole() {
//        val postHeaders = HttpHeaders()
//        postHeaders.contentType = MediaType.APPLICATION_JSON
//        restTemplate.postForEntity(
//            "/api/notifications/webhook",
//            HttpEntity(mapper.writeValueAsString(notificationRequest), postHeaders),
//            Map::class.java
//        )
//
//
//        val headers = HttpHeaders()
//        val jwtToken = jwtService.generateToken(1, "ava", "AVATAR", avatar.commonName)
//        headers.set("Authorization", "Bearer $jwtToken")
//        var request = HttpEntity<Void>(headers)
//        var result = restTemplate.exchange(
//            "/api/notifications", HttpMethod.GET, request, List::class.java
//        )
//        val notifications = notificationRepository.findAll()
//        Assertions.assertEquals(notificationRequest.recipients.size, notifications)
//    }
}