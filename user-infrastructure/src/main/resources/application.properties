spring.datasource.url=jdbc:postgresql://${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
spring.datasource.username=${POSTGRES_USER}
spring.datasource.password=${POSTGRES_PASSWORD}
spring.datasource.driver=org.postgresql.Driver

key.signature.change.flag=${KEY_SIGNATURE_CHANGE_FLAG:false}

spring.liquibase.change-log=classpath:db/changelog-master.xml
spring.liquibase.enabled=true
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=update

spring.datasource.hikari.data-source-properties.stringtype=unspecified
spring.datasource.tomcat.connection-properties.stringtype=unspecified

spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

server.error.include-message=always
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
springdoc.swagger-ui.path=/swagger-ui.html
jwt.refreshExpirationMs=86400000
jwt.expirationMs=3600000

#rework those to env later

portal.url=${APPLICATION_HOST:https://polity-stage.quantumobile.com}
adviser.commonName=OU=LLC,\ O=AdvisorA,\ L=Los\ Angeles,\ C=US
adviser.url=${ADVISER_URL}
platform.url=${PLATFORM_URL}
corda.url=${CORDA_URL}
corda.username=bastionuser
corda.password=bpassword