package com.dcs.userinfrastructure

import com.dcs.userinfrastructure.model.Node
import com.dcs.userinfrastructure.repository.NodeRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.CommandLineRunner
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.sql.Date
import java.time.Instant

@Profile("default")
@Component
class DemoData : CommandLineRunner {
    @Value("\${corda.url}")
    val cordaUrl: String = "bastionuser"


    @Autowired
    var nodeRepository: NodeRepository? = null


    override fun run(vararg args: String?) {
        if (nodeRepository!!.count() == 0L) {
            val avatarNodeNamesToPorts = mapOf(
                "AvatarB" to 12122,
                "AvatarA" to 12119
            )

            val avatarNodes = avatarNodeNamesToPorts.entries.map {
                Node(
                    commonName = it.key,
                    organizationUnit = "INC",
                    locality = "London",
                    country = "GB",
                    url = "$cordaUrl:${it.value}",
                    acquiredDate = Date.from(Instant.now())
                )
            }
            nodeRepository!!.saveAll(avatarNodes)
        }
    }
}
