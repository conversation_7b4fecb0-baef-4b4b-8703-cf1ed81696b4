package com.dcs.userinfrastructure.repository

import com.dcs.userinfrastructure.model.notification.NotificationEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Repository
interface NotificationRepository : JpaRepository<NotificationEntity?, Long?> {

    fun findAllByUuidAndIsReadFalseAndSentAtBetween(
        uuid: UUID, from: LocalDateTime?, to: LocalDateTime?
    ): List<NotificationEntity>

    fun findAllByUuid(uuid: UUID): List<NotificationEntity>

    fun findAllByUuidAndSentAtBetween(uuid: UUID, from: LocalDateTime?, to: LocalDateTime?): List<NotificationEntity>

    fun findAllByUuidAndIsReadTrue(uuid: UUID): List<NotificationEntity>
    fun findAllByUuidAndIsReadTrueAndSentAtBetween(
        uuid: UUID, from: LocalDateTime?, to: LocalDateTime?
    ): List<NotificationEntity>
    fun findAllByUuidAndIsReadFalse(uuid: UUID): List<NotificationEntity>
   }