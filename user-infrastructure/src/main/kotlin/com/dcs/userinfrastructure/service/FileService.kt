package com.dcs.userinfrastructure.service

import liquibase.util.file.FilenameUtils
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.core.io.UrlResource
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.util.FileSystemUtils
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.IOException
import java.net.MalformedURLException
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.security.MessageDigest
import java.util.*
import java.util.stream.Stream
import javax.xml.bind.DatatypeConverter

@Service
class FileService {
    private val root: Path = Paths.get("uploads")

    init {
        try {
            if (!Files.exists(root)) Files.createDirectory(root)
        } catch (e: IOException) {
            throw RuntimeException("Could not initialize folder for upload!")
        }
    }

    fun save(file: MultipartFile): String {
        try {
            val filename = "${UUID.randomUUID()}-${file.originalFilename?.replace(" ","")}"
            Files.copy(file.inputStream, this.root.resolve(filename))
            return this.root.resolve(filename).toString()
        } catch (e: Exception) {
            throw RuntimeException("Could not store the file. Error: " + e.message)
        }
    }




    fun load(filename: String?): Resource {
        return try {
            val file: Path = root.resolve(filename)
            val resource: Resource = UrlResource(file.toUri())
            if (resource.exists() || resource.isReadable()) {
                resource
            } else {
                throw RuntimeException("Could not read the file!")
            }
        } catch (e: MalformedURLException) {
            throw RuntimeException("Error: " + e.message)
        }
    }

    fun deleteAll() {
        FileSystemUtils.deleteRecursively(root.toFile())
    }

    fun loadAll(): Stream<Path> {
        return try {
            Files.walk(this.root, 1).filter { path -> !path.equals(this.root) }.map(this.root::relativize)
        } catch (e: IOException) {
            throw RuntimeException("Could not load the files!")
        }
    }

    fun generatePdf(): String {
        val file = FileSystemResource("src/main/resources/lorem-ipsum.pdf")
        val filename = "${UUID.randomUUID()}-${file.filename.replace(" ","")}"
        try {
            Files.copy(file.inputStream, this.root.resolve(filename))
        } catch (e: Exception) {
            throw RuntimeException("Could not store the file. Error: " + e.message)
        }
        return filename
    }
}
