package com.dcs.userinfrastructure.service

import com.dcs.userinfrastructure.dto.Notification
import com.dcs.userinfrastructure.model.notification.NotificationEntity
import com.dcs.userinfrastructure.model.notification.NotificationType
import com.dcs.userinfrastructure.repository.NotificationRepository
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.Gson
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import com.dcs.userinfrastructure.declarations.toMap

@Service
class NotificationServiceImpl(
    private val notificationRepository: NotificationRepository,
    private val objectMapper: ObjectMapper
) : NotificationService {

    var gson = Gson()
    override fun <T : Any, U : Any> createNotification(
        uuid: UUID, notificationType: NotificationType, payload: T, meta: U
    ): Notification {
        val notificationEntity = NotificationEntity(
            uuid = uuid,
            notificationType = notificationType,
            payload = payload.toMap(),
            meta = meta.toMap(),
            sentAt = null
        )

        notificationRepository.save(notificationEntity)

        return notificationEntity.toNotification()
    }

    @Transactional
    override fun getNotificationsByCustomer(
        uuid: UUID, sentDate: LocalDate?, isRead: Boolean?
    ): List<Notification> {

        when (isRead) {
            true -> {
                return if (sentDate == null) {
                    notificationRepository.findAllByUuidAndIsReadTrue(uuid).map { it.toNotification() }
                } else {
                    notificationRepository.findAllByUuidAndIsReadTrueAndSentAtBetween(
                        uuid, sentDate.atStartOfDay(), sentDate.plusDays(1).atStartOfDay()
                    ).map { it.toNotification() }
                }
            }

            false -> {
                return if (sentDate == null) {
                    notificationRepository.findAllByUuidAndIsReadFalse(uuid).map { it.toNotification() }
                } else {
                    notificationRepository.findAllByUuidAndIsReadFalseAndSentAtBetween(
                        uuid, sentDate.atStartOfDay(), sentDate.plusDays(1).atStartOfDay()
                    ).map { it.toNotification() }
                }
            }

            null -> {
                return if (sentDate == null) {
                    val rrr = notificationRepository.findAllByUuid(uuid).map { it.toNotification() }
                    println()
                    println(uuid.toString())
                    println(rrr)
                    println()
                    rrr
                } else {
                    notificationRepository.findAllByUuidAndSentAtBetween(
                        uuid, sentDate.atStartOfDay(), sentDate.plusDays(1).atStartOfDay()
                    ).map { it.toNotification() }
                }
            }
        }
    }


    override fun getNotificationById(id: Long): Notification {
        return notificationRepository.getById(id).toNotification()
    }

    override fun readNotification(uuid: UUID, listOfIds: List<Long>): List<Notification>? {
        val unreadNotifications = notificationRepository.findAllById(listOfIds)
        val result = unreadNotifications.filter { !it!!.isRead }.let {
            it.map { notificationEntity ->
                notificationEntity!!.sentAt = LocalDateTime.now()
                notificationEntity.isRead = true
            }
            notificationRepository.saveAll(it)
        }


        return result.map { it!!.toNotification() }
    }

    private fun Notification.toNotificationRead(): Notification {
        return Notification(
            id = id,
            uuid = uuid,
            sentAt = sentAt,
            createdAt = createdAt,
            notificationType = notificationType,
            isRead = true,
            payload = payload,
            meta = meta
        )
    }

    private fun NotificationEntity.toNotification(): Notification {
        return Notification(
            id = id!!,
            uuid = uuid,
            sentAt = sentAt,
            createdAt = createdAt,
            notificationType = notificationType,
            isRead = isRead,
            payload = payload,
            meta = meta
        )
    }
}