package com.dcs.userinfrastructure.controller

import com.dcs.userinfrastructure.dto.KeyExchange
import com.dcs.userinfrastructure.dto.Notification
import com.dcs.userinfrastructure.dto.agreement.AgreementType
import com.dcs.userinfrastructure.model.notification.NotificationType
import com.dcs.userinfrastructure.service.JwtService
import com.dcs.userinfrastructure.service.KeyExchangeService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController
@RequestMapping("/api/v1/key-exchange")
class KeyExchangeController(
    private val jwtService: JwtService,
    private val keyExchangeService: KeyExchangeService
) {

    @PostMapping
    fun savePublicKey(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody pubKey: PubKey
    ): ResponseEntity<KeyExchange> {

        val decodedJwt = jwtService.getAllClaimsFromToken(authHeader.substring(7))
        val customerId = decodedJwt["uuid"] as String

        val result = keyExchangeService.savePublicKey(UUID.fromString(customerId), pubKey.pubKey)

        return ResponseEntity.ok(result)
    }

    class PubKey(
        val pubKey: String
    )
}