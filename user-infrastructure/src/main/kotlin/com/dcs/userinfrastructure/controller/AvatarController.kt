package com.dcs.userinfrastructure.controller

import com.dcs.userinfrastructure.dto.agreement.AgreementDto
import com.dcs.userinfrastructure.dto.agreement.AgreementType
import com.dcs.userinfrastructure.dto.agreement.AutoSignAgreementRequest
import com.dcs.userinfrastructure.dto.avatar.AcceptAgreementRequest
import com.dcs.userinfrastructure.dto.avatar.AcceptDeclineAgreementResponse
import com.dcs.userinfrastructure.dto.avatar.CryptoVerifyAcceptAgreementRequest
import com.dcs.userinfrastructure.dto.avatar.DeclineAgreementRequest
import com.dcs.userinfrastructure.dto.notifications.DFNSAcquisitionNotification
import com.dcs.userinfrastructure.dto.notifications.NotificationsMetadata
import com.dcs.userinfrastructure.dto.notifications.SafeheronAcquisitionNotification
import com.dcs.userinfrastructure.exception.AgreementNotFound
import com.dcs.userinfrastructure.exception.SignatureVerificationException
import com.dcs.userinfrastructure.exception.VerifyFailedException
import com.dcs.userinfrastructure.exception.flow.ConnectionFlowException
import com.dcs.userinfrastructure.exception.avatar.AvatarNotFoundException
import com.dcs.userinfrastructure.exception.flow.FailedFlowException
import com.dcs.userinfrastructure.model.notification.NotificationType
import com.dcs.userinfrastructure.service.*
import io.github.oshai.kotlinlogging.KotlinLogging
import net.corda.c5bastion.statuses.Status
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.*
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.util.*
import javax.validation.Valid


@RestController
@RequestMapping("/api/v1/avatar/agreements")
class AvatarController(
    private val avatarService: AvatarService,
    private val agreementService: AgreementService,
    private val jwtService: JwtService,
    private val fileService: FileService,
    private val keyExchangeService: KeyExchangeService,
    private val notificationService: NotificationService,
) {

    private val logger = KotlinLogging.logger {}

    @Value("\${adviser.commonName}")
    val adviserX500 = ""

    val checkSignature: String? = System.getenv("KEY_SIGNATURE_CHANGE_FLAG")

    @PostMapping("/decline")
    @Deprecated("Use /api/v1/avatar/agreements/auto-sign instead")
    @ResponseBody
    fun declineAgreement(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody declineAgreementRequest: DeclineAgreementRequest
    ): ResponseEntity<AcceptDeclineAgreementResponse>? {
        val jwt = authHeader.substring(7)
        val profileName = jwtService.getProfileNameFromToken(jwt)
        val avatar = avatarService.getAvatarByName(profileName)

        val status = try {
            agreementService.declineAgreement(
                declineAgreementRequest.agreementId,
                declineAgreementRequest.declineReason,
                avatar
            )
        } catch (e: ConnectionFlowException) {
            logger.error { "Flow was unable to be executed due to connection problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.SERVICE_UNAVAILABLE, e.message, e
            )
        } catch (e: FailedFlowException) {
            logger.error { "Flow was unable to be executed due to internal flow problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, e.message, e
            )
        } catch (e: Exception) {
            logger.error { "Unknown problem encountered while declining agreement: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR, e.message, e
            )
        }
        return ResponseEntity(
            AcceptDeclineAgreementResponse(
                declineAgreementRequest.agreementId,
                status
            ),
            HttpStatus.OK
        )
    }

    @RequestMapping("/auto-sign", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE], method = [RequestMethod.POST])
    @ResponseBody
    fun automaticallySignAgreement(
        @RequestHeader(name = "Authorization") authHeader: String,
        @Valid @ModelAttribute autoSignAgreementRequest: AutoSignAgreementRequest
    ): ResponseEntity<AgreementDto> {
        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getAllClaimsFromToken(jwt)
        val customerId = decodedJwt["uuid"] as String

        val profileName = jwtService.getProfileNameFromToken(jwt)
        val avatar = avatarService.getAvatarByName(profileName)

        val agreement = when (autoSignAgreementRequest.agreementType) {
            AgreementType.SAFEHERON_ACQUISITION, AgreementType.DFNS_ACQUISITION -> {
                val pdfFile = autoSignAgreementRequest.pdfFile?.let {
                    fileService.save(it)
                } ?: fileService.generatePdf()
                try {
                    val agreement = agreementService.proposeAgreementToBlockchain(
                        avatar,
                        autoSignAgreementRequest.agreementType,
                        pdfFile,
                    )
                    if (autoSignAgreementRequest.agreementType == AgreementType.SAFEHERON_ACQUISITION) {
                        val payload = SafeheronAcquisitionNotification(
                            issuer = adviserX500,
                            agreementId = agreement.agreementID,
                            status = Status.SIGNED.toString(),
                            statusPrev = Status.PROPOSED.toString(),
                            recipients = listOf(avatar.name, adviserX500),
                            declinedBy = null,
                            declineReason = null
                        )

                        val meta = NotificationsMetadata()

                        notificationService.createNotification(
                            UUID.fromString(customerId),
                            NotificationType.SAFEHERON_ACQUISITION,
                            payload,
                            meta
                        )
                    } else {
                        val payload = DFNSAcquisitionNotification(
                            issuer = adviserX500,
                            agreementId = agreement.agreementID,
                            status = Status.SIGNED.toString(),
                            statusPrev = Status.PROPOSED.toString(),
                            recipients = listOf(avatar.name, adviserX500),
                            declinedBy = null,
                            declineReason = null
                        )

                        val meta = NotificationsMetadata()

                        notificationService.createNotification(
                            UUID.fromString(customerId),
                            NotificationType.DFNS_ACQUISITION,
                            payload,
                            meta
                        )
                    }
                    agreement
                } catch (e: ConnectionFlowException) {
                    throw ResponseStatusException(
                        HttpStatus.SERVICE_UNAVAILABLE, e.message, e
                    )
                } catch (e: FailedFlowException) {
                    throw ResponseStatusException(
                        HttpStatus.BAD_REQUEST, e.message, e
                    )
                }
            }
            else -> {
                throw ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                    "Invalid agreement type ${autoSignAgreementRequest.agreementType} provided to auto-sign agreement"
                )
            }
        }
        val status = try {
            agreement.run {
                agreementService.acceptAgreement(agreementID, avatar)
                agreementService.verifyAgreement(agreementID)
                agreementService.notarizeAgreement(agreementID)
                agreementService.completeAgreement(agreementID)
            }
        } catch (e: ConnectionFlowException) {
            logger.error { "Flow was unable to be executed due to connection problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.SERVICE_UNAVAILABLE, e.message, e
            )
        } catch (e: FailedFlowException) {
            logger.error { "Flow was unable to be executed due to internal flow problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, e.message, e
            )
        } catch (e: VerifyFailedException) {
            logger.error { "Flow was unable to be executed due to verification problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.BAD_REQUEST, e.message, e
            )
        } catch (e: Exception) {
            logger.error { "Unknown problem encountered while auto-signing agreement: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR, e.message, e
            )
        }

        return ResponseEntity(
            agreement.run {
                copy(status = status)
            },
            HttpStatus.OK
        )
    }

    @PostMapping("/accept")
    @Deprecated("Use /api/v1/avatar/agreements/auto-sign instead")
    @ResponseBody
    fun acceptAgreement(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody acceptAgreementRequest: AcceptAgreementRequest
    ): ResponseEntity<*>? {
        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getAllClaimsFromToken(jwt)
        val customerId = decodedJwt["uuid"] as String

        if (checkSignature.toBoolean()) {
            val data = CryptoVerifyAcceptAgreementRequest(agreementId = acceptAgreementRequest.agreementId)

            val isValidSig = keyExchangeService.verifySignature(
                customerId,
                acceptAgreementRequest.sig ?: "",
                data
            )

            if (!isValidSig) throw SignatureVerificationException("Data verification failed")
        }

        val profileName = jwtService.getProfileNameFromToken(jwt)
        val avatar = try {
            avatarService.getAvatarByName(profileName)
        } catch (e: AvatarNotFoundException) {
            logger.error { "Avatar not found: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, "Avatar Not Found", e
            )
        }

        val status = try {
            val res = agreementService.acceptAgreement(acceptAgreementRequest.agreementId, avatar)
            agreementService.verifyAgreement(acceptAgreementRequest.agreementId)
            agreementService.notarizeAgreement(acceptAgreementRequest.agreementId)
            agreementService.completeAgreement(acceptAgreementRequest.agreementId)
            res
        } catch (e: ConnectionFlowException) {
            logger.error { "Flow was unable to be executed due to connection problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.SERVICE_UNAVAILABLE, e.message, e
            )
        } catch (e: FailedFlowException) {
            logger.error { "Flow was unable to be executed due to internal flow problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, e.message, e
            )
        } catch (e: VerifyFailedException) {
            logger.error { "Flow was unable to be executed due to verification problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.BAD_REQUEST, e.message, e
            )
        } catch (e: Exception) {
            logger.error { "Unknown problem encountered while processing agreement proposing: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR, e.message, e
            )
        }

        return ResponseEntity(
            AcceptDeclineAgreementResponse(
                acceptAgreementRequest.agreementId,
                status
            ),
            HttpStatus.OK
        )
    }


    @GetMapping
    @ResponseBody
    fun listAgreements(
        @RequestHeader(name = "Authorization") authHeader: String,
    ): ResponseEntity<List<AgreementDto>>? {
        val jwt = authHeader.substring(7)
        val profileName = jwtService.getProfileNameFromToken(jwt)
        val avatar = try {
            avatarService.getAvatarByName(profileName)
        } catch (e: AvatarNotFoundException) {
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, "Avatar Not Found", e
            )
        }

        val agreements = try {
            agreementService.listAgreements(avatar)
        } catch (e: ConnectionFlowException) {
            logger.error { "Flow was unable to be executed due to connection problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.SERVICE_UNAVAILABLE, e.message, e
            )
        } catch (e: FailedFlowException) {
            logger.error { "Flow was unable to be executed due to internal flow problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, e.message, e
            )
        } catch (e: Exception) {
            logger.error { "Unknown problem encountered while processing list agreements: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR, e.message, e
            )
        }
        return ResponseEntity(
            agreements,
            HttpStatus.OK
        )
    }

    @GetMapping("{type}/{id}")
    @ResponseBody
    fun getAgreement(
        @PathVariable(value = "id") agreementId: String,
        @PathVariable(value = "type") agreementType: AgreementType,
        @RequestHeader(name = "Authorization") authHeader: String,
    ): ResponseEntity<AgreementDto>? {
        val jwt = authHeader.substring(7)
        val profileName = jwtService.getProfileNameFromToken(jwt)
        val avatar = try {
            avatarService.getAvatarByName(profileName)
        } catch (e: AvatarNotFoundException) {
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, "Avatar Not Found", e
            )
        }

        val agreement = try {
            when (agreementType) {
                AgreementType.DFNS_ACQUISITION -> agreementService.getAgreementFromBlockchain(avatar.node.url, agreementId)
                AgreementType.SAFEHERON_ACQUISITION -> agreementService.getAgreementFromBlockchain(avatar.node.url, agreementId)
                AgreementType.NODE_ACQUISITION -> agreementService.getAgreementFromDB(agreementId.toLong())
            }
        } catch (e: ConnectionFlowException) {
            logger.error { "Flow was unable to be executed due to connection problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.SERVICE_UNAVAILABLE, e.message, e
            )
        } catch (e: FailedFlowException) {
            logger.error { "Flow was unable to be executed due to internal flow problem: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.BAD_REQUEST, e.message, e
            )
        } catch (e: AgreementNotFound) {
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND, e.message, e
            )
        } catch (e: Exception) {
            logger.error { "Unknown problem encountered while getting agreement: ${e.message}" }
            throw ResponseStatusException(
                HttpStatus.INTERNAL_SERVER_ERROR, e.message, e
            )
        }
        return ResponseEntity(
            agreement,
            HttpStatus.OK
        )
    }

}