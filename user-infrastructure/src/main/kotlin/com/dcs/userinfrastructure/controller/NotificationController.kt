package com.dcs.userinfrastructure.controller

import com.dcs.userinfrastructure.dto.Notification
import com.dcs.userinfrastructure.dto.notifications.NotificationRequest
import com.dcs.userinfrastructure.service.AvatarService
import com.dcs.userinfrastructure.service.JwtService
import com.dcs.userinfrastructure.service.NotificationService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.util.*


@RestController
@RequestMapping("/api/v1/notifications")
class NotificationController(
    private val notificationService: NotificationService,
    private val jwtService: JwtService
) {

    @Autowired
    var avatarService: AvatarService? = null

    private val logger: Logger = LoggerFactory.getLogger(NotificationController::class.java)

    @Value("\${adviser.commonName}")
    val adviserX500 = ""


    @PostMapping("/webhook")
    fun <T: Any, U: Any> postNotification(
        @RequestBody notificationRequest: NotificationRequest<T, U>
    ): ResponseEntity<Notification> {

        val result = notificationService.createNotification(
            notificationRequest.uuid,
            notificationRequest.notificationType,
            notificationRequest.payload,
            notificationRequest.meta
        )

        return ResponseEntity.ok(result)
    }

    @PostMapping()
    fun readNotifications(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody listOfIds: List<Long>
    ): ResponseEntity<List<Notification>> {

        val decodedJwt = jwtService.getAllClaimsFromToken(authHeader.substring(7))
        val uuid = decodedJwt["uuid"] as String

        val result = notificationService.readNotification(
            UUID.fromString(uuid),
            listOfIds
        )

        return ResponseEntity.ok(result)
    }

    @GetMapping()
    fun getNotificationsByCustomer(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd") date: LocalDate?,
        @RequestParam isRead: Boolean?
    ): List<Notification> {
        val decodedJwt = jwtService.getAllClaimsFromToken(authHeader.substring(7))
        val uuid = decodedJwt["uuid"] as String

        return notificationService.getNotificationsByCustomer(
            UUID.fromString(uuid), date, isRead
        )

    }

    @GetMapping("/{id}")
    fun getNotificationById(
        @RequestHeader(name = "Authorization") authHeader: String,
        @PathVariable id: String
    ): Notification {
        return notificationService.getNotificationById(id.toLong())
    }


}