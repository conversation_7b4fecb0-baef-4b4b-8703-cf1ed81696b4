package com.dcs.userinfrastructure.model.notification

import com.fasterxml.jackson.annotation.JsonFormat
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import com.vladmihalcea.hibernate.type.json.JsonStringType
import org.hibernate.annotations.Type
import org.hibernate.annotations.TypeDef
import org.hibernate.annotations.TypeDefs
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import javax.persistence.*


//Notification {
// issuer: string
// recipients: List[string]
//  date: string /
// / ISO agreement_id: number
// agreement_type: string
// enum: Investment / Node Acquisition / ...
// status: string // current status
// status_prev: string
// these only populated if contract status is 'declined'
// decline_reason: string
// decline_by: string //
// avatar /
// platform //
// comment/title/smth like this? }

@Entity
@Table(name = "notifications")
@TypeDefs(
    TypeDef(name = "json", typeClass = JsonStringType::class),
    TypeDef(name = "jsonb", typeClass = JsonBinaryType::class)
)
class NotificationEntity(

    @Column(name = "uuid")
    var uuid: UUID,

    @Column(name = "notification_type")
    @Enumerated(EnumType.STRING)
    var notificationType: NotificationType,

    @Column(name = "is_read")
    var isRead: Boolean = false,

    @Column(name = "sent_at")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    var sentAt: LocalDateTime?,

    @Column(name = "created_at")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    var id: Long? = null,

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    var payload: Map<String, Any?>,

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    var meta: Map<String, Any?>
)