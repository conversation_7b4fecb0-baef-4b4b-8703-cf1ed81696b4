package com.dcs.userinfrastructure.model

import java.time.LocalDate
import javax.persistence.*


@Entity
@Table(name = "agreement", uniqueConstraints = [UniqueConstraint(columnNames = ["avatar_id"])])
open class Agreement(
    @Column(name = "agreement_file")
    open val agreementFile: String,

    @Column(name = "agreement_hash")
    open val agreementHash: String,

    @Column(name = "fee")
    open val fee: String,

    @ManyToOne
    @JoinColumn(name = "avatar_id")
    open val avatar: Avatar,

    @Column(name = "agreement_date", nullable = true)
    open val agreementDate: LocalDate?,
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    open var id: Long? = null,
)