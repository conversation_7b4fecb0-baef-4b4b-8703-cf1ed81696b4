package com.dcs.userinfrastructure.model

import java.util.UUID
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.Id
import javax.persistence.Table

@Entity
@Table(name = "key_exchange")
class KeyExchangeEntity(
    @Id
    @Column(name = "customer_id", nullable = false)
    var customerId: UUID,

    @Column(name = "pub_key", nullable = false)
    var pubKey: String
)