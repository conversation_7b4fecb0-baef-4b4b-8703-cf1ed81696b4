package com.dcs.userinfrastructure.model

import java.util.*
import javax.persistence.*


@Entity
@Table(name = "node", uniqueConstraints = [UniqueConstraint(columnNames = ["url"])])
open class Node(
    @Column(name = "url", nullable = false, unique = true)
    open var url: String,

    @Column(name = "common_name", nullable = false, unique = true)
    open var commonName: String,


//    @OneToMany(cascade = [(CascadeType.ALL)], fetch = FetchType.EAGER, mappedBy = "avatar")
//    open var avatars: MutableList<Avatar> = mutableListOf(),


    @Column(nullable = false)
    open var locality: String,


    @Column(nullable = false)
    open var country: String,


    @Column(name = "organization_unit", nullable = true)
    open var organizationUnit: String?,

    @Column(name = "acquiredDate")
    open var acquiredDate: Date? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    open var id: Long? = null,
) {
    //    "OU=INC, O=AvatarA, L=London, C=GB"
    fun toX500(): String {
        var x500 = "O=$commonName, L=$locality, C=$country"
        if (organizationUnit != null) x500 = "OU=$organizationUnit, $x500"
        return x500
    }
}