services:
  user-infrastructure-test:
    #  -DREMOTEDEBUG=true
    command: gradle bootRun -DREMOTEDEBUG=false
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env.example.test
    depends_on:
      - postgres-user-infrastructure-test
    volumes:
      - .build:/project/build/
    ports:
      - 8081:8080
      - 5006:5005


  postgres-user-infrastructure-test:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - .env.example.test
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    expose:
      - 5432
