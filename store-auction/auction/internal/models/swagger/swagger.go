package swagger

import (
	"time"
)

type ActiveLotsAddress struct {
	Address string      `json:"websocket_address"`
	Lots    []LotUpdate `json:"lots_state"`
}

type LotUpdate struct {
	ID         string `json:"id"`
	Price      int    `json:"price"`
	WinningBid bool   `json:"winning_bid"`
	TimeLeft   int64  `json:"time_left"`
}

type LotsStateResponse struct {
	Type string      `json:"type"`
	Data []LotUpdate `json:"data"`
}

type WonLot struct {
	Price       int       `json:"price"`
	UserID      string    `json:"user_id"`
	CloseTime   time.Time `json:"close_time"`
	Status      string    `json:"status"`
	AgreementID string    `json:"agreement_id,omitempty"`

	TierID string `json:"tier_id" gorm:"tier_id;size:50"`
	Tier   struct {
		Name            string `json:"tier_name" gorm:"name;size:50"`
		ProductQuantity int    `json:"product_quantity" gorm:"product_quantity;size:50"`
		BuyNowPrice     int    `json:"buy_now_price" gorm:"buy_now_price;size:50"`

		ProductID string `json:"product_id" gorm:"product_id;size:50"`
		Product   struct {
			Category    string `json:"category" gorm:"category;size:50"`
			Merchant    string `json:"merchant" gorm:"merchant;size:100"`
			MinPrice    int    `json:"min_price" gorm:"min_price;size:200"`
			Name        string `json:"name" gorm:"name;size:200"`
			Description string `json:"description" gorm:"description;size:200"`
		}
	}
}
