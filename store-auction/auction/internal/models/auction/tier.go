package auction

import (
	"gorm.io/gorm"
)

type Tier struct {
	gorm.Model
	Name            string `json:"tier_name" gorm:"name;size:50"`
	ProductQuantity int    `json:"product_quantity" gorm:"product_quantity;size:50"`
	BuyNowPrice     int    `json:"buy_now_price" gorm:"buy_now_price;size:50"`

	ProductID string `json:"product_id" gorm:"product_id;size:50"`
	Product   Product

	PermissionID string `json:"permission_id" gorm:"permission_id;size:50"`
	Permission   Permission
}

type GetTierResponse struct {
	ID              string `json:"id"`
	Name            string `json:"tier_name" gorm:"name;size:50"`
	ProductQuantity int    `json:"product_quantity" gorm:"product_quantity;size:50"`
	BuyNowPrice     int    `json:"buy_now_price" gorm:"buy_now_price;size:50"`
	PermissionID    string `json:"permission_id" gorm:"permission_id;size:50"`

	ProductID string `json:"product_id" gorm:"product_id;size:50"`
}
