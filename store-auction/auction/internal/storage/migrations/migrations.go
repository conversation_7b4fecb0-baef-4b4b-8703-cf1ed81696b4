package migrations

import (
	"log"

	"github.com/Polity_MVP/store-auction/auction/internal/models/auction"
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

const permissionsTable = "permissions"

func Migrate(db *gorm.DB) error {
	m := gormigrate.New(db, gormigrate.DefaultOptions, []*gormigrate.Migration{
		{
			ID: "000000000001",
			Migrate: func(tx *gorm.DB) error {
				return tx.AutoMigrate(&auction.Permission{})
			},
			Rollback: func(tx *gorm.DB) error {
				return tx.Migrator().DropTable(permissionsTable)
			},
		},
		{
			ID: "000000000003",
			Migrate: func(tx *gorm.DB) error {
				return tx.AutoMigrate(&auction.Bid{})
			},
			Rollback: func(tx *gorm.DB) error {
				return tx.Migrator().DropTable("bids")
			},
		},
		{
			ID: "000000000004",
			Migrate: func(tx *gorm.DB) error {
				type ActiveLot struct {
					Bids []auction.Bid `gorm:"constraint:OnDelete:CASCADE;"`
				}
				return tx.Migrator().CreateConstraint("bids", "OnDelete")
			},
			Rollback: func(tx *gorm.DB) error {
				type ActiveLot struct {
					Bids []auction.Bid `gorm:"constraint:OnDelete:CASCADE;"`
				}
				return tx.Migrator().DropConstraint("bids", "OnDelete")
			},
		},
		{
			ID: "000000000006",
			Migrate: func(tx *gorm.DB) error {
				type WonLot struct {
					AgreementID string
				}
				log.Printf("Migration 6 successfully finished")
				return tx.Migrator().AddColumn(&WonLot{}, "agreement_id")
			},
			Rollback: func(tx *gorm.DB) error {
				type WonLot struct {
					AgreementID string
				}
				return db.Migrator().DropColumn(&WonLot{}, "agreement_id")
			},
		},
	})
	// Init schema on clean DB
	m.InitSchema(func(tx *gorm.DB) error {
		err := tx.AutoMigrate(
			&auction.Product{},
			&auction.Tier{},
			&auction.ActiveLot{},
			&auction.WonLot{},
			&auction.Bid{},
			&auction.Permission{},
		)
		if err != nil {
			log.Printf("Automigrate failed.. Error: '%v'\n", err)
			return err
		}
		log.Printf("Migration schema successfully finished. ")
		return nil
	})

	if err := m.Migrate(); err != nil {
		log.Fatalf("Could not migrate: %v", err)
	}
	log.Printf("Migration did run successfully")
	return nil
}
