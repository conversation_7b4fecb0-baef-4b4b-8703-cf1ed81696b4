package service

import (
	"context"
	"errors"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/Polity_MVP/store-auction/auction/container"
	"github.com/Polity_MVP/store-auction/auction/internal/models/agreements"
	"github.com/Polity_MVP/store-auction/auction/internal/models/auction"
	"github.com/Polity_MVP/store-auction/auction/internal/models/identity"
	"github.com/Polity_MVP/store-auction/auction/internal/models/notification"
	"github.com/Polity_MVP/store-auction/auction/internal/storage"
	"github.com/Polity_MVP/store-auction/auction/providers/userInfrastructure"
)

const (
	countdownFrontier         = 1 * time.Minute
	timeLeftUpdateFrequency   = 1 * time.Second
	statusWon                 = "won"
	botID                     = "bot"
	maxLotSubscribersQuantity = 100

	categoryWallet                    = "wallet"
	merchantSafeheron                 = "safeheron"
	agreementTypeSafeheronAcquisition = "SAFEHERON_ACQUISITION"

	errLowBid            = "bid is not bigger then current price"
	errCreateActiveLot   = "cant create active lot"
	errProceedExpiredLot = "cant proceed expired lot"
	errActiveLotNotFound = "active lot is not found in map"
	errTierNotFound      = "tier is not found in map"
	errUpdateTimeLeft    = "cant update lot time left"
	cancelStatus         = "cancelled"
)

type AuctionService interface {
	RunActiveLots()
	GetLotsAddress(token, userID, tierID string) (auction.ActiveLotsAddress, error)
	GetTiersByProduct(productID string) ([]auction.GetTierResponse, error)
	GetTierByID(productID, tierID string) (*auction.GetTierResponse, error)
	BuyNow(user identity.Identity, productID, tierID string) (agreements.AgreementID, error)
	GetTiers() ([]auction.Tier, error)
	Subscribe(tierID string) <-chan []auction.ActiveLot
	SubscribeWonLots(tierID string) <-chan auction.UserWonLot
	MakeBid(bid *auction.UserBid) error
	CreateActiveLot(tierID string) error
	GetProducts() ([]auction.GetProductResponse, error)
	GetBids(tierID, lotID string) ([]auction.BidResponse, error)
	CreateAgreement(user identity.Identity, tierID string, wonLot *auction.WonLot) (agreements.AgreementID, error)
	CancelWonLot(agreementID string) (*auction.WonLot, error)
}

type auctionService struct {
	ctx                      context.Context
	wg                       *sync.WaitGroup
	container                container.AuctionContainer
	storage                  *storage.AuctionStorage
	tiersLots                map[string]*tierLots
	tiersLotsMU              *sync.RWMutex
	newLotsQueue             chan string
	userInfrastructureClient userInfrastructure.Client
	tiersWonLots             map[string]*tierWonLots
	tiersWonLotsMU           *sync.RWMutex
}

type tierLots struct {
	lotsMU *sync.RWMutex
	lots   map[string]*lotTimer

	subscribedUsersMU *sync.RWMutex
	subscribedUsers   []chan []auction.ActiveLot
}

type tierWonLots struct {
	subscribedUsersMU *sync.RWMutex
	subscribedUsers   []chan auction.UserWonLot
}

func NewAuctionService(
	ctx context.Context,
	wg *sync.WaitGroup,
	container container.AuctionContainer,
	storage *storage.AuctionStorage,
	userInfrastructureClient userInfrastructure.Client,
) (AuctionService, error) {

	tiersLots := make(map[string]*tierLots)
	tiersLotsMU := &sync.RWMutex{}
	newLotsQueue := make(chan string, 100)

	tiersWonLots := make(map[string]*tierWonLots)
	tiersWonLotsMU := &sync.RWMutex{}

	return &auctionService{
		ctx:                      ctx,
		wg:                       wg,
		container:                container,
		storage:                  storage,
		tiersLots:                tiersLots,
		tiersLotsMU:              tiersLotsMU,
		newLotsQueue:             newLotsQueue,
		userInfrastructureClient: userInfrastructureClient,
		tiersWonLots:             tiersWonLots,
		tiersWonLotsMU:           tiersWonLotsMU,
	}, nil
}

func (a *auctionService) RunActiveLots() {
	go func() {
		a.startLots()
	}()

	for {
		select {
		case <-a.ctx.Done():
			return
		case tierID, ok := <-a.newLotsQueue:
			if !ok {
				break
			}

			tier, err := a.storage.GetTier(tierID)
			if err != nil {
				a.container.GetLogger().With(err).Error("cant get tier to create active lot")
				return
			}

			if tier.ProductQuantity < 1 {
				a.container.GetLogger().Error("products are out of stock")
				return
			}

			lotID, err := a.createActiveLot(tier)
			if err != nil {
				a.container.GetLogger().With(err).Error(errCreateActiveLot)
				return
			}

			a.wg.Add(1)
			go func() {
				defer a.wg.Done()

				lot, err := a.storage.GetActiveLot(lotID)
				if err != nil {
					a.container.GetLogger().With(err).Error("cant get active lot")
					return
				}
				t := a.startActiveLot(fmt.Sprint(tier.ID), lotID, lot.TimeLeft)

			Loop:
				for {
					select {
					case <-a.ctx.Done():
						return
					case <-t.timer.C:
						err := a.storage.UpdateTimeLeft(lot, time.Duration(0))
						if err != nil {
							a.container.GetLogger().With(err).Error(errUpdateTimeLeft)
							return
						}

						err = a.ProcessExpiredLot(fmt.Sprint(tier.ID), lotID)
						if err != nil {
							a.container.GetLogger().With(err).Error(errProceedExpiredLot)
							return
						}

						a.newLotsQueue <- fmt.Sprint(tier.ID)
						break Loop
					default:
						time.Sleep(timeLeftUpdateFrequency)
						err := a.storage.UpdateTimeLeft(lot, t.timeLeft())
						if err != nil {
							a.container.GetLogger().With(err).Error(errUpdateTimeLeft)
							return
						}
					}
				}
			}()
		}
	}
}

func (a *auctionService) startLots() {
	tiers, err := a.GetTiers()
	if err != nil {
		a.container.GetLogger().With(err).Error("cant get tiers to start active lots")
		return
	}

	for _, tier := range tiers {
		tier := tier // https://golang.org/doc/faq#closures_and_goroutines

		a.tiersLotsMU.Lock()
		a.tiersLots[fmt.Sprint(tier.ID)] = &tierLots{
			lotsMU:            &sync.RWMutex{},
			lots:              make(map[string]*lotTimer),
			subscribedUsersMU: &sync.RWMutex{},
			subscribedUsers:   make([]chan []auction.ActiveLot, 0, 1),
		}
		a.tiersLotsMU.Unlock()

		a.tiersWonLotsMU.Lock()
		a.tiersWonLots[fmt.Sprint(tier.ID)] = &tierWonLots{
			subscribedUsersMU: &sync.RWMutex{},
			subscribedUsers:   make([]chan auction.UserWonLot, 0, 1),
		}
		a.tiersWonLotsMU.Unlock()

		lots, err := a.getActiveLots(fmt.Sprint(tier.ID))
		if err != nil {
			a.container.GetLogger().With(err).Error("cant get active lots to start")
			return
		}

		switch len(lots) {
		case 0:
			a.newLotsQueue <- fmt.Sprint(tier.ID) // expected to have at least one lot per tier
		default:
			for _, lot := range lots {
				lot := lot

				a.wg.Add(1)
				go func(lotID string) {
					defer a.wg.Done()
					t := a.startActiveLot(fmt.Sprint(tier.ID), lotID, lot.TimeLeft)
				Loop:
					for {
						select {
						case <-a.ctx.Done():
							return
						case <-t.timer.C:
							err := a.ProcessExpiredLot(fmt.Sprint(tier.ID), lotID)
							if err != nil {
								a.container.GetLogger().With(err).Error(errProceedExpiredLot)
								return
							}
							a.newLotsQueue <- fmt.Sprint(tier.ID)
							break Loop
						default:
							time.Sleep(timeLeftUpdateFrequency)
							err := a.storage.UpdateTimeLeft(&lot, t.timeLeft())
							if err != nil {
								a.container.GetLogger().With(err).Error(errUpdateTimeLeft)
								return
							}
						}
					}
				}(fmt.Sprint(lot.ID))
			}
		}
	}
}

func (a *auctionService) Subscribe(tierID string) <-chan []auction.ActiveLot {
	tierSubscription := make(chan []auction.ActiveLot, maxLotSubscribersQuantity)

	a.tiersLotsMU.RLock()
	defer a.tiersLotsMU.RUnlock()

	a.tiersLots[tierID].subscribedUsersMU.Lock()
	defer a.tiersLots[tierID].subscribedUsersMU.Unlock()

	a.tiersLots[tierID].subscribedUsers = append(a.tiersLots[tierID].subscribedUsers, tierSubscription)

	return tierSubscription
}

func (a *auctionService) SubscribeWonLots(tierID string) <-chan auction.UserWonLot {
	subscription := make(chan auction.UserWonLot, maxLotSubscribersQuantity)

	a.tiersWonLotsMU.RLock()
	defer a.tiersWonLotsMU.RUnlock()

	a.tiersWonLots[tierID].subscribedUsersMU.Lock()
	defer a.tiersWonLots[tierID].subscribedUsersMU.Unlock()

	a.tiersWonLots[tierID].subscribedUsers = append(a.tiersWonLots[tierID].subscribedUsers, subscription)

	return subscription
}

func (a *auctionService) createActiveLot(tier *auction.Tier) (string, error) {
	if tier.ProductQuantity < 1 {
		a.container.GetLogger().Error("products are out of stock")
		return "", errors.New("products are out of stock")
	}

	lotID, err := a.storage.CreateActiveLot(&auction.ActiveLot{
		Price:    tier.Product.MinPrice,
		TimeLeft: time.Duration(a.container.GetConfig().LotExpiration) * time.Second,
		TierID:   fmt.Sprint(tier.ID),
	})
	if err != nil {
		return "", err
	}

	err = a.storage.DecreaseProductQuantity(tier)
	if err != nil {
		return "", err
	}

	return lotID, nil
}

func (a *auctionService) startActiveLot(tierID, lotID string, timeLeft time.Duration) *lotTimer {
	a.tiersLotsMU.RLock()
	defer a.tiersLotsMU.RUnlock()

	tl := a.tiersLots[tierID]

	tl.lotsMU.Lock()
	defer tl.lotsMU.Unlock()

	if timeLeft == 0 {
		tl.lots[lotID] = newLotTimer(time.Duration(a.container.GetConfig().LotExpiration) * time.Second)
	} else {
		tl.lots[lotID] = newLotTimer(timeLeft)
	}

	return a.tiersLots[tierID].lots[lotID]
}

func (a *auctionService) GetLotsAddress(token, userID, tierID string) (auction.ActiveLotsAddress, error) {
	lots, err := a.getActiveLots(tierID)
	if err != nil {
		return auction.ActiveLotsAddress{}, err
	}

	activeLots := make([]auction.LotUpdate, 0, len(lots))
	for _, lot := range lots {
		winningBid := false
		if userID == lot.UserID {
			winningBid = true
		}

		activeLots = append(activeLots, auction.LotUpdate{
			ID:         fmt.Sprint(lot.ID),
			Price:      lot.Price,
			WinningBid: winningBid,
			TimeLeft:   lot.TimeLeft / time.Millisecond,
		})
	}

	return auction.ActiveLotsAddress{
		Address: fmt.Sprintf("ws://%s/api/v1/connect/%s/%s/", net.JoinHostPort(a.container.GetConfig().StoreAuctionHost, a.container.GetConfig().StoreAuctionPort), tierID, token),
		Lots:    activeLots,
	}, nil
}

func (a *auctionService) GetTiersByProduct(productID string) ([]auction.GetTierResponse, error) {
	tiers, err := a.storage.GetTiersByProduct(productID)
	if err != nil {
		return nil, err
	}
	return tiers, nil
}

func (a *auctionService) GetTiers() ([]auction.Tier, error) {
	tiers, err := a.storage.GetTiers()
	if err != nil {
		return nil, err
	}

	return tiers, nil
}

func (a *auctionService) GetTierByID(productID, tierID string) (*auction.GetTierResponse, error) {
	tier, err := a.storage.GetTierById(productID, tierID)
	if err != nil {
		return nil, err
	}
	return tier, nil
}

func (a *auctionService) BuyNow(user identity.Identity, productID, tierID string) (agreements.AgreementID, error) {
	tier, err := a.storage.GetTier(tierID)
	if err != nil {
		return agreements.AgreementID{}, err
	}

	if tier.ProductQuantity < 1 {
		a.container.GetLogger().Error("products are out of stock")
		return agreements.AgreementID{}, errors.New("products are out of stock")
	}

	wonLot, err := a.storage.CreateWonLot(&auction.WonLot{
		Price:     tier.BuyNowPrice,
		UserID:    user.UserID,
		CloseTime: time.Now(),
		Status:    statusWon,
		TierID:    tierID,
	})
	if err != nil {
		return agreements.AgreementID{}, err
	}

	agreement, err := a.CreateAgreement(user, tierID, wonLot)
	if err != nil {
		return agreements.AgreementID{}, err
	}

	payload := notification.WonInAuction{
		UserId:   user.UserID,
		Price:    tier.BuyNowPrice,
		TierName: tier.Name,
	}

	err = a.userInfrastructureClient.SendWonInAuctionNotification(user.UserID, payload)
	if err != nil {
		return agreements.AgreementID{}, err
	}

	err = a.storage.DecreaseProductQuantity(tier)
	if err != nil {
		return agreements.AgreementID{}, err
	}

	return agreement, nil
}

func (a *auctionService) getActiveLots(tierID string) ([]auction.ActiveLot, error) {
	lots, err := a.storage.GetActiveLots(tierID)
	if err != nil {
		return nil, err
	}
	return lots, nil
}

func (a *auctionService) MakeBid(bid *auction.UserBid) error {
	lot, err := a.storage.GetActiveLot(bid.LotID)
	if err != nil {
		return err
	}

	if bid.Price <= lot.Price {
		return errors.New(errLowBid)
	}
	lot.Price = bid.Price

	if lot.TimeLeft < countdownFrontier {
		a.tiersLotsMU.Lock()

		_, ok := a.tiersLots[bid.TierID]
		if !ok {
			a.tiersLotsMU.Unlock()
			return errors.New(errTierNotFound)
		}

		a.tiersLots[bid.TierID].lotsMU.Lock()

		_, ok = a.tiersLots[bid.TierID].lots[bid.LotID]
		if !ok {
			a.tiersLots[bid.TierID].lotsMU.Unlock()
			a.tiersLotsMU.Unlock()
			return errors.New(errActiveLotNotFound)
		}

		a.tiersLots[bid.TierID].lots[bid.LotID].reset(countdownFrontier)

		a.tiersLots[bid.TierID].lotsMU.Unlock()
		a.tiersLotsMU.Unlock()
		lot.TimeLeft = countdownFrontier
	}

	lot.UserID = bid.UserID

	err = a.storage.UpdateActiveLot(lot)
	if err != nil {
		return err
	}

	err = a.storage.CreateBid(auction.Bid{
		Amount:      bid.Price,
		ActiveLotID: bid.LotID,
	})
	if err != nil {
		return err
	}

	err = a.SendUpdateLots(bid.TierID)
	if err != nil {
		return err
	}

	return nil
}

func (a *auctionService) SendUpdateLots(tierID string) error {
	lots, err := a.storage.GetActiveLots(tierID)
	if err != nil {
		return err
	}

	a.tiersLotsMU.RLock()
	defer a.tiersLotsMU.RUnlock()
	_, ok := a.tiersLots[tierID]
	if !ok {
		return errors.New(errTierNotFound)
	}

	a.tiersLots[tierID].subscribedUsersMU.RLock()
	defer a.tiersLots[tierID].subscribedUsersMU.RUnlock()
	for _, subscriber := range a.tiersLots[tierID].subscribedUsers {
		subscriber <- lots
	}

	return nil
}

func (a *auctionService) SendWonLot(tierID string, wonLot auction.UserWonLot) error {
	a.tiersWonLotsMU.RLock()
	defer a.tiersWonLotsMU.RUnlock()
	_, ok := a.tiersWonLots[tierID]
	if !ok {
		return errors.New(errTierNotFound)
	}

	a.tiersWonLots[tierID].subscribedUsersMU.RLock()
	defer a.tiersWonLots[tierID].subscribedUsersMU.RUnlock()
	for _, subscriber := range a.tiersWonLots[tierID].subscribedUsers {
		subscriber <- wonLot
	}

	return nil
}

func (a *auctionService) ProcessExpiredLot(tierID, lotID string) error {
	a.tiersLotsMU.RLock()

	a.tiersLots[tierID].lotsMU.Lock()
	delete(a.tiersLots[tierID].lots, lotID) // TBD: fix possibility of memory leak
	a.tiersLots[tierID].lotsMU.Unlock()

	a.tiersLotsMU.RUnlock()

	lot, err := a.storage.GetActiveLot(lotID)
	if err != nil {
		return err
	}

	if lot.UserID != "" && lot.UserID != botID {
		wonLot, err := a.storage.CreateWonLot(&auction.WonLot{
			Price:     lot.Price,
			UserID:    lot.UserID,
			CloseTime: time.Now(),
			Status:    statusWon,
			TierID:    lot.TierID,
		})
		if err != nil {
			return err
		}

		err = a.SendWonLot(tierID, auction.UserWonLot{
			UserID:      lot.UserID,
			ActiveLotID: lotID,
			WonLot:      wonLot,
		})
		if err != nil {
			return err
		}

		err = a.storage.DeleteActiveLot(lot)
		if err != nil {
			return err
		}

		payload := notification.WonInAuction{
			UserId:   lot.UserID,
			Price:    lot.Price,
			TierName: lot.Tier.Name,
		}

		err = a.userInfrastructureClient.SendWonInAuctionNotification(lot.UserID, payload)
		if err != nil {
			return err
		}

	} else {
		err = a.storage.DeleteActiveLot(lot)
		if err != nil {
			return err
		}

		tier, err := a.storage.GetTier(tierID)
		if err != nil {
			return err
		}

		err = a.storage.IncreaseProductQuantity(tier)
		if err != nil {
			return err
		}
	}

	return nil
}

func (a *auctionService) CreateActiveLot(tierID string) error {
	a.newLotsQueue <- tierID

	return nil
}

func (a *auctionService) GetProducts() ([]auction.GetProductResponse, error) {
	products, err := a.storage.GetProducts()
	if err != nil {
		return nil, err
	}

	return products, nil
}

func (a *auctionService) GetBids(tierID, lotID string) ([]auction.BidResponse, error) {
	bids, err := a.storage.GetBids(lotID)
	if err != nil {
		return nil, err
	}

	bidsResponse := make([]auction.BidResponse, 0, len(bids))

	for _, bid := range bids {
		bidsResponse = append(bidsResponse, auction.BidResponse{
			Amount:  bid.Amount,
			BidTime: bid.CreatedAt,
		})
	}

	return bidsResponse, nil
}

func (a *auctionService) CreateAgreement(user identity.Identity, tierID string, wonLot *auction.WonLot) (agreements.AgreementID, error) {
	var agreement agreements.AgreementID

	tier, err := a.storage.GetTier(tierID)
	if err != nil {
		return agreements.AgreementID{}, err
	}

	switch tier.Product.Category {
	case categoryWallet:
		switch tier.Product.Merchant {
		case merchantSafeheron:
			agreementRequest := agreements.CreateAgreementRequest{
				Investment:    fmt.Sprint(wonLot.Price),
				AgreementType: agreementTypeSafeheronAcquisition,
			}

			agreementID, err := a.userInfrastructureClient.CreateAgreement(user.Token, agreementRequest)
			if err != nil {
				return agreements.AgreementID{}, err
			}

			err = a.storage.UpdateAgreementID(wonLot, agreementID)
			if err != nil {
				return agreements.AgreementID{}, err
			}

			agreement = agreementID
		}
	}

	return agreement, nil
}

func (a *auctionService) CancelWonLot(agreementID string) (*auction.WonLot, error) {
	wonLot, err := a.storage.GetWonLotByAgreement(agreementID)
	if err != nil {
		return nil, err
	}

	wonLot.Status = cancelStatus

	updatedWonLot, err := a.storage.UpdateWonLotByAgreementId(agreementID, wonLot)
	if err != nil {
		return nil, err
	}

	return updatedWonLot, nil
}
