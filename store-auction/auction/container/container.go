package container

import (
	"github.com/Polity_MVP/store-auction/auction/config"
	"github.com/Polity_MVP/store-auction/auction/flagger"
	"github.com/Polity_MVP/store-auction/auction/validator"
	"go.uber.org/zap"
)

// AuctionContainer represents an interface for accessing the data which sharing in overall application
type AuctionContainer interface {
	GetConfig() *config.AuctionConfig
	GetLogger() *zap.SugaredLogger
	GetValidator() *validator.AuctionValidator
	GetFlagger() flagger.AuctionFlagger
}

// container struct is for sharing data such as the setting of application and logger in overall this application
type container struct {
	config    *config.AuctionConfig
	logger    *zap.SugaredLogger
	validator *validator.AuctionValidator
	flagger   flagger.AuctionFlagger
}

// NewAuctionContainer is constructor
func NewAuctionContainer(config *config.AuctionConfig, logger *zap.SugaredLogger, validator *validator.AuctionValidator, flagger flagger.AuctionFlagger) AuctionContainer {
	return &container{
		config:    config,
		logger:    logger,
		validator: validator,
		flagger:   flagger,
	}
}

// GetConfig returns the object of configuration
func (c *container) GetConfig() *config.AuctionConfig {
	return c.config
}

// GetLogger returns the object of logger
func (c *container) GetLogger() *zap.SugaredLogger {
	return c.logger
}

// GetValidator returns the object of validator
func (c *container) GetValidator() *validator.AuctionValidator {
	return c.validator
}

// GetFlagger returns the object of validator
func (c *container) GetFlagger() flagger.AuctionFlagger {
	return c.flagger
}
