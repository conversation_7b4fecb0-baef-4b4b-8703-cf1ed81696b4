package userInfrastructure

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httputil"

	"github.com/Polity_MVP/store-auction/auction/container"
	"github.com/Polity_MVP/store-auction/auction/internal/models/agreements"
	"github.com/Polity_MVP/store-auction/auction/internal/models/notification"
)

const (
	wonInAuctionNotificationType      = "WON_IN_AUCTION"
	agreementTypeSafeheronAcquisition = "SAFEHERON_ACQUISITION"
)

type Client interface {
	SendWonInAuctionNotification(userID string, payload notification.WonInAuction) error
	CreateAgreement(token string, payload agreements.CreateAgreementRequest) (agreements.AgreementID, error)
}

// client wrapper for the interface implementation
type client struct {
	auctionContainer container.AuctionContainer
}

// NewClient constructs an instance of the User Infrastructure Client
func NewClient(auctionContainer container.AuctionContainer) (Client, error) {
	return &client{
		auctionContainer: auctionContainer,
	}, nil
}

func (c *client) SendWonInAuctionNotification(userID string, payload notification.WonInAuction) error {
	notificationURL := fmt.Sprintf("http://%s/api/v1/notifications/webhook", net.JoinHostPort(
		c.auctionContainer.GetConfig().UserInfrastructureHost, c.auctionContainer.GetConfig().UserInfrastructurePort),
	)

	encodedPayload, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	body, err := json.Marshal(notification.Notification{
		NotificationType: wonInAuctionNotificationType,
		UserID:           userID,
		Payload:          encodedPayload,
	})
	if err != nil {
		return err
	}

	userInfrastructureRequest, err := http.NewRequest(http.MethodPost, notificationURL, bytes.NewBuffer(body))
	if err != nil {
		return err
	}

	userInfrastructureRequest.Header.Set("Content-Type", "application/json")

	//httpClient := &http.Client{}
	resp, err := http.DefaultClient.Do(userInfrastructureRequest)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Unexpected response status code from User Infrastructure: %d ", resp.StatusCode)
	}

	return nil
}

func (c *client) CreateAgreement(token string, payload agreements.CreateAgreementRequest) (agreements.AgreementID, error) {
	var agreementsResponse agreements.CreateAgreementResponse

	agreementURL := fmt.Sprintf("http://%s/api/v1/auction/agreements", net.JoinHostPort(
		c.auctionContainer.GetConfig().UserInfrastructureHost, c.auctionContainer.GetConfig().UserInfrastructurePort))

	body, err := json.Marshal(payload)

	userInfrastructureRequest, err := http.NewRequest(http.MethodPost, agreementURL, bytes.NewBuffer(body))
	if err != nil {
		c.auctionContainer.GetLogger().With(err).Error("cant create request")
		return agreements.AgreementID{}, err
	}

	userInfrastructureRequest.Header.Add("Content-Type", "application/json")
	userInfrastructureRequest.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))

	_, err = httputil.DumpRequest(userInfrastructureRequest, true)
	if err != nil {
		c.auctionContainer.GetLogger().With(err).Error("err")
	}

	resp, err := http.DefaultClient.Do(userInfrastructureRequest)
	if err != nil {
		c.auctionContainer.GetLogger().With(err).Debug("cant do request")
		return agreements.AgreementID{}, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		responseBody, err := io.ReadAll(resp.Body)
		if err != nil {
			c.auctionContainer.GetLogger().With(err).Debug("cant read response")
			return agreements.AgreementID{}, err
		}
		c.auctionContainer.GetLogger().With(responseBody).Debug("status code is not valid")
		return agreements.AgreementID{}, fmt.Errorf("Unexpected response status code from User Infrastructure: %d ", resp.StatusCode)
	}

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.auctionContainer.GetLogger().With(err).Debug("cant read response")
		return agreements.AgreementID{}, err
	}

	err = json.Unmarshal(responseBody, &agreementsResponse)
	if err != nil {
		return agreements.AgreementID{}, err
	}

	return agreements.AgreementID{AgreementID: agreementsResponse.AgreementID}, nil
}
