package flagger

import (
	"os"
	"strings"

	"github.com/Polity_MVP/store-auction/auction/config"
	"github.com/spf13/viper"
)

const featureFlagPrefix = "FEATURE_FLAG_"

type AuctionFlagger interface {
	GetFeatureFlag(key string) bool
}

type auctionFlagger struct {
	auctionViper *viper.Viper
}

func NewAuctionFlagger(cfg *config.AuctionConfig) (AuctionFlagger, error) {
	auctionViper := viper.New()

	auctionViper.SetConfigFile(cfg.FlagConfigPath)

	err := auctionViper.ReadInConfig()
	if err != nil {
		return nil, err
	}

	// TBD: use viper's SetEnvPrefix() and AutomaticEnv() when fix https://github.com/spf13/viper/issues/761
	for _, e := range os.Environ() {
		split := strings.Split(e, "=")
		if len(split) != 2 {
			continue
		}
		k := split[0]
		v := split[1]

		if strings.HasPrefix(k, featureFlagPrefix) {
			auctionViper.Set(strings.TrimPrefix(k, featureFlagPrefix), v)
		}
	}

	return &auctionFlagger{
		auctionViper: auctionViper,
	}, nil
}

func (f *auctionFlagger) GetFeatureFlag(key string) bool {
	// check other types (or use any if necessary) https://github.com/spf13/viper#getting-values-from-viper
	featureValue := f.auctionViper.GetBool(key)

	return featureValue
}
