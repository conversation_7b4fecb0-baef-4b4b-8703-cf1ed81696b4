package config

import (
	"github.com/caarlos0/env"
)

// AuctionConfig contains needed envs to run Store service
type AuctionConfig struct {
	StoreAuctionHost        string `env:"STORE_AUCTION_HOST" envDefault:"localhost"`
	StoreAuctionPort        string `env:"STORE_AUCTION_PORT" envDefault:"8084"`
	LogLevel                string `env:"LOG_LEVEL" envDefault:"DEBUG"`
	AuthorizationSecretKey  string `env:"AUTHORIZATION_SECRET_KEY" envDefault:"404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970"`
	LotExpiration           int    `env:"LOT_EXPIRATION_SEC" envDefault:"900"`
	GracefulShutdownTimeout int    `env:"GRACEFUL_SHUTDOWN_TIMEOUT_SEC" envDefault:"10"`
	FlagConfigPath          string `env:"FLAG_CONFIG_PATH" envDefault:"./flag-config.yaml"`
	StoreAuctionEnabled     bool   `env:"FLAG_FEATURE_STORE_ENABLED" envDefault:"true"`

	DatabaseHost     string `env:"POSTGRES_HOST" envDefault:"localhost"`
	DatabasePort     string `env:"POSTGRES_PORT" envDefault:"5437"`
	DatabaseUser     string `env:"POSTGRES_USER" envDefault:"user"`
	DatabasePassword string `env:"POSTGRES_PASSWORD" envDefault:"qwerty"`
	DatabaseName     string `env:"POSTGRES_DB" envDefault:"store_auction_database"`

	UserInfrastructureHost string `env:"USER_INFRASTRUCTURE_HOST" envDefault:"localhost"`
	UserInfrastructurePort string `env:"USER_INFRASTRUCTURE_PORT" envDefault:"8081"`
}

// NewAuctionConfig parses envs and constructs the config
func NewAuctionConfig() (*AuctionConfig, error) {
	var auctionConfig AuctionConfig

	err := env.Parse(&auctionConfig)
	if err != nil {
		return nil, err
	}

	return &auctionConfig, nil
}
