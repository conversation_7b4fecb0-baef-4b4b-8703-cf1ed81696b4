version: "3.5"
services:
  store-auction:
    build:
      context: .
      target: release
      dockerfile: Dockerfile
    image: store-auction
    volumes:
      - "./:/app"
    restart: always
    env_file:
      - .env
    ports:
      - "8084:8084"
    depends_on:
      - store-auction-storage

  store-auction-storage:
    image: postgres
    restart: always
    healthcheck:
      test: [ "CMD", "pg_isready", "-q", "-d", "$POSTGRES_DB", "-U", "$POSTGRES_USER" ]
      interval: 10s
      timeout: 3s
      retries: 5
    env_file:
      - .env
    ports:
      - "5437:5432"
      - "5439:5432"