package flagger

import (
	"os"
	"strings"

	"github.com/Polity_MVP/store-auction/store/config"
	"github.com/spf13/viper"
)

const featureFlagPrefix = "FEATURE_FLAG_"

type StoreFlagger interface {
	GetFeatureFlag(key string) bool
}

type storeFlagger struct {
	storeViper *viper.Viper
}

func NewStoreFlagger(cfg *config.StoreConfig) (StoreFlagger, error) {
	storeViper := viper.New()

	storeViper.SetConfigFile(cfg.FlagConfigPath)

	err := storeViper.ReadInConfig()
	if err != nil {
		return nil, err
	}

	// TBD: use viper's SetEnvPrefix() and AutomaticEnv() when fix https://github.com/spf13/viper/issues/761
	for _, e := range os.Environ() {
		split := strings.Split(e, "=")
		if len(split) != 2 {
			continue
		}
		k := split[0]
		v := split[1]

		if strings.HasPrefix(k, featureFlagPrefix) {
			storeViper.Set(strings.TrimPrefix(k, featureFlagPrefix), v)
		}
	}

	return &storeFlagger{
		storeViper: storeViper,
	}, nil
}

func (f *storeFlagger) GetFeatureFlag(key string) bool {
	// check other types (or use any if necessary) https://github.com/spf13/viper#getting-values-from-viper
	featureValue := f.storeViper.GetBool(key)

	return featureValue
}
