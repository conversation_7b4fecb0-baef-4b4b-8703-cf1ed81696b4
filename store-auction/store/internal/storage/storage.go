package storage

import (
	"fmt"
	"github.com/Polity_MVP/store-auction/store/container"
	"github.com/Polity_MVP/store-auction/store/internal/models/lots"
	"github.com/Polity_MVP/store-auction/store/internal/storage/migrations"
	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"log"
)

const (
	wonLotsTable = "won_lots"
	whereUserID  = "user_id = ?"
	whereStatus  = "status = ?"
	wonStatus    = "won"
)

type StoreStorage struct {
	container container.StoreContainer
	Conn      *gorm.DB
}

func NewStoreStorage(container container.StoreContainer) (*StoreStorage, error) {
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		container.GetConfig().DatabaseHost, container.GetConfig().DatabasePort, container.GetConfig().DatabaseUser, container.GetConfig().DatabasePassword, container.GetConfig().DatabaseName)

	conn, err := gorm.Open(postgres.New(postgres.Config{
		DSN: dsn,
	}), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	err = migrations.Migrate(conn)
	if err != nil {
		log.Fatalf("Can't do automigrate [%s]", err)
	}
	return &StoreStorage{
		Conn:      conn,
		container: container,
	}, nil
}

func (s *StoreStorage) GetWonLots(userID string) ([]lots.WonLot, error) {
	var wonLots []lots.WonLot

	tx := s.Conn.
		Table(wonLotsTable).
		Where(whereUserID, userID).
		Where(whereStatus, wonStatus).
		Preload("Tier").
		Find(&wonLots)
	if tx.Error != nil {
		return nil, tx.Error
	}

	return wonLots, nil
}

func (s *StoreStorage) GetPermissions(userID string) ([]lots.WonLot, error) {
	var wonLots []lots.WonLot

	tx := s.Conn.
		Table(wonLotsTable).
		Where(whereUserID, userID).
		Preload("Tier.Permission").
		Where(whereStatus, wonStatus).
		Find(&wonLots)
	if tx.Error != nil {
		return nil, tx.Error
	}

	return wonLots, nil
}
