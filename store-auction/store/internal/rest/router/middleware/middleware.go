package middleware

import (
	"errors"
	"strings"

	"github.com/Polity_MVP/store-auction/store/container"
	"github.com/Polity_MVP/store-auction/store/internal/models/identity"
	"github.com/golang-jwt/jwt"
	"github.com/valyala/fasthttp"
)

const userIdentity = "user_identity"

func ParseJWT(h fasthttp.RequestHandler, container container.StoreContainer) fasthttp.RequestHandler {
	logger := container.GetLogger()
	return func(ctx *fasthttp.RequestCtx) {
		tokenString := string(ctx.Request.Header.Peek(fasthttp.HeaderAuthorization))
		if tokenString == "" {
			logger.Debug("Token string in Authorization Header is empty.")
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}
		if !strings.HasPrefix(tokenString, "Bearer") {
			logger.Debug("Token string in Authorization Header doesn't contain prefix 'Bearer'.")
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}

		tokenString = strings.Replace(tokenString, "Bearer ", "", 1)

		token, err := jwt.ParseWithClaims(tokenString, &identity.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, errors.New("unexpected signing method")
			}
			return []byte(container.GetConfig().AuthorizationSecretKey), nil
		})
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}

		err = token.Claims.Valid()
		if !token.Valid || err != nil {
			ctx.SetStatusCode(fasthttp.StatusForbidden)
			return
		}

		claims := token.Claims.(*identity.CustomClaims)
		user := identity.Identity{
			UserID:   claims.UserID,
			Username: claims.Username,
			Role:     claims.Role,
			Token:    tokenString,
		}

		err = container.GetValidator().Validator.Struct(user)
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}

		ctx.SetUserValue(userIdentity, user)

		h(ctx)
	}
}

func SetupResponse(h fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		ctx.Response.Header.Set(fasthttp.HeaderAccessControlAllowOrigin, "*")
		ctx.Response.Header.Set(fasthttp.HeaderAccessControlAllowCredentials, "true")
		ctx.Response.Header.Set(fasthttp.HeaderContentType, "application/json")
		ctx.Response.Header.Set(fasthttp.HeaderAccessControlAllowMethods, "GET,HEAD,OPTIONS,POST,PUT,DELETE,PATCH")
		ctx.Response.Header.Set(fasthttp.HeaderAccessControlAllowHeaders, "Access-Control-Allow-Headers, "+
			"Origin,Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, "+
			"Access-Control-Request-Headers, Authorization")

		h(ctx)
	}
}
