package service

import (
	"fmt"

	"github.com/Polity_MVP/store-auction/store/container"
	"github.com/Polity_MVP/store-auction/store/internal/models/lots"
	"github.com/Polity_MVP/store-auction/store/internal/models/permissions"
	"github.com/Polity_MVP/store-auction/store/internal/storage"
)

type StoreService interface {
	GetWonLots(userID string) ([]lots.WonLotResponse, error)
	GetPermissionsForWonLots(userID string) ([]permissions.PermissionResponse, error)
}

type storeService struct {
	container container.StoreContainer
	storage   *storage.StoreStorage
}

func NewStoreService(container container.StoreContainer, storage *storage.StoreStorage) StoreService {
	return &storeService{
		container: container,
		storage:   storage,
	}
}

func (s *storeService) GetWonLots(userID string) ([]lots.WonLotResponse, error) {
	wonLotsResponse := []lots.WonLotResponse{}

	userWonLots, err := s.storage.GetWonLots(userID)
	if err != nil {
		return nil, err
	}

	for _, lot := range userWonLots {
		wonLotsResponse = append(wonLotsResponse, lots.WonLotResponse{
			ID:        fmt.Sprint(lot.ID),
			Price:     lot.Price,
			CloseTime: lot.CloseTime,
			Status:    lot.Status,
			TierID:    lot.TierID,
			ProductID: lot.Tier.ProductID,
		})
	}

	return wonLotsResponse, nil
}

func (s *storeService) GetPermissionsForWonLots(userID string) ([]permissions.PermissionResponse, error) {
	permissionsResponse := []permissions.PermissionResponse{}

	userWonLots, err := s.storage.GetPermissions(userID)
	if err != nil {
		return nil, err
	}

	for _, lot := range userWonLots {
		permissionsResponse = append(permissionsResponse, permissions.PermissionResponse{
			ID:              fmt.Sprint(lot.Tier.Permission.ID),
			Category:        lot.Tier.Permission.Category,
			Action:          lot.Tier.Permission.Action,
			ProductQuantity: lot.Tier.ProductQuantity,
		})
	}

	return permissionsResponse, nil
}
