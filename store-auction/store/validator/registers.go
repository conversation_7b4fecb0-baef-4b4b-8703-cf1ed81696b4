package validator

import (
	ut "github.com/go-playground/universal-translator"
	goValidator "github.com/go-playground/validator/v10"
)

const (
	userIDMaxLength   = 50
	userNameMaxLength = 50
	userRoleMaxLength = 50
)

func (v *StoreValidator) requiredRegister() error {
	_ = v.Validator.RegisterTranslation("required", v.trans, func(ut ut.Translator) error {
		return ut.Add("required", "{0} is a required field", true)
	}, func(ut ut.Translator, fe goValidator.FieldError) string {
		t, _ := ut.T("required", fe.Field())
		return t
	})

	return nil
}

func (v *StoreValidator) userNameRegister() error {
	_ = v.Validator.RegisterValidation("user_name", func(fl goValidator.FieldLevel) bool {
		return fl.Field().String() != "" && len(fl.Field().String()) <= userNameMaxLength
	})

	_ = v.Validator.RegisterTranslation("user_name", v.trans, func(ut ut.Translator) error {
		return ut.Add("user_name", "{0} invalid user name", true)
	}, func(ut ut.Translator, fe goValidator.FieldError) string {
		t, _ := ut.T("user_name", fe.Field())
		return t
	})

	return nil
}

func (v *StoreValidator) userRoleRegister() error {
	_ = v.Validator.RegisterValidation("user_role", func(fl goValidator.FieldLevel) bool {
		return fl.Field().String() != "" && len(fl.Field().String()) <= userRoleMaxLength
	})

	_ = v.Validator.RegisterTranslation("user_role", v.trans, func(ut ut.Translator) error {
		return ut.Add("user_role", "{0} invalid user role", true)
	}, func(ut ut.Translator, fe goValidator.FieldError) string {
		t, _ := ut.T("user_role", fe.Field())
		return t
	})

	return nil
}

func (v *StoreValidator) userIDRegister() error {
	_ = v.Validator.RegisterValidation("user_id", func(fl goValidator.FieldLevel) bool {
		return fl.Field().String() != "" && len(fl.Field().String()) <= userIDMaxLength
	})

	_ = v.Validator.RegisterTranslation("user_id", v.trans, func(ut ut.Translator) error {
		return ut.Add("user_id", "{0} invalid user id", true)
	}, func(ut ut.Translator, fe goValidator.FieldError) string {
		t, _ := ut.T("user_id", fe.Field())
		return t
	})

	return nil
}
