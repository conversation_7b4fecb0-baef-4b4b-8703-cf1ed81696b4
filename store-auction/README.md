# Store Auction

## Description

That unit consists of to logical services: Store and Auction. 
Store is responsible for managing permissions and won lots. 
Auction is responsible active auction related stuff (making bids, get products to buy etc.).

## Installation

To start the service:

1. Run the following command
```
cp .env.example .env
```
2. Set appropriate env values
3. Run the following command
```
make run
```

## Note

You are able to set feature flags either configuring flag-config.yaml or by setting environment variable with prefix 'FEATURE_FLAG_'
For example to create a 'my_feature' feature flag - just put in .env:
```
FEATURE_FLAG_MY_FEATURE_ENABLED = true
```