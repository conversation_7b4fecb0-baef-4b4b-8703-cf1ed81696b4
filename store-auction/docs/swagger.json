{"swagger": "2.0", "info": {"description": "This is service for auction representing and managing tiers.", "title": "Store Auction", "contact": {}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "basePath": "/api/v1", "paths": {"/api/v1/connect/{tier_id}/{token}/": {"get": {"description": "Please pay attention - documentation for that endpoint was added to meet the team understanding, websocket communication is not the part of REST. Connect by tier id and receive tiers lots updates", "tags": ["auction"], "summary": "Get lots update via websocket", "parameters": [{"type": "string", "description": "user token", "name": "token", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.LotsStateResponse"}}}}}, "/api/v1/products/{product_id}/tiers/{tier_id}/lot/": {"post": {"security": [{"BearerAuth": []}], "description": "Opens active lot for tier", "tags": ["auction"], "summary": "Opens active lot", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/": {"get": {"security": [{"BearerAuth": []}], "description": "Gets all products list", "produces": ["application/json"], "tags": ["auction"], "summary": "Get products", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/auction.GetProductResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/agreement/{agreement_id}/cancel/": {"put": {"security": [{"BearerAuth": []}], "description": "Cancel won lot by agreementID", "produces": ["application/json"], "tags": ["auction"], "summary": "Cancel won lot", "parameters": [{"type": "string", "description": "agreement id", "name": "agreement_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.WonLot"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/{product_id}/tiers/": {"get": {"security": [{"BearerAuth": []}], "description": "Gets tiers by product id", "produces": ["application/json"], "tags": ["auction"], "summary": "Get tiers", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/auction.GetTierResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/{product_id}/tiers/{tier_id}/": {"get": {"security": [{"BearerAuth": []}], "description": "Gets tiers by product and tier id", "produces": ["application/json"], "tags": ["auction"], "summary": "Get tiers", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/auction.GetTierResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/{product_id}/tiers/{tier_id}/buy/": {"post": {"security": [{"BearerAuth": []}], "description": "Buy tier now without bidding process", "tags": ["auction"], "summary": "Buy tier now", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/{product_id}/tiers/{tier_id}/connect/": {"get": {"security": [{"BearerAuth": []}], "description": "Get websocket address to receive active lots update and list of active lots by tier", "produces": ["application/json"], "tags": ["auction"], "summary": "Get lots websocket address", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.ActiveLotsAddress"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/products/{product_id}/tiers/{tier_id}/lots/{lot_id}/": {"get": {"security": [{"BearerAuth": []}], "description": "Get bids history for active lot", "produces": ["application/json"], "tags": ["auction"], "summary": "Get lot's bids history", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}, {"type": "string", "description": "lot id", "name": "lot_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/auction.BidResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Get websocket address to receive active lots update and list of active lots by tier", "produces": ["application/json"], "tags": ["auction"], "summary": "Get lots websocket address", "parameters": [{"type": "string", "description": "product id", "name": "product_id", "in": "path", "required": true}, {"type": "string", "description": "tier id", "name": "tier_id", "in": "path", "required": true}, {"type": "string", "description": "lot id", "name": "lot_id", "in": "path", "required": true}, {"description": "users bid", "name": "bid", "in": "body", "required": true, "schema": {"$ref": "#/definitions/auction.BidRequest"}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/tiers/won/": {"get": {"security": [{"BearerAuth": []}], "description": "Get tiers owned by user", "produces": ["application/json"], "tags": ["store"], "summary": "Get tiers owned by user", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/lots.WonLotResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}, "/tiers/won/permissions/": {"get": {"security": [{"BearerAuth": []}], "description": "Get users permissions", "produces": ["application/json"], "tags": ["store"], "summary": "Get users permissions", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/permissions.PermissionResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.ErrorResponse"}}}}}}, "definitions": {"api.ErrorResponse": {"type": "object", "properties": {"message": {"type": "string"}}}, "auction.BidRequest": {"type": "object", "properties": {"price": {"type": "integer"}}}, "auction.BidResponse": {"type": "object", "properties": {"amount": {"type": "integer"}, "bid_time": {"type": "string"}}}, "auction.GetProductResponse": {"type": "object", "properties": {"category": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "merchant": {"type": "string"}, "min_price": {"type": "integer"}, "name": {"type": "string"}}}, "auction.GetTierResponse": {"type": "object", "properties": {"buy_now_price": {"type": "integer"}, "id": {"type": "string"}, "permission_id": {"type": "string"}, "product_id": {"type": "string"}, "product_quantity": {"type": "integer"}, "tier_name": {"type": "string"}}}, "lots.WonLotResponse": {"type": "object", "properties": {"close_time": {"type": "string"}, "id": {"type": "string"}, "price": {"type": "integer"}, "product_id": {"type": "string"}, "status": {"type": "string"}, "tier_id": {"type": "string"}}}, "permissions.PermissionResponse": {"type": "object", "properties": {"action": {"type": "string"}, "category": {"type": "string"}, "id": {"type": "string"}, "product_quantity": {"type": "integer"}}}, "swagger.ActiveLotsAddress": {"type": "object", "properties": {"lots_state": {"type": "array", "items": {"$ref": "#/definitions/swagger.LotUpdate"}}, "websocket_address": {"type": "string"}}}, "swagger.LotUpdate": {"type": "object", "properties": {"id": {"type": "string"}, "price": {"type": "integer"}, "time_left": {"type": "integer"}, "winning_bid": {"type": "boolean"}}}, "swagger.LotsStateResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/swagger.LotUpdate"}}, "type": {"type": "string"}}}, "swagger.WonLot": {"type": "object", "properties": {"agreement_id": {"type": "string"}, "close_time": {"type": "string"}, "price": {"type": "integer"}, "status": {"type": "string"}, "tier": {"type": "object", "properties": {"buy_now_price": {"type": "integer"}, "product": {"type": "object", "properties": {"category": {"type": "string"}, "description": {"type": "string"}, "merchant": {"type": "string"}, "min_price": {"type": "integer"}, "name": {"type": "string"}}}, "product_id": {"type": "string"}, "product_quantity": {"type": "integer"}, "tier_name": {"type": "string"}}}, "tier_id": {"type": "string"}, "user_id": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}