basePath: /api/v1
definitions:
  api.ErrorResponse:
    properties:
      message:
        type: string
    type: object
  auction.BidRequest:
    properties:
      price:
        type: integer
    type: object
  auction.BidResponse:
    properties:
      amount:
        type: integer
      bid_time:
        type: string
    type: object
  auction.GetProductResponse:
    properties:
      category:
        type: string
      description:
        type: string
      id:
        type: string
      merchant:
        type: string
      min_price:
        type: integer
      name:
        type: string
    type: object
  auction.GetTierResponse:
    properties:
      buy_now_price:
        type: integer
      id:
        type: string
      permission_id:
        type: string
      product_id:
        type: string
      product_quantity:
        type: integer
      tier_name:
        type: string
    type: object
  lots.WonLotResponse:
    properties:
      close_time:
        type: string
      id:
        type: string
      price:
        type: integer
      product_id:
        type: string
      status:
        type: string
      tier_id:
        type: string
    type: object
  permissions.PermissionResponse:
    properties:
      action:
        type: string
      category:
        type: string
      id:
        type: string
      product_quantity:
        type: integer
    type: object
  swagger.ActiveLotsAddress:
    properties:
      lots_state:
        items:
          $ref: '#/definitions/swagger.LotUpdate'
        type: array
      websocket_address:
        type: string
    type: object
  swagger.LotUpdate:
    properties:
      id:
        type: string
      price:
        type: integer
      time_left:
        type: integer
      winning_bid:
        type: boolean
    type: object
  swagger.LotsStateResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/swagger.LotUpdate'
        type: array
      type:
        type: string
    type: object
  swagger.WonLot:
    properties:
      agreement_id:
        type: string
      close_time:
        type: string
      price:
        type: integer
      status:
        type: string
      tier:
        properties:
          buy_now_price:
            type: integer
          product:
            properties:
              category:
                type: string
              description:
                type: string
              merchant:
                type: string
              min_price:
                type: integer
              name:
                type: string
            type: object
          product_id:
            type: string
          product_quantity:
            type: integer
          tier_name:
            type: string
        type: object
      tier_id:
        type: string
      user_id:
        type: string
    type: object
info:
  contact: {}
  description: This is service for auction representing and managing tiers.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: Store Auction
  version: "1.0"
paths:
  /api/v1/connect/{tier_id}/{token}/:
    get:
      description: Please pay attention - documentation for that endpoint was added
        to meet the team understanding, websocket communication is not the part of
        REST. Connect by tier id and receive tiers lots updates
      parameters:
      - description: user token
        in: path
        name: token
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/swagger.LotsStateResponse'
      summary: Get lots update via websocket
      tags:
      - auction
  /api/v1/products/{product_id}/tiers/{tier_id}/lot/:
    post:
      description: Opens active lot for tier
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      responses:
        "201":
          description: Created
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Opens active lot
      tags:
      - auction
  /products/:
    get:
      description: Gets all products list
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/auction.GetProductResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get products
      tags:
      - auction
  /products/{product_id}/tiers/:
    get:
      description: Gets tiers by product id
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/auction.GetTierResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get tiers
      tags:
      - auction
  /products/{product_id}/tiers/{tier_id}/:
    get:
      description: Gets tiers by product and tier id
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/auction.GetTierResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get tiers
      tags:
      - auction
  /products/{product_id}/tiers/{tier_id}/buy/:
    post:
      description: Buy tier now without bidding process
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      responses:
        "201":
          description: Created
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Buy tier now
      tags:
      - auction
  /products/{product_id}/tiers/{tier_id}/connect/:
    get:
      description: Get websocket address to receive active lots update and list of
        active lots by tier
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/swagger.ActiveLotsAddress'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get lots websocket address
      tags:
      - auction
  /products/{product_id}/tiers/{tier_id}/lots/{lot_id}/:
    get:
      description: Get bids history for active lot
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      - description: lot id
        in: path
        name: lot_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/auction.BidResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get lot's bids history
      tags:
      - auction
    put:
      description: Get websocket address to receive active lots update and list of
        active lots by tier
      parameters:
      - description: product id
        in: path
        name: product_id
        required: true
        type: string
      - description: tier id
        in: path
        name: tier_id
        required: true
        type: string
      - description: lot id
        in: path
        name: lot_id
        required: true
        type: string
      - description: users bid
        in: body
        name: bid
        required: true
        schema:
          $ref: '#/definitions/auction.BidRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get lots websocket address
      tags:
      - auction
  /products/agreement/{agreement_id}/cancel/:
    put:
      description: Cancel won lot by agreementID
      parameters:
      - description: agreement id
        in: path
        name: agreement_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/swagger.WonLot'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Cancel won lot
      tags:
      - auction
  /tiers/won/:
    get:
      description: Get tiers owned by user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/lots.WonLotResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get tiers owned by user
      tags:
      - store
  /tiers/won/permissions/:
    get:
      description: Get users permissions
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/permissions.PermissionResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get users permissions
      tags:
      - store
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
