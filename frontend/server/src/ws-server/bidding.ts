import WebSocket from 'ws'

import type { ILotState, IWSMsg } from 'src/apps/cabinet/features/Bidding/types'

import C from '../db/generators'

const lot: ILotState = {
  id: C.uuid,
  price: 5,
  winning_bid: false,
  time_left: 10 * 60 * 1000,
}

const port = +(process.env.JSON_WS_SERVER_PORT || 4001)
const wsServer = new WebSocket.Server({
  port,
})

wsServer.on('connection', client => {
  console.log('New WS connection')

  const msg: IWSMsg = {
    type: 'lots_state',
    data: [lot],
  }
  client.send(JSON.stringify(msg))
})

console.log(`WS server is listening at port ${port}`)
