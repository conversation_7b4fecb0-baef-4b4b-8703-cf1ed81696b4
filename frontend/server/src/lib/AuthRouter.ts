import { Router } from 'express'
import { JsonServerRouter } from 'json-server'
import * as jwt from 'jsonwebtoken'

import { IJWTData, UserRole } from 'src/features/User/types'

import DB from '../db'
import { IUser } from '../types'

import { createUserTokens } from './jwt'

// eslint-disable-next-line no-console
console.log(`
Test users available:
${DB.users
  .map(x => ` #${x.id} | [${x.role}] ${x.username} / ${x.password}`)
  .join('\n')}
`)

export function setupFractalRouter(db: JsonServerRouter<typeof DB>['db']) {
  const router = Router()

  router.post('/obtain-token/', (req, res) => {
    res.status(200).json({
      access_token: 'test',
      refresh_token: 'test',
    })
  })

  router.post(['/login/', '/registration/'], (req, res) => {
    const { role } = req.body
    const users = db.get('users').value()

    const user = users.find(x => x.role.toLowerCase() === role.toLowerCase())

    if (user !== undefined) {
      const tokens = createUserTokens(user)
      // eslint-disable-next-line no-console
      console.log('\nLog in as %o\nAuth data: %o\n', user, tokens)
      return res.status(200).json({
        ...tokens,
        redirect_url:
          role === UserRole.Operator
            ? 'http://localhost:3002'
            : 'http://localhost:3001',
      })
    }

    res.status(404).json({ message: 'User not found' })
  })

  router.post('/refresh-token', (req, res) => {
    const authHeader = (req.headers['Authorization'] ?? '') as string
    const [, token] = authHeader.split(' ')
    try {
      const data = jwt.decode(token) as IJWTData
      let user: IUser | undefined
      if (data.role === 'AVATAR') {
        const avatars = db.get('investor_avatars').value()
        const avatar = avatars.find(
          x => x.id === +data.sub
        ) as typeof avatars[0]

        const users = db.get('users').value()
        const db_user = users.find(x => x.id === +data.sub)
        user = {
          ...db_user,
          role: 'AVATAR',
          username: avatar.username,
        } as typeof users[0]
      } else {
        const users = db.get('users').value()
        user = users.find(x => x.id === +data.sub)
      }

      // eslint-disable-next-line no-console
      console.log('[refresh-token] Decoded data from token:', data)
      // eslint-disable-next-line no-console
      console.log('[refresh-token] Matching user:', user)
      if (user !== undefined) {
        return res.status(200).send(createUserTokens(user))
      } else {
        return res.status(400).json({ message: 'Invalid auth token' })
      }
    } catch (e) {
      console.error(e)
      res.status(400).json({ message: 'Unable to decode token' })
    }
  })

  return router
}
