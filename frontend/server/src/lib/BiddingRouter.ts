import { Router } from 'express'
import { JsonServerRouter } from 'json-server'

import type {
  IAuctionWinnerResponse,
  IWSState,
} from 'src/apps/cabinet/features/Bidding/types'
import { AgreementType } from 'src/types'

import DB from '../db'
import { sample } from '../db/generators'

export function setupBiddingRouter(db: JsonServerRouter<typeof DB>['db']) {
  const router = Router()

  router.get('/products', (req, res) => {
    res.status(200).send(db.get('bidding_products').value())
  })

  router.get('/products/:id/tiers', (req, res) => {
    const { id } = req.params
    const tiers = db
      .get('bidding_tiers')
      .value()
      .filter(x => x.product_id === id)
    res.status(200).send(tiers)
  })

  router.post('/products/:product/tiers/:tier/buy', (req, res) => {
    const agreement_id = sample(
      db
        .get('avatar_agreements')
        .value()
        .filter(x => x.agreementType === AgreementType.SafeheronAcquisition)
        .map(x => x.agreementID)
    )
    const response: IAuctionWinnerResponse = { agreement_id }
    res.status(200).send(response)
  })

  router.put('/products/:product/tiers/:tier/lots/:lot', (req, res) => {
    res.sendStatus(200)
  })

  router.get('/products/:product/tiers/:tier/connect', (req, res) => {
    const response: Pick<IWSState, 'websocket_address'> = {
      websocket_address: 'ws://localhost:4001',
    }
    res.status(200).send(response)
  })

  return router
}
