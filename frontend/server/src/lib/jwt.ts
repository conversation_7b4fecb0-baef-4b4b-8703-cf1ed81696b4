import express_jwt from 'express-jwt'
import { Options as Unless } from 'express-unless'
import * as jwt from 'jsonwebtoken'

import C from '../db/generators'
import { IUser } from '../types'

export const JWT_SECRET = '404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970'
export const create_jwt_middleware = (authPaths: Unless['path']) =>
  express_jwt({
    secret: JWT_SECRET,
    algorithms: ['HS512'],
  }).unless({
    path: authPaths,
  })

export const createTokens = (data: Parameters<typeof jwt.sign>[0]) => {
  const sign = (expiresIn: number) =>
    jwt.sign(data, JWT_SECRET, {
      expiresIn,
    })
  const expiry = 60 * 60 * 24 * 30
  return {
    access_token: sign(expiry),
    refresh_token: sign(expiry),
  }
}

export function createUserTokens(user: Omit<IUser, 'password'>) {
  return createTokens({
    sub: user.id.toString(),
    username: user.username,
    role: user.role,
    photo: user.photo,
    uuid: C.uuid,
  })
}
