import { Router } from 'express'
import ExpressFormData from 'express-form-data'
import { JsonServerRouter } from 'json-server'

import {
  ICreateAvatarFormFields,
  IInvestorAvatarRaw,
} from 'src/apps/auth/features/Investor/types'

import DB from '../db'
import { UserRole } from '../types'

import { createUserTokens } from './jwt'

export function setupInvestorRouter(db: JsonServerRouter<typeof DB>['db']) {
  const router = Router()

  router.post('/avatars/login', (req, res) => {
    const { id } = req.body
    const users = db.get('investor_avatars').value()
    // TODO: get related user from `req.user`, check he is the owner of requested avatar
    const avatar = users.find(x => x.id === id)
    if (avatar !== undefined) {
      const tokens = createUserTokens({
        id: avatar.id,
        username: avatar.username,
        role: UserRole.Avatar,
        photo: avatar.photo,
      })
      console.log('JWT created', tokens)
      res.status(302).send({
        redirect_url: 'http://localhost:3001',
        ...tokens,
      })
    } else {
      res.sendStatus(404)
    }
  })

  router.use(ExpressFormData.parse()).post('/avatars', (req, res) => {
    const avatars = db.get('investor_avatars').value()

    const { name, element_id } = req.body as ICreateAvatarFormFields
    const response: IInvestorAvatarRaw = {
      id: Math.max(...avatars.map(x => x.id)) + 1,
      username: name,
      element_id,
      hasAcquiredNode: false,
      photo: null, // don't handle file upload
    }
    res.status(201).send(response)
  })

  // A stub for notification route (notify backend that Element invite has been sent)
  router.post('/', (req, res) => {
    res.sendStatus(200)
  })

  return router
}
