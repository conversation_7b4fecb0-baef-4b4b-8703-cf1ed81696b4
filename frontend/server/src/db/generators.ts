import Casual from 'casual'
import { sample as baseSample } from 'lodash'
import moment, { unitOfTime } from 'moment'

import { BlockchainAsset } from 'src/types'
import { getAssetIconURI } from 'src/utils/blockchain'

import { PUBLIC_URL } from '../const'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ListOrDict<T> = T[] | Record<any, T>
// Better typed version of `_.sample`, without `undefined` in return value
type ISample = <T>(xs: ListOrDict<T> | Readonly<ListOrDict<T>>) => T

interface CustomGenerators {
  iso_now: string
  iso_offset: (offset?: number, unit?: unitOfTime.DurationConstructor) => string
  file: (path: string) => string
  photo: (w?: number, h?: number) => string
  crypto: BlockchainAsset
  crypto_icon: string
  /* Casual has a `random_element` / `random_value` helpers, but they return `any`.
   * Use this unified and well-typed helper instead. */
  sample: ISample
}

export const sample = baseSample as ISample

Casual.define('sample', sample)

Casual.define('iso_now', () => moment().format())
Casual.define(
  'iso_offset',
  (offset: number, unit: unitOfTime.DurationConstructor) => {
    const m = moment()
    const units: unitOfTime.DurationConstructor[] = [
      'h',
      'm',
      's',
      'd',
      'm',
      's',
    ]
    const args = [
      Math.abs(offset ?? Casual.integer(-24, 24)),
      unit ?? baseSample(units),
    ]
    const modified = offset < 0 ? m.subtract(...args) : m.add(...args)
    return modified.format()
  }
)
Casual.define('file', (path: string) => `${PUBLIC_URL}/${path}`)
Casual.define('photo', (w: number, h: number) => {
  const id = Casual.integer(1, 1000)
  const DEFAULT_SIZE = 200
  // can't use defaults in func params, because Casual will treat such generator as getter prop
  const size = [w ?? DEFAULT_SIZE, h ?? w ?? DEFAULT_SIZE]
    .filter(x => x !== undefined)
    .join('/')
  return `https://picsum.photos/seed/${id}/${size}`
})

Casual.define('crypto', () => sample(Object.values(BlockchainAsset)))

Casual.define('crypto_icon', () =>
  getAssetIconURI((Casual as typeof ExtraCasual).crypto)
)

// ---

Casual.seed(123) // produce same results over re-runs

// ---

const ExtraCasual = Casual as typeof Casual & CustomGenerators

export default ExtraCasual

export const list = <T>(n: number, fn: (i: number) => T) =>
  Array.from(new Array(n), (_, i) => fn(i))

export const dict = <K extends string | number, V>(params: {
  key: () => K
  value: () => V
  size?: number
}) =>
  Array.from(
    Array(params.size ?? Casual.integer(0)),
    _ => [params.key(), params.value()] as const
  ).reduce((m, [k, v]) => ({ ...m, [k]: v }), {})

export const CRYPTO_ASSETS = Object.values(BlockchainAsset)
