import { IPlatformStatistics } from 'src/apps/operator/features/Dashboard/types'

import C, { dict } from './generators'

const statistics: IPlatformStatistics = {
  investorsCount: C.integer(0),
  avatarsCount: C.integer(0),
  adviserCount: C.integer(0),
  signedAgreementsCount: C.integer(0),
  avatarPerAdviser: dict({
    size: 10,
    key: () => C.name,
    value: () => C.integer(0, 50),
  }),
  auaPerAdviser: dict({
    size: 10,
    key: () => C.name,
    value: () => C.integer(1000, 500000),
  }),
  walletStatisticsResult: [
    { walletType: 'DFNS', count: C.integer(0, 100) },
    { walletType: 'SAFEHERON', count: C.integer(0, 100) },
  ],
}

const routes = {
  operator_statistics: statistics,
}

export default routes
