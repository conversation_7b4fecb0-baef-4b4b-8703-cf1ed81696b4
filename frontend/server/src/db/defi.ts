import {
  IDefiProduct,
  IWalletDefiAsset,
} from 'src/apps/cabinet/features/Wallet/types'

import C from './generators'

const DEFI_ASSETS = [
  {
    asset: {
      id: null,
      address: '******************************************',
      chainId: 137,
      name: '<PERSON>',
      displayName: 'DAI',
      symbol: 'DAI',
      icon: 'https://icons.de.fi/dai/coingecko.webp',
      decimals: 18,
      price: 1.0001146900528144,
      categories: [],
    },
    balance: 0.5,
    price: 1.0001146900528144,
    total: 0.5000573450264072,
  },
  {
    asset: {
      id: null,
      address: '******************************************',
      chainId: 137,
      name: 'Ankr Staked POL',
      displayName: 'ANKRPOL',
      symbol: 'ankrPOL',
      icon: 'https://icons.de.fi/plg/******************************************/coingecko-large.webp',
      decimals: 18,
      price: 0.740075,
      categories: [],
    },
    balance: 3.5550223717885534,
    price: 0.740075,
    total: 2.6309831818014136,
  },
  {
    asset: {
      id: null,
      address: '******************************************',
      chainId: 137,
      name: 'Polygon',
      displayName: 'POL',
      symbol: 'POL',
      icon: 'https://icons.de.fi/matic-network/coingecko.webp',
      decimals: 18,
      price: 0.6604455510327539,
      categories: [],
    },
    balance: 13.71305206939829,
    price: 0.6604455510327539,
    total: 9.056724230314599,
  },
  {
    asset: {
      id: null,
      address: '******************************************',
      chainId: 137,
      name: 'Tether',
      displayName: 'USDT',
      symbol: 'USDT',
      icon: 'https://icons.de.fi/tether/coingecko.webp',
      decimals: 6,
      price: 0.9994235706139679,
      categories: [],
    },
    balance: 15.24,
    price: 0.9994235706139679,
    total: 15.23121521615687,
  },
]

const DAPPS: IDefiProduct[] = [
  {
    ID: 1,
    name: 'Uniswap',
    description:
      'Swap, earn, and build on the leading decentralized crypto trading protocol.',
    link: 'https://app.uniswap.org/',
    icon: 'https://app.uniswap.org/favicon.png',
  },
  {
    ID: 2,
    name: 'dHEDGE',
    description:
      'Reliable way for everyone to participate in asset management.',
    link: 'https://app.dhedge.org/',
    icon: 'https://firebasestorage.googleapis.com/v0/b/dhedge-public/o/logo512.png?alt=media',
  },
]

const routes = {
  defi_assets: DEFI_ASSETS.map(
    (x): IWalletDefiAsset => ({
      icon: x.asset.icon,
      price: x.price,
      name: x.asset.name,
      display_name: x.asset.displayName,
      address: x.asset.address,
      chain_id: x.asset.chainId.toString(),
      balance_usd: C.double(1, 10),
      balance: x.balance,
      asset_symbol: x.asset.symbol,
    })
  ),

  defi_products: {
    defi_products: DAPPS,
  },
}

export default routes
