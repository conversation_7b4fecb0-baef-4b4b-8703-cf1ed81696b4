import {
  ITransactionHistoryEntry,
  IWallet,
  IWalletAsset,
  IWalletAssetsListResponse,
  IWalletListItemResponse,
  WalletProvider,
  WalletStatus,
} from 'src/apps/cabinet/features/Wallet/types'

import C, { CRYPTO_ASSETS, list } from './generators'

const PROVIDERS: WalletProvider[] = ['dfns', 'safeheron']

type FakeWallet = IWalletListItemResponse & {
  _details: IWallet
  _assets: IWalletAssetsListResponse
}

export function generateWallet(
  params?: Partial<
    { provider: WalletProvider } & Pick<
      IWalletListItemResponse,
      'status' | 'wallet_name'
    >
  >
): FakeWallet {
  const {
    status = C.sample(WalletStatus),
    wallet_name: name = C.company_name,
    provider = C.sample(PROVIDERS),
  } = params ?? {}
  const uuid = C.uuid
  const created_at = C.iso_offset()
  const balance_usd = C.double(0, 100)
  return {
    request_id: uuid,
    wallet_name: name,
    created_at,
    status,
    balance_usd: balance_usd.toString(),

    _details: {
      id: C.integer(1, 100),
      wallet_name: name,
      type: provider,
      status,
      created_at,
      address: uuid,
      archived_at: C.iso_offset(),
    },

    _assets: {
      assets: generateWalletAssets(),
      supported_assets: CRYPTO_ASSETS,
      balance_usd: balance_usd.toString(),
    },
  }
}

function generateWalletAssets(): IWalletAsset[] {
  const all = CRYPTO_ASSETS.map(symbol => ({
    asset_symbol: symbol,
    balance: C.double(0, 100).toString(),
    balance_usd: C.double(0, 100).toString(),
  }))

  // remove some assets from response, so feature "add asset to wallet" is available to user
  const slice = all.slice(C.integer(0, 2), C.integer(1, 2) * -1)

  if (slice.every(x => +x.balance > 0)) {
    const start = C.integer(0, 3)
    const count = C.integer(2, 5)
    slice.slice(start, start + count).forEach(x => {
      x.balance = '0'
      x.balance_usd = '0'
    })
  }

  return slice
}

const routes = {
  wallets_dfns: list(5, () => generateWallet({ provider: 'dfns' })),
  wallets_safeheron: list(5, () => generateWallet({ provider: 'safeheron' })),
  wallets_txns: list(
    30,
    (): ITransactionHistoryEntry => ({
      type: C.sample(['sent', 'received']),
      hash: C.uuid,
      from: C.uuid,
      to: C.uuid,
      value: C.double(1, 10).toString(),
      value_usd: C.double(1, 100).toString(),
      date: C.iso_offset(),
      gas: C.double(1, 10).toString(),
    })
  ),
}

function ensureProvisionedWallet(xs: FakeWallet[]) {
  const status = WalletStatus.Provisioned
  if (xs.length === 0) return
  if (!xs.some(x => x.status === status)) {
    const [x] = xs
    x.status = status
    // eslint-disable-next-line no-underscore-dangle
    x._details.status = status
  }
}

ensureProvisionedWallet(routes.wallets_dfns)
ensureProvisionedWallet(routes.wallets_safeheron)

export default routes
