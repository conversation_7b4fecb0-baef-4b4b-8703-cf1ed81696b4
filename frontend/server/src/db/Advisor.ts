import { flatMap } from 'lodash'

import { IInvestorAvatarRaw as IAdvisorContact } from 'src/apps/cabinet/features/Advisor/types'
import { IAgreementRaw } from 'src/types'

import { agreements as baseAgreements } from './agreements'
import C, { list } from './generators'

const avatars = list<IAdvisorContact>(15, id => ({
  id,
  name: C.name,
  photoURL: C.photo(),
}))

const agreements = flatMap(avatars, (avatar, i) =>
  baseAgreements.map((base): IAgreementRaw => {
    return {
      ...base,

      // @ts-expect-error This is here to emulate filtration by avatar, implemented on real backend
      id: avatar.id,

      avatar: avatar.name,

      agreementID: (+base.agreementID + i * 10).toString(),
    }
  })
)

const agreementsDetailsRoutes = agreements.reduce((m, x) => {
  return {
    ...m,
    [`adviser_agreements_${x.agreementID}`]: x,
  }
}, {})

const routes = {
  adviser_avatars: avatars,
  adviser_agreements: agreements,
  ...agreementsDetailsRoutes,
}

export default routes
