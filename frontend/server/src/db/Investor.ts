import {
  IInvestorAdvisor,
  IInvestorAvatarRaw,
} from 'src/apps/auth/features/Investor/types'

import C, { list } from './generators'

const routes = {
  investor_avatars: list(
    4,
    (id): IInvestorAvatarRaw => ({
      id,
      username: C.name,
      photo: C.photo(),
      hasAcquiredNode: C.boolean,
      element_id: `@${C.first_name}:matrix.org`,
    })
  ),

  investor_advisers: list(
    10,
    (id): IInvestorAdvisor => ({
      id,
      name: C.name,
      photo: C.photo(),
      element_id: `@${C.first_name}:matrix.org`,
    })
  ),
}

export default routes
