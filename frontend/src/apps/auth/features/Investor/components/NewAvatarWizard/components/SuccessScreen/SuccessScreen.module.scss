.root {
  display: grid;
  justify-items: center;
  text-align: center;
  gap: 40px;

  &.compact {
    .title {
      font-size: 24px;
      line-height: 1.33;
    }

    .subtitle {
      font-size: 16px;
      line-height: 1.5;
    }
  }
}

.banner_holder {
  position: relative;
}

.advisor_photo {
  /* Photo should look like a part of a fancy svg banner.
   TODO: maybe rewrite svg source and put an <image> there? */
  position: absolute;
  right: 16px;
  top: calc(50% - 28px);

  --size: 52px;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
}

.header {
  /* none */
}

.title {
  font-weight: 500;
  font-size: 30px;
  line-height: 1.27;
  color: var(--th-clr-txt-primary);
}

.subtitle {
  font-weight: 400;
  font-size: 18px;
  line-height: 1.55;
  color: var(--th-clr-txt-hint);
}

.footer {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn_room,
.btn_dashboard {
  width: 226px;
}
