@import "src/styles/mixins";

@value item-size: 210px;

.item {
  @include m-card;
  @include m-clickable;

  &.inactive {
    --clickable-color: var(--th-clr-warn);
    cursor: not-allowed;
  }

  display: grid;
  gap: 12px;
  grid-auto-flow: row;
  justify-items: center;
  align-content: center;
  text-align: center;

  width: item-size;
  height: item-size;
  padding: 40px 12px;
}

.name {
  font-weight: 500;
  font-size: 1.125rem;
  line-height: 1.5;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.content {
  display: grid;
  justify-items: center;
  gap: 4px;
}

.photo_container {
  --size: 64px;
  width: var(--size);
  height: var(--size);
  border-radius: 12px;
  border: 1px solid var(--th-border-color);

  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.loader {
  position: absolute;
}
