import { Optional } from 'utility-types'

import { IB<PERSON><PERSON><PERSON>, UserR<PERSON> } from 'src/features/User'
import { AsyncState, IJWTPair } from 'src/types'

/**
 * access/refresh tokens received in exchange to Fractal code
 */
export interface IBaseAuthParams extends IJWTPair {
  role: UserRole
}

export interface ISignupParams extends IBaseAuthParams {
  /**
   * code pre-generated on our backend (sent in invite email),
   * indicating access to certain user role
   */
  code: string
}

export interface IAuthParams extends Optional<ISignupParams, 'code'> {}

export interface ILoginParams {
  role: string
  token: string // access_token
}

export interface IAuthResponse extends IJWTPair {
  redirect_url: string
}

// ---

export interface IUserResponse extends IBase<PERSON>ser {}

export interface IUser extends IUserResponse {}

export interface IUserState extends AsyncState {
  data: IUser
  authResponse?: IAuthResponse
}
