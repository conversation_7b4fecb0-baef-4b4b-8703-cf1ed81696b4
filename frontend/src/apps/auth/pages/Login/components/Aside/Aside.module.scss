.root {
  flex-basis: 50%;
  flex-grow: 1;
  padding: 16px;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 70px;
  height: 100%;
  padding: 70px 0 64px 64px;
  border-radius: 16px;
  background-image: url("./bg.svg");
  background-repeat: no-repeat;
  background-size: cover;
  // fallback until img loads; so text is always visible
  background-color: var(--th-clr-primary-700);
  color: var(--th-clr-white);
}

.quote {
  max-width: 492px;
  padding-right: 64px;
  font-style: italic;
  font-weight: 500;
  font-size: 18px;
  line-height: 28px;

  p {
    margin-bottom: 1em;
  }
}

.ui_preview {
  flex-grow: 1;
  border-radius: 20px 0 0 20px;
  background-image: url("./ui-preview.png");
  background-size: cover;
  background-repeat: no-repeat;
}
