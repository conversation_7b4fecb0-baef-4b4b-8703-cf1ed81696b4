@import "src/styles/mixins";

.card {
  position: relative;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 16px;
  padding: 16px;
  cursor: pointer;
}

.main {
  display: grid;
  gap: 4px;
}

.title {
  word-break: keep-all;
  font-weight: 600;
  font-size: 18px;
  line-height: 28px;
}

.description {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--th-clr-txt-secondary);
}

.arrow {
  color: var(--th-clr-gray-700);
}

.icon_holder {
  @include m-flex-center;
  @include m-bordered;

  height: 48px;
  width: 48px;

  align-self: flex-start;
  color: var(--th-clr-primary);
  position: relative;

  > * {
    position: absolute;
  }

  transition: box-shadow;
  --transition-duration: 0.1s;
}

.selectable {
  border: 2px solid transparent;
  border-radius: var(--th-border-radius);
  transition-duration: 0.4s;
  transition-property: border-color, background-color;

  &.selected {
    border-color: var(--th-clr-primary);
    background-color: var(--th-clr-bg-selected);
  }
}

.icon_slot {
  display: flex;

  transform: rotateY(0);
  transition: transform var(--transition-duration);
  transition-delay: var(--transition-duration);
}

.spinner {
  transform: rotateY(90deg);
  transition: transform var(--transition-duration);
  transition-delay: 0s;
}

.loading {
  .icon_holder {
    box-shadow: 0 0 10px 2px var(--th-clr-primary);
  }

  .icon_slot {
    transform: rotateY(90deg);
    transition-delay: 0s;
  }

  .spinner {
    transform: rotateY(0);
    transition-delay: var(--transition-duration);
  }
}
