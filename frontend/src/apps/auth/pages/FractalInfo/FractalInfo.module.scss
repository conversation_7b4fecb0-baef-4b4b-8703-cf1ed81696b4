.root {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 48px 32px;
}

.logo {
  width: 100%;
  margin-bottom: 12px;
}

.header {
  max-width: 592px;
  text-align: center;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: -24px;
  padding: 0 16px 24px;
}

.buttons {
  display: flex;
  justify-content: center;
}

.cards {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
  width: 100%;
}

.card {
  max-width: 320px;
  border: 1px solid var(--th-clr-gray-200);
  border-radius: var(--th-border-radius);
  padding: 24px;

  p {
    font-size: 14px;
    line-height: 20px;
    color: var(--th-clr-txt-secondary);
  }
}

.card_badge {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 20px;
}

.card_img {
  display: flex;
  justify-content: center;
  height: 200px;
  margin-bottom: 16px;
  border-radius: var(--th-border-radius);
  background-color: var(--th-clr-gray-200);

  img {
    display: block;
    width: auto;
    height: 100%;
  }
}

.card_title {
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
}

.alert_link {
  font-weight: 600;
  color: inherit;
}
