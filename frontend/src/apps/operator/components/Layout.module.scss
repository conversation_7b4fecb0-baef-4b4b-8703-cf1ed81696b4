.root {
  overflow: hidden;
  height: 100vh;
  min-width: 320px;

  margin-inline: auto;

  display: grid;
  grid-template-rows: 72px 1fr;

  background-color: var(--th-clr-bg-body);
}

.header {
  padding: 16px 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  overflow: hidden;
  border-bottom: 1px solid var(--th-clr-gray-300);
  background-color: var(--th-clr-bg-primary);
}

.logo {
  color: var(--th-clr-txt-primary);
  vertical-align: middle;
  --logo-clr-icon: var(--th-clr-primary);
}

.content {
  padding: 48px 80px;
  overflow: auto;
}
