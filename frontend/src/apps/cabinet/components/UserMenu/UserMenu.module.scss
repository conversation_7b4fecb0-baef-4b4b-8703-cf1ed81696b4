.root {
  z-index: 11;

  div[class$="-control"] {
    border: none;
    background-color: transparent;

    &:hover {
      background-color: var(--th-clr-gray-100);
    }
  }

  div[class$="-ValueContainer"] {
    padding: 4px;
  }

  div[class$="-menu"] {
    border: 1px solid var(--th-clr-gray-200);
    border-radius: 12px;
    box-shadow: 0 4px 6px -2px rgb(16 24 40 / 3%), 0 12px 16px -4px rgb(16 24 40 / 8%);
  }
}

.user {
  display: flex;
  gap: 0.75em;
  align-items: center;

  --usermenu-clr-secondary: var(--th-clr-primary-300);
}

.avatar {
  border: 1px solid var(--th-clr-gray-200);
  border-radius: 8px;

  &.avatar_placeholder {
    width: 30px;
    aspect-ratio: 1;
    object-fit: cover;
    overflow: hidden;
    padding: 2px;
    color: var(--th-clr-primary-50);
    background-color: var(--th-clr-primary-500);

    font-weight: bold;
    font-size: 0.7em;
    text-transform: uppercase;
    word-break: break-all;
    text-align: center;

    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.verified {
  position: relative;

  &::after {
    content: "";
    display: block;
    position: absolute;
    right: 0;
    bottom: 0;
    width: 12px;
    height: 12px;
    background-image: url("./verified.svg");
    background-size: contain;
  }
}

.info {
  font-size: 0.875rem;
}

.username {
  font-weight: 500;
}

.desc {
  color: var(--th-clr-txt-placeholder);
}

.btn_logout {
  width: 100%;
  justify-content: flex-start;
  font-size: 0.875rem;
  font-weight: 500;

  &:hover {
    --btn-clr-txt: transparent;
  }
}
