.description {
  max-width: 600px;
  text-align: center;
  font-size: 18px;
}

.form {
  width: 100%;
  max-width: 432px;
}

.finish_step_page {
  background: url("./img/bg-cells.png") no-repeat center 0;
  /* Stretch to show as much of background image as possible: */
  width: 100%;

  /* Responsive padding, based on static design.
   In design, padding is 160px – for screen of 1024px height.
   So for smaller screens keep this proportion, for larger ones – use static value. */
  padding-top: min(calc(160 / 1024 * 100vh), 160px);

  /* negate top padding provided by Layout */
  margin-top: calc(var(--layout-pad-block) * -1);
}
