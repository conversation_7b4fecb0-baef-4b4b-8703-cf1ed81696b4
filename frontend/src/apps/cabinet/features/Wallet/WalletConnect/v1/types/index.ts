import WalletConnect from '@walletconnect/client'

import {
  IWCLocalState,
  WCErrorEventListener,
  WCEventListener,
  WCEventName,
  WCSuccessEventListener,
} from './events'

export * from './events'

export interface IWalletConnectEnhanced extends WalletConnect {
  readonly state: IWCLocalState
  setConnectedAsset(assetSymbol: this['state']['assetSymbol']): void
  setActiveCallRequest(callRequest: this['state']['callRequest']): void
  setActiveSessionRequest(sessionRequest: this['state']['sessionRequest']): void
  // define strict event names and payloads, instead of string / any in original
  on<E extends WCEventName>(event: E, listener: WCEventListener<E>): void
  /**
   * To understand everything about design of WalletConnect@1,
   * just realize that their event manager doesn't even allow to unsubscribe a specific listener off particular event –
   * the feature which every event manager supports since the very beginning of Universe
   * */
  off(event: WCEventName): void
  // more convenient method in case you're only interested in successful scenario
  // and don't need those `if (error === null)` all around
  sub<E extends WCEventName>(
    event: E,
    onSuccess: WCSuccessEventListener<E>,
    onError?: WCErrorEventListener
  ): void
}
