import { BlockchainAsset, BlockchainMethod } from 'src/types'

/**
 * Custom state specifically dedicated for our interface needs.
 *
 * Probably it should be kept in app's reducer separately.
 * But it is way way more convenient to keep everything in one place, at connector instance,
 * rather than gather state by pieces all around app.
 */
export interface IWCLocalState {
  assetSymbol?: BlockchainAsset
  callRequest?: IWCEventPayloadCallRequest
  sessionRequest?: IWCEventPayloadSessionRequest
}

// ---

export type WCEventListener<E extends WCEventName> = (
  error: Error | null,
  payload: IWCEventPayloads[E]
) => void

export type WCSuccessEventListener<E extends WCEventName> = (
  payload: IWCEventPayloads[E]
) => void

export type WCErrorEventListener = (error: Error) => void

export interface IWCEventPayloads {
  connect: IWCEventPayloadConnect
  disconnect: IWCEventPayloadDisconnect
  session_request: IWCEventPayloadSessionRequest
  session_update: IWCEventPayload
  call_request: IWCEventPayloadCallRequest
  state_change: { event: 'state_change'; params: [IWCLocalState] }
}

export type WCEventName = keyof IWCEventPayloads

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IWCEventPayload {}

export interface PeerParams {
  peerId: string
  peerMeta: {
    description: string
    icons: string[]
    name: string
    url: string
  }
}

export interface IWCEventPayloadConnect extends IWCEventPayload {
  event: 'connect'
  params: [
    PeerParams & {
      chainId: number
      accounts: string[]
    }
  ]
}

export interface IWCEventPayloadDisconnect extends IWCEventPayload {
  event: 'disconnect'
  params: [{ message: string }]
}

export interface IWCSessionRequest extends PeerParams {
  chainId: number
}

export interface IWCEventPayloadSessionRequest extends IWCEventPayload {
  id: number
  method: 'session_request'
  params: [IWCSessionRequest]
}

interface IWCEventPayloadCallRequestBase extends IWCEventPayload {
  id: number
  jsonrpc: string
  method: string
  params: unknown[]
}

export interface IWCEventPayloadCallRequestTxn
  extends IWCEventPayloadCallRequestBase {
  method: BlockchainMethod.eth_sendTransaction
  params: [
    {
      data: string
      from: string
      to: string
      value: string
      gas: string
    }
  ]
}

export interface IWCEventPayloadCallRequestSwitchChain
  extends IWCEventPayloadCallRequestBase {
  method: BlockchainMethod.eth_switchChain
  params: [{ chainId: string }]
}

export type IWCEventPayloadCallRequest =
  | IWCEventPayloadCallRequestTxn
  | IWCEventPayloadCallRequestSwitchChain
