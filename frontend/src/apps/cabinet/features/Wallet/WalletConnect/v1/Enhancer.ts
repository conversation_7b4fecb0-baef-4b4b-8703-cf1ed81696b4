import WalletConnect from '@walletconnect/client'
import SocketTransport from '@walletconnect/socket-transport'

import { BlockchainAsset } from 'src/types'

import {
  IWCEventPayloadCallRequest,
  IWCEventPayloadSessionRequest,
  IWCLocalState,
  IWalletConnectEnhanced,
  WCErrorEventListener,
  WCEventName,
  WCSuccessEventListener,
} from './types'

/**
 * Enhance base class with extra local state, accordingly to our UI needs.
 * Put that state in session object, so instance can save and load it from localStorage along with everything else.
 */
export class WalletConnectEnhanced
  extends WalletConnect
  implements IWalletConnectEnhanced
{
  // @ts-expect-error Authors unlikely thought anybody will extend this class, so they made private just everything.
  // We DO need access to parent's internal toolkit.
  protected _state: IWCLocalState = this._getStorageSession()?.state ?? {}

  get state() {
    return this._state
  }

  protected _updateState(patch: Partial<typeof this.state>) {
    Object.assign(this._state, patch)
    // @ts-expect-error Don't care that it's private, we need access to those events
    this._eventManager.trigger({
      event: 'state_change',
      params: [{ ...this._state }],
    })
    // @ts-expect-error Need this too
    this._setStorageSession()
  }

  get session() {
    return {
      ...super.session,
      state: this._state,
    }
  }

  set session(value) {
    if (value === undefined) {
      return
    }
    const { state = {}, ...rest } = value
    super.session = rest
    this._state = state
  }

  setConnectedAsset(assetSymbol: BlockchainAsset | undefined) {
    this._updateState({ assetSymbol })
  }

  setActiveCallRequest(callRequest: IWCEventPayloadCallRequest | undefined) {
    this._updateState({ callRequest })
  }

  setActiveSessionRequest(
    sessionRequest: IWCEventPayloadSessionRequest | undefined
  ) {
    this._updateState({ sessionRequest })
  }

  sub<E extends WCEventName>(
    e: E,
    onSuccess: WCSuccessEventListener<E>,
    onError?: WCErrorEventListener
  ) {
    this.on(e, (err, payload) => {
      if (err === null) {
        onSuccess(payload)
      } else {
        onError?.(err)
      }
    })
  }

  /**
   * WC@1 is designed to establish session connection right upon instance creation.
   * You pass uri to constructor options, it is parsed, and with a parsed bridge url a socket transport initialized.
   *
   * That is, you cannot change uri after instance created (regardless that setter is public) –
   * it will have no effect, because transport will not be re-initialized.
   * You cannot like "start waiting for session request", because client is not designed for that.
   *
   * This complicates lots of things in terms of UI, because you have to delay connector instantiation
   * all the way down to the place where user enters uri.
   * Like keep an instance reference somewhere at very top, and pass a factory function through multiple components to some connection form.
   *
   * To don't mess with this bs, simply add transport re-initialization logic when uri changed.
   * Thanks to JS that "private" / "protected" members exist only at types level,
   * and you can access whatever you want if you absolutely need.
   */
  set uri(uri: string) {
    if (this.uri === uri) return

    super.uri = uri
    const wc = this
    /**
     * Copied from {@link https://github.com/WalletConnect/walletconnect-monorepo/blob/c94c1d608e75ef7f0e77572a8627d9412ade24c3/packages/clients/core/src/index.ts#L150-L164}
     */
    /* eslint-disable no-underscore-dangle, @typescript-eslint/ban-ts-comment */
    // @ts-ignore
    wc._transport = new SocketTransport({
      protocol: wc.protocol,
      version: wc.version,
      url: wc.bridge,
      subscriptions: [wc.clientId],
    })
    // @ts-ignore
    wc._initTransport()
    // @ts-ignore
    wc._subscribeToSessionRequest()
    /* eslint-enable */
  }
}
