import {
  Abstract<PERSON>hainId,
  BlockchainAddress,
  BlockchainAsset,
  BlockchainMethod,
} from 'src/types'

export interface IConnectorOptions {
  uri?: string

  /**
   * StorageId is required, because we need to store a separate session per each Polity wallet.
   * Without it, with WC@1 session in localStorage will be overwritten every time.
   * With WC@2m there multiple sessions stored anyway, but we need an identifier to find on related to specific wallet.
   */
  storageId: string
}

// ---

export interface ICallRequestTxnParams {
  data: string
  from: string
  to: string
  value: string
  /* gas MAY be absent for SOME dApps.
   * Like, Instadapp provides some "maxFeePerGas" and "maxPriorityFeePerGas" instead of it – whatever wtf is that.
   * All these fields have to just passed to backend and dealt with there. */
  gas?: string
  maxFeePerGas?: string
  maxPriorityFeePerGas?: string
}

export interface ICallRequestSwitchChainParams {
  chainId: string // hexadecimal; @see https://eips.ethereum.org/EIPS/eip-3326
}

// ---

export interface IWCCallRequestAbstract {
  id: number
  topic: string
  method: string
  params: unknown
}

export interface IWCCallRequestTxn extends IWCCallRequestAbstract {
  method: BlockchainMethod.eth_sendTransaction
  params: ICallRequestTxnParams
}

export interface IWCCallRequestSwitchChain extends IWCCallRequestAbstract {
  method: BlockchainMethod.eth_switchChain
  params: ICallRequestSwitchChainParams
}

/**
 * @see https://specs.walletconnect.com/2.0/blockchain-rpc/ethereum-rpc#personal_sign
 */
export interface IWCCallRequestPersonalSign extends IWCCallRequestAbstract {
  method: BlockchainMethod.personalSign
  params: [string, string] /* [message_to_sign, blockchain_address] */
}

export type IWCCallRequest =
  | IWCCallRequestTxn
  | IWCCallRequestSwitchChain
  | IWCCallRequestPersonalSign

// ---

export interface IWCPeerMeta {
  name: string
  description: string
  url: string
  icons: string[]
}

export interface IWCSessionRequest {
  id: number
  chainId: AbstractChainId
  peerMeta: IWCPeerMeta
  chains: string[]
  methods: string[]
  events: string[]
}

export interface IWCFacadeState {
  assetSymbol: BlockchainAsset | null
  callRequest: IWCCallRequest | null
  sessionRequest: IWCSessionRequest | null
}

export type WCClientUpdateListener = () => void

export interface IWCApproveSessionParams {
  id: number
  accounts: BlockchainAddress[]
  asset: BlockchainAsset
  chains: string[]
  methods: string[]
  events: string[]
}

export interface IWCFacade {
  get state(): IWCFacadeState
  get account(): BlockchainAddress | undefined
  get connected(): boolean
  get chainId(): AbstractChainId | undefined
  get peerMeta(): IWCPeerMeta | undefined

  approveSession(params: IWCApproveSessionParams): Promise<void>
  rejectSession(params: { id: number; reason?: string }): Promise<void>
  updateSession(params: {
    requestId: number
    chainId: AbstractChainId
  }): Promise<void>
  disconnect(reason?: string): Promise<void>

  awaitSessionRequest(uri?: string): Promise<IWCSessionRequest>

  approveRequest(req: {
    id: number
    topic: string
    result: string
  }): Promise<void>
  rejectRequest(req: { id: number; topic: string; msg: string }): Promise<void>

  teardown(): void

  onUpdate(cb: WCClientUpdateListener): void

  setURI(uri: string): Promise<void>
}
