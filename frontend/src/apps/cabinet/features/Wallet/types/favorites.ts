import { BlockchainAsset } from 'src/types'

export interface IFavoriteWalletAssetRaw {
  address: string
  asset_symbol: BlockchainAsset
  name: string
}

export interface IFavoriteWalletAsset extends IFavoriteWalletAssetRaw {
  is_static: boolean
}

export interface IFavoriteAssetsResponse {
  dynamic_addresses: IFavoriteWalletAssetRaw[] | null
  static_addresses: IFavoriteWalletAssetRaw[] | null
}

export interface ICreateFavoriteAssetPayload {
  address: string
  asset_symbol: string
  name: string
}

export interface IRemoveFavoriteAssetPayload {
  address: string
  asset_symbol: string
}
