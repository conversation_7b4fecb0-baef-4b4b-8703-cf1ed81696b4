import { BlockchainAsset } from 'src/types'

import { WalletID, WalletProvider } from './base'

export interface IWalletAsset {
  asset_symbol: BlockchainAsset
  balance: string
  balance_usd: string
}

export interface IWalletAssetsListResponseRaw {
  assets: IWalletAsset[]
  balance_usd: string
}

export interface IWalletAssetsListResponse
  extends IWalletAssetsListResponseRaw {
  supported_assets: BlockchainAsset[]
}

export interface IWalletAssetPayload {
  provider: WalletProvider
  asset_symbol: BlockchainAsset
  request_uuid: WalletID
}

export interface IWalletDefiAsset {
  address: string
  asset_symbol: string
  balance: number
  balance_usd: number
  chain_id: string
  display_name: string
  icon: string
  name: string
  price: number // in USD
}
