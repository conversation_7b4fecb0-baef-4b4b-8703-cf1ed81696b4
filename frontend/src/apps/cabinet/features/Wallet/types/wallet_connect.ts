import { BlockchainAsset, BlockchainMethod } from 'src/types'

import { BlockchainAddress, WalletID } from './base'

export interface IWCCallPayloadTxn {
  asset: BlockchainAsset
  request_id: WalletID
  method: BlockchainMethod.eth_sendTransaction
  data: {
    data: string
    from: BlockchainAddress
    to: BlockchainAddress
    value: string
    // See description in `ICallRequestTxnParams` interface
    gas?: string
    maxFeePerGas?: string
    maxPriorityFeePerGas?: string
  }
}

export interface IWCCallResponseTxn {
  status: string
  transaction_hash: string
  transaction_id: string
}

/**
 * @see https://specs.walletconnect.com/2.0/blockchain-rpc/ethereum-rpc#personal_sign
 */
export interface IWCCallPayloadSign {
  request_id: WalletID
  asset: BlockchainAsset
  method: BlockchainMethod.personalSign
  data: {
    from_address: string
    sign_data: string
  }
}

export interface IWCCallResponseSign {
  status: string // seemingly, it's always "SIGN_COMPLETED"
  transaction_hash: string // the actual signature, to be passed back to WC API
  transaction_id: string
}
