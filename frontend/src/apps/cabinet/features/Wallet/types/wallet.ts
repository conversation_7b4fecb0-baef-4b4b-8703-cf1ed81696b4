import { AgreementType, DateTime, UUID } from 'src/types'

import { WalletID, WalletProvider, WalletStatus } from './base'

interface IWalletBase {
  wallet_name: string
  status: WalletStatus
  created_at: DateTime
}

// ---

export interface IWalletListItemResponse extends IWalletBase {
  request_id: WalletID
  balance_usd?: string
}

export interface IWalletListItem
  extends Omit<IWalletListItemResponse, 'request_id'> {
  address: WalletID
  type: WalletProvider
}

// ---

export interface IWallet extends IWalletBase {
  id: number
  address: WalletID
  type: WalletProvider
  archived_at: DateTime
}

// ---

export interface ICreateWalletParams {
  provider: WalletProvider
  name: string
}

export interface ICreateWalletPayload {
  address: WalletID
  request_id: UUID
  type: WalletProvider
  wallet_name: string
}

export interface ISignWalletAgreementPayload {
  avatar: string
  agreementType: AgreementType
}
