.root {
  &.error {
    .integer,
    .decimal {
      color: var(--th-clr-error);
    }
  }
}

.value { /* none */ }

.title {
  color: var(--th-clr-gray-700);
  font-size: 1rem;
  line-height: 1.5;
}

.integer {
  font-size: 3rem;
  font-weight: 600;
  line-height: 1.25;
  color: var(--th-clr-txt-primary);
}

.decimal {
  font-size: 1.875rem;
  font-weight: 500;
  line-height: 1.25;
  color: var(--th-clr-txt-secondary);

  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}
