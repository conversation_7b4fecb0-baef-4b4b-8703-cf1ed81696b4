import { AgreementType } from 'src/types'

import { IInvestorAvatar } from './base'

export interface IAgreementFormQueryParams {
  type?: AgreementType
  user?: string // avatar id
}

interface IBaseAgreementPayload {
  agreementType: AgreementType
  /* By design, users on platform MUST use their element.io ids as usernames.
   * Thus, 'name' is a unique id withing system. */
  avatar: IInvestorAvatar['name']
}

export interface ISafeheronAcquisitionAgreementPayload
  extends IBaseAgreementPayload {
  agreementType: AgreementType.SafeheronAcquisition
  investment: number
  fee: number
}

export interface INodeRentAgreementPayload extends IBaseAgreementPayload {
  agreementType: AgreementType.NodeAcquisition
  fee: number
  pdfFile: File
}

// TODO: not implemented in PoC app
export interface IManagedPortfolioAgreementPayload
  extends IBaseAgreementPayload {
  agreementType: AgreementType.ManagedPortfolio
}

export type IAgreementPayload =
  | ISafeheronAcquisitionAgreementPayload
  | INodeRentAgreementPayload
  | IManagedPortfolioAgreementPayload
