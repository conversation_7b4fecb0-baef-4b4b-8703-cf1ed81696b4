import { ReactNode } from 'react'

type FlexAlignOptions = 'start' | 'stretch' | 'center'

export interface IPageProps {
  className?: string
  hero?: ReactNode
  title?: ReactNode
  description?: ReactNode
  goBack?:
    | true
    | string
    // TODO: probably would also need a `position` option, to put btn either above title or left to it
    | { link?: string; label: ReactNode }
  stickyHeader?: boolean
  alignHeader?: FlexAlignOptions
  alignContent?: FlexAlignOptions
  gap?: number | string
  pushdown?: boolean // idk how to name this properly
  children: ReactNode
}
