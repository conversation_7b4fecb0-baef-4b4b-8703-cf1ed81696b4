import { HTMLAttributes, ReactElement, ReactHTML, ReactNode } from 'react'

export interface IListProps<T> extends IStyled {
  tag?: keyof ReactHTML
  data?: T[]
  renderItemContent?: (x: T, i: number) => ReactNode
  getItemProps?: (x: T, i: number) => IDefaultItemExtraProps
  renderItem?: (x: T, props: IDefaultItemProps, i: number) => ReactElement
  empty?: ReactNode
  emptyIcon?: ReactNode
  loading?: boolean
  elevate?: boolean

  gap?: number | string
  direction?: 'row' | 'col'
  wrap?: boolean
  reverse?: boolean

  defaultItemClickable?: boolean
}

export type IDefaultItemProps = HTMLAttributes<HTMLElement> & {
  noDefaultStyles?: true
}
export type IDefaultItemExtraProps = Omit<IDefaultItemProps, 'children'>
