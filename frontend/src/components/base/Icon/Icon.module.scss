@value box_module: "../../layouts/Box/Box.module.scss";

.icon {
  composes: box_params from box_module;

  display: inline-flex;

  --box-size-def: var(--th-icon-size);
  --icon-size: var(--box-size);
  height: var(--icon-size);
  width: var(--icon-size); // aspect-ratio doesn't work on SVG elements

  > svg {
    width: 100%;
    height: 100%;
  }
}

.box {
  composes: bordered from box_module;
  --box-pad-def: 2px;
  padding: var(--box-pad);
  border-radius: min(25%, var(--th-border-radius));
}
