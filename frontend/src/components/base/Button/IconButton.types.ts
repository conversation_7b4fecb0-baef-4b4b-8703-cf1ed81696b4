import { ReactChild } from 'react'

import { IBoxSize } from '../../layouts/Box'
import { IIconProps } from '../Icon'
import { ITooltipProps } from '../Tooltip'

import { IButtonProps } from './Button.types'

export type IIconTooltipProps = Omit<ITooltipProps, 'autoWrap' | 'children'>

export interface IIconButtonProps
  extends Omit<IButtonProps, 'title' | 'children' | 'size'> {
  size?: IBoxSize | IIconProps['size']
  title?: ReactChild | IIconTooltipProps
  icon: IIconProps['type']
  pending?: boolean
}
