import React, { ReactNode } from 'react'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory } from '@storybook/react'

import { IconButton as Component } from './IconButton'

export default {
  title: createTitle(),
  component: Component,
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <div style={{ display: 'grid', gap: 16 }}>
    <Section title="Fixed sizes">
      <div style={{ display: 'flex', gap: 16, alignItems: 'start' }}>
        <Caption title="xs">
          <Component {...args} size="xs" />
        </Caption>
        <Caption title="sm">
          <Component {...args} size="sm" />
        </Caption>
        <Caption title="md">
          <Component {...args} size="md" />
        </Caption>
        <Caption title="lg">
          <Component {...args} size="lg" />
        </Caption>
        <Caption title="xl">
          <Component {...args} size="xl" />
        </Caption>
        <Caption title="2xl">
          <Component {...args} size="2xl" />
        </Caption>
      </div>
    </Section>

    <hr style={{ width: '100%' }} />

    <Section title="Custom size (set through control panel)">
      <Component {...args} />
    </Section>
  </div>
)

export const Default = Template.bind({})

Default.args = {
  icon: 'copy',
  size: '6rem',
}

Default.argTypes = {
  disabled: {
    type: 'boolean',
  },
}

// ---

function Section({
  children,
  title,
}: {
  children: ReactNode
  title: ReactNode
}) {
  return (
    <div style={{ display: 'grid', gap: 12 }}>
      <div>{title}</div>
      <div>{children}</div>
    </div>
  )
}

function Caption({
  children,
  title,
}: {
  children: ReactNode
  title: ReactNode
}) {
  return (
    <div style={{ display: 'grid', gap: 4, justifyItems: 'center' }}>
      <div>{title}</div>
      <div>{children}</div>
    </div>
  )
}
