import {
  UseRowSelectInstanceProps,
  UseRowSelectOptions,
  UseRowSelectRowProps,
  UseRowSelectState,
} from 'react-table'

declare module 'react-table' {
  interface TableOptions<D extends object> extends UseRowSelectOptions<D> {}

  interface Row<D extends object> extends UseRowSelectRowProps<D> {}

  interface TableInstance<D extends object>
    extends UseRowSelectInstanceProps<D> {}

  interface TableState<D extends object> extends UseRowSelectState<D> {}

  interface Hooks<D extends object> extends UseRowSelectHooks<D> {}
}
