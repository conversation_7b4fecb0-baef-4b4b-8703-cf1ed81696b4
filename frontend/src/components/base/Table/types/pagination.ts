import { ReactElement } from 'react'
import { UsePaginationInstanceProps, UsePaginationState } from 'react-table'

/**
 * Props of Pagination component
 * Takes everything that provides react-table hook itself,
 * plus our custom options.
 */
export interface IPaginationProps<Row extends object>
  extends ICustomPaginationProps,
    UsePaginationInstanceProps<Row>,
    UsePaginationState<Row> {}

export interface ICustomPaginationProps {
  pageSizesList?: number[]
  showPageSizeSelector?: boolean
  renderPageCounter?: (props: {
    current: number
    total: number
  }) => ReactElement
  showPagination?: boolean
}
