/* stylelint-disable selector-class-pattern */
:global {
  .react-tabs__tab-list {
    margin: 0 0 1rem;
    padding: 6px;

    display: flex;
    gap: 0.5rem;

    background-color: var(--th-clr-gray-100);
    max-width: max-content;

    border-radius: 8px;
    border: none;
  }

  .react-tabs__tab {
    bottom: 0; // override default
    border: none;
    border-radius: 6px;

    font-size: 1rem;
    line-height: 1.5;

    padding: 10px 14px;
    font-weight: 600;
    color: var(--th-clr-txt-placeholder);

    transition-property: color, background-color;
    transition-duration: 300ms;

    &--selected {
      color: var(--th-clr-txt-primary);
      background: var(--th-clr-bg-primary);
      box-shadow: 0 1px 2px 0 rgb(16 24 40 / 6%), 0 1px 3px 0 rgb(16 24 40 / 10%);
    }

    &--disabled {
      background: var(--th-clr-gray-200);
      color: var(--th-clr-gray-400);
    }

    // override default - remove this pseudoelement
    &:focus::after {
      content: none;
    }
  }
}
/* stylelint-enable selector-class-pattern */
