import React from 'react'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory } from '@storybook/react'

import { Loader as Component } from './Loader'

export default {
  title: createTitle(),
  component: Component,
  parameters: {
    controls: { expanded: true },
  },
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = ({ size, ...args }) => (
  <Component
    {...args}
    size={size === undefined || isNaN(+size) ? size : +size}
  />
)

export const Default = Template.bind({})

Default.args = {
  size: '1em',
  width: 7,
}

Default.argTypes = {
  size: {
    type: 'string',
    description: 'Use numbers, or CSS units like "1em"',
  },
  width: {
    type: 'number',
  },
}
