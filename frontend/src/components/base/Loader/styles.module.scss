@import "src/styles/mixins";

.spinner {
  --spinner-size: 1em;
  --spinner-color: var(--th-clr-primary);

  width: var(--spinner-size);
  height: var(--spinner-size);
  aspect-ratio: 1;
  border-radius: 50%;

  @include m-flex-center($inline: true);

  .icon {
    fill: var(--spinner-color);
    animation: rotate 1.8s ease-in-out infinite;

    width: 100%;
    height: 100%;
    aspect-ratio: 1;
  }

  .circle {
    stroke: var(--spinner-color);
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
