import { CSSProperties, ReactChild, ReactElement, ReactNode } from 'react'
import {
  FieldValues,
  KeepStateOptions,
  SubmitErrorHandler,
  SubmitHandler,
  UseFormHandleSubmit,
  UseFormProps,
  UseFormReturn,
} from 'react-hook-form'

import { IButtonProps } from '../../Button'

export interface IUseFormProps<
  TFieldValues extends FieldValues = FieldValues,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  TContext = any
> extends UseFormProps<TFieldValues, TContext> {
  onSubmit?: (
    data: Parameters<SubmitHandler<TFieldValues>>[0],
    event: Parameters<SubmitHandler<TFieldValues>>[1],
    form: UseFormReturn<TFieldValues>
  ) => ReturnType<SubmitHandler<TFieldValues>>
  onReset?: (
    // pass out current form data, in case you need to do smth with it before it's lost
    data: Parameters<SubmitHandler<TFieldValues>>[0],
    form: UseFormReturn<TFieldValues>
  ) => void
  onSubmitError?: (err: Error) => void
  onValidationError?: SubmitErrorHandler<TFieldValues>
  reset?: KeepStateOptions
}

export interface IUseFormReturn<TFieldValues extends FieldValues = FieldValues>
  extends UseFormReturn<TFieldValues> {
  onSubmit: ReturnType<UseFormHandleSubmit<TFieldValues>>
  onReset: () => void
}

export interface IBaseFormProps<TFieldValues extends FieldValues = FieldValues>
  extends IStyled,
    IUseFormProps<TFieldValues> {
  id?: string
  debug?: boolean
  children?: ReactNode | ((form: IUseFormReturn<TFieldValues>) => ReactNode)
}

export type FormButtonProps =
  | ReactChild
  | Omit<IButtonProps, 'type' | 'disabled'>

export interface IFormControlsProps {
  isResettable?: boolean
  isSubmitting?: boolean
  allowSubmit?: boolean
  onlySubmitChanges?: boolean
  btnReset?: FormButtonProps
  btnSubmit?: FormButtonProps
  buttonsAlign?: CSSProperties['justifyContent']
  buttonsLayout?: CSSProperties['gridAutoColumns']
  renderSubmitting?: (defaultLoader: ReactElement) => ReactNode
}

interface FormChildrenRenderer<TFieldValues extends FieldValues = FieldValues> {
  (
    form: UseFormReturn<TFieldValues>,
    params: {
      controls: ReactElement | undefined
      error: ReactElement
      externalError: ReactElement | undefined
    }
  ): ReactNode
}

export interface IFormContentProps<
  TFieldValues extends FieldValues = FieldValues
> extends Omit<IFormControlsProps, 'isSubmitting'> {
  error?: unknown
  showDefaultControls?: boolean
  children?: ReactNode | FormChildrenRenderer<TFieldValues>
}

export interface IFormProps<TFieldValues extends FieldValues = FieldValues>
  extends Omit<IBaseFormProps<TFieldValues>, 'reset' | 'children'>,
    Omit<IFormContentProps<TFieldValues>, 'isResettable'> {
  reset?: true | KeepStateOptions
}
