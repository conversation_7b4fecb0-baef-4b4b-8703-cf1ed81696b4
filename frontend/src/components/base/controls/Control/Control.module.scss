.control {
  --control-align-label: start;
  --control-align-input: start;

  display: grid;
  gap: 0.375rem; // gap between body (label + input) and footer (error / hint). May NOT vary.
  position: relative;

  &_inline {
    display: inline-grid;
  }
}

.content {
  display: grid;
  gap: 0.375rem; // gap between label and input; may vary arbitrary

  > :not(.label) {
    align-self: var(--control-align-input);
  }
}

.control_txt_params {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.43;
}

@mixin footer-animation {
  &.footer_empty {
    line-height: 0;
  }

  > * {
    line-height: inherit;
  }

  transition: line-height 0.1s;
}

.footer {
  composes: control_txt_params;
  display: grid;

  @include footer-animation;
}

.hint {
  composes: control_txt_params;
  color: var(--th-clr-txt-hint);
}

.error {
  composes: control_txt_params;
  color: var(--th-clr-error);
}

.label {
  composes: control_txt_params;
  font-weight: 500;
  color: var(--th-clr-txt-secondary);
  align-self: var(--control-align-label);
}
