import { ReactNode } from 'react'
import { DropzoneOptions, FileRejection } from 'react-dropzone'

export interface IFileInputProps
  extends Pick<DropzoneOptions, 'maxSize' | 'minSize'>,
    IStyled {
  accept?: string | DropzoneOptions['accept']
  name?: string
  onChange?: (file: File) => void
  onError?: (rejection: FileRejection) => void
  placeholder?: ReactNode
  renderFiles?: (props: IFileInputFilesRendererProps) => ReactNode
  unstyled?: boolean
}

export interface IFileInputFilesRendererProps {
  open(): void
  files: File[]
}
