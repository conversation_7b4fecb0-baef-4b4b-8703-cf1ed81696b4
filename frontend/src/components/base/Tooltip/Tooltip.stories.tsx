import React from 'react'

import createTitle from '@parachutehome/create-title.macro'
import { ActionType, Placement } from '@rc-component/trigger/lib/interface'
import { ComponentMeta, ComponentStory } from '@storybook/react'

import { Tooltip as Component } from './Tooltip'

export default {
  title: createTitle(),
  component: Component,
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <Component {...args}>
    <button>hover it</button>
  </Component>
)

export const Default = Template.bind({})

Default.args = {
  overlay: 'Overlay content',
}

const trigger: ActionType[] = ['hover', 'focus', 'click', 'contextMenu']
const placements: Placement[] = [
  'top',
  'topLeft',
  'topRight',
  'bottom',
  'bottomLeft',
  'bottomRight',
  'left',
  'leftTop',
  'leftBottom',
  'right',
  'rightTop',
  'rightBottom',
]

Default.argTypes = {
  children: {
    table: {
      disable: true,
    },
  },
  autoWrap: {
    table: {
      disable: true,
    },
  },
  trigger: {
    type: {
      name: 'enum',
      value: trigger,
    },
  },
  placement: {
    type: {
      name: 'enum',
      value: placements,
    },
  },
  showArrow: {
    type: 'boolean',
  },
}

export const Showcase: ComponentStory<typeof Component> = args => (
  <div
    style={{
      display: 'grid',
      columnGap: '7rem',
      rowGap: '5rem',
      gridTemplateColumns: 'repeat(3, max-content)',
      padding: '4rem',
    }}
  >
    {placements.map(placement => (
      <Component
        key={placement}
        {...args}
        placement={placement}
        overlay={placement}
        visible
        autoWrap={false}
      >
        <div
          style={{
            border: '1px solid lightgrey',
            padding: 8,
            height: '3em',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          placement={placement}
        </div>
      </Component>
    ))}
  </div>
)

Showcase.args = {
  showArrow: true,
}

Showcase.argTypes = {
  autoWrap: {
    table: {
      disable: true,
    },
  },
  showArrow: {
    type: 'boolean',
  },
}
