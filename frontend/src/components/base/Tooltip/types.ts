import { ReactHTML } from 'react'

import { TooltipProps } from 'rc-tooltip/lib/Tooltip'

// ---

export interface ITooltipProps extends TooltipProps {
  /**
   * Tooltip component works by adding a ref to child component.
   * Which requires certain support from developer, i.e. refs forwarding in your func components.
   * To simplify this, just wrap children in a plain node –
   * in quite many cases it is suitable and won't break styling.
   * If it does – then take care of refs forwarding, or style the wrapper yourself.
   *
   * Without it, rc-tooltip is smart enough to automatically fallback to `findDOMNode` –
   * but thanks, <PERSON>act, it is deprecated in StrictMode, so can't use this approach.
   */
  autoWrap?: boolean | keyof ReactHTML
}
