/**
 * See rc-tooltip/assets/bootstrap.css
 */
@mixin tooltip-arrow-color($positions, $sides, $color) {
  @each $pos in $positions {
    @each $side in append($sides, "") {
      &-placement-#{$pos}#{$side} &-arrow {
        border-#{$pos}-color: $color;
        filter: drop-shadow(0 0 2px var(--rc-tooltip-clr-shadow));
      }
    }
  }
}

@mixin set-tooltip-color($color) {
  & {
    &-inner {
      background-color: $color;
    }

    @include tooltip-arrow-color((top, bottom), (Left, Right), $color);
    @include tooltip-arrow-color((left, right), (Top, Bottom), $color);
  }
}

/* stylelint-disable selector-class-pattern */
:global {
  .rc-tooltip {
    --rc-tooltip-clr-bg: var(--th-clr-bg-primary);
    --rc-tooltip-clr-txt: var(--th-clr-txt-primary);
    --rc-tooltip-clr-shadow: var(--th-clr-primary-200);

    opacity: 1;

    // Make sure tooltips are always below modal overlay
    z-index: calc(#{var(--modal-overlay-zindex)} - 1);

    &-inner {
      box-shadow: 0 0 8px 0 var(--rc-tooltip-clr-shadow);
      color: var(--rc-tooltip-clr-txt);
      min-height: auto; // override an original value, which is why even there?
    }

    @include set-tooltip-color(var(--rc-tooltip-clr-bg));
  }
}

/* stylelint-enable */
