@import "src/styles/mixins";

$container-h-pad: 8px;

.address_container {
  --ba-clr-primary: var(--th-clr-primary);
  --ba-clr-secondary: var(--th-clr-white);
  --ba-clr-bg-hover: var(--th-clr-primary-50);

  &.invert {
    --ba-clr-primary: var(--th-clr-white);
    --ba-clr-secondary: var(--th-clr-primary);
    --ba-clr-bg-hover: var(--th-clr-primary-400);
  }

  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: 1fr auto;
  align-items: center;
  gap: 0.8rem;

  max-width: max-content;

  /* Truncated addr is short enough, shouldn't shrink any further */
  &.trunc {
    width: max-content;

    .copied_msg {
      text-align: center;
    }
  }

  height: 32px;
  box-sizing: content-box;

  padding: 4px $container-h-pad;
  border: 1px solid var(--th-border-color);
  border-radius: 8px;

  background-color: var(--ba-clr-secondary);

  overflow: hidden;

  transition-property: color, background-color;
  transition-duration: 300ms;

  &.copy {
    &:has(.address_text:hover) {
      &:not(.copied) {
        background-color: var(--ba-clr-bg-hover);
      }

      .btn_copy {
        --btn-clr-txt: var(--ba-clr-bg-hover);
      }
    }

    .address_text {
      cursor: copy;
    }

    &.copied {
      .address_text {
        visibility: hidden;
      }

      .copied_msg {
        display: block;
      }

      background-color: var(--ba-clr-primary);
    }
  }

  position: relative;
}

.address_text {
  color: var(--ba-clr-primary);
  position: relative;

  @include m-text-overflow;
  max-height: 100%;
}

.btn_copy {
  --btn-clr-primary: var(--ba-clr-primary);
  --btn-clr-txt: var(--ba-clr-secondary);
}

/*
 * Use monospace font for address, so that multiple addresses in a column
 * will all have same width regardless of their text, which looks way better.
 */
.chars_mono {
  font-family: monospace;
}

.copied_msg {
  position: absolute;
  left: $container-h-pad;
  font-weight: bold;
  font-size: 0.8em;
  padding-inline: 4px 32px;
  color: var(--ba-clr-secondary);
  display: none;
}
