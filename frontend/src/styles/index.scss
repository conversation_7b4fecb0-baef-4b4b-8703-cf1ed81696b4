@import "./fonts";
@import "~react-toastify/dist/ReactToastify.css";

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  color: var(--th-clr-txt-primary);
  background-color: var(--th-clr-bg-body);
  font-family: "Inter", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--th-clr-txt-primary);
  text-decoration: none;
}

textarea {
  color: var(--th-clr-txt-primary);
  padding: 8px;
}

ol,
ul {
  padding: 0;
  list-style: none;
}

fieldset {
  border: none;
  padding: 0;
  margin: 0;
}
