export type T_ID = string | number
export type HasId = { id: T_ID }
export type TagType = string
export type ListTagId = 'LIST'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type AnyTagType = any

export type RTQTag<T extends TagType = AnyTagType, ID extends T_ID = T_ID> = {
  id: ID
  type: T
}

export type ProvidedID<List extends boolean = false> =
  | undefined
  | (List extends false ? T_ID | HasId : Array<T_ID> | Array<HasId>)

export type ProvidedTagResult<
  T extends TagType,
  ID extends ProvidedID
> = ID extends undefined ? [] : [RTQTag<T>]

export interface TypedSingleTagProvider<T extends TagType> {
  <ID extends ProvidedID>(id: ID): ProvidedTagResult<T, ID>
}

export interface TypedTagListProvider<T extends TagType = TagType> {
  (xs: ProvidedID<true>): RTQTag<T>[]
}

export interface TypedTagFunc<T extends TagType> {
  <ID extends T_ID>(id: ID): RTQTag<T, ID>

  list(): RTQTag<T, ListTagId>

  toString(): T

  providesOne: TypedSingleTagProvider<T>
  providesList: TypedTagListProvider<T>
}

export type TagTypeOrFn<T extends TagType = AnyTagType> = T | TypedTagFunc<T>
