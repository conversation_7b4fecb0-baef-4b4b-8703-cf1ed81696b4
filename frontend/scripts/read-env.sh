#!/bin/bash

APP_NAME=$1

if [ -z $APP_NAME ]; then
  EXT=""
  LOG_NAME="default"
else
  EXT=".$APP_NAME"
  LOG_NAME=$APP_NAME
fi

ENV_FILE="./.env$EXT"
ENV_FILE_LOCAL="$ENV_FILE.local"

echo "reading env files for [$LOG_NAME]"

set -a
if [ -f $ENV_FILE ]; then
  source $ENV_FILE
  echo "> read $ENV_FILE"
fi

if [ -f $ENV_FILE_LOCAL ]; then
  source $ENV_FILE_LOCAL
  echo "> read $ENV_FILE_LOCAL"
fi
set +a
