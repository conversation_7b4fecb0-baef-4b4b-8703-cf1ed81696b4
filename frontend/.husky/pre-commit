#!/bin/sh

HUSKY_ROOT=$(dirname "$0")
FE_ROOT="$HUSKY_ROOT/.."

. "$HUSKY_ROOT/_/husky.sh"

# Go to "frontend" folder.
# Git hooks run from repo root. To run npm scripts, should go to where they are installed.
cd $FE_ROOT

npx lint-staged

# Do not put this into lint-staged config,
# because it will run tsc for each file separately, with file as argument.
# And in that case, for some weird reason, tsconfig won't be used:
# @link https://stackoverflow.com/questions/44676944/how-to-compile-a-specific-file-with-tsc-using-the-paths-compiler-option
#
# This means, ts will report errors even for non-staged files.
# But if you have ts errors somewhere, your app won't compile anyway,
# so unlikely you'll ever won't to do commit in that state.
npx tsc --noEmit

# lint json-server code
cd server
npx lint-staged
npx tsc --noEmit
