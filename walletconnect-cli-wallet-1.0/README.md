## Example standalone wallet

To run do in root directory you need to have nodejs installed and run:

1) `npm i`
2) `npm run start -- -m dfns -u 123 -i 123 -w hhhh`

Command line arguments:

`-m` - mode (DFNS/Safeheron)

`-u` - user id

`-i` - wallet id to use (request id)

`-w` - walletconnect uri from QR code

`-b` - BWI Url, must be in quotation marks(`"http..."`), by default `localhost:8083`

example:
`npm run start -- -m dfns -u ************************************ -i 25398f6d-9079-48a4-ada9-5b1d38770181 -b http://**************:8083 -w "wc:14d6a79f-4da8-4bc2-a275-e3c7b063dfa1@1?bridge=https%3A%2F%2Fb.bridge.walletconnect.org&key=f5e42f48b0ad91fb4542b40f16fc623c6a03fdf3036c656baac733fcba6474d7"`
