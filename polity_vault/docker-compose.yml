version: "3.9"

services:
  postgres-polity-vault:
    image: postgres:12.0-alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - .env
    ports:
      - "5432:5432"

  redis:
    image: 'bitnami/redis:latest'
    env_file:
      - .env
    expose:
      - '6379'
    environment:
      - REDIS_PASSWORD=26ecfc64-50d1-4317-b6a2-2f39aa521f23


  polity-vault:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    volumes:
      - '.:/app'
    ports:
      - "8000:8000"
    depends_on:
      - postgres-polity-vault
      - redis