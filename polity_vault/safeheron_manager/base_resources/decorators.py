import logging
import time

from botocore.exceptions import Client<PERSON>rror, ParamValidationError

logger = logging.getLogger(__name__)

RETRY_TIMES = 300
RETRY_DELAY = 1


def retry(func):
    def wrapper(*args, **kwargs):
        attempts = RETRY_TIMES
        state = 1
        while attempts > 0 and state:
            state = 0 if func(*args, **kwargs) else 1
            attempts -= 1
            if not state: return True
            time.sleep(RETRY_DELAY)
        return False

    return wrapper


def resolve_botocore_errors(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ClientError as e:
            logger.error("Client error:", e)
        except ParamValidationError as e:
            logger.error("Param Validation Error:", e)
        except IndexError as e:
            logger.error("Index error:", e)
        except Exception as e:
            logger.error("Unexpected error:", e)

    return wrapper
