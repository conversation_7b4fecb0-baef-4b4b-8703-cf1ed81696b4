POSTGRES_USER=polity_vault_infrastructure
POSTGRES_DB=polity_vault_infrastructure
POSTGRES_PASSWORD=qwerty
POSTGRES_HOST=postgres-polity-vault
POSTGRES_PORT=5432
SECRET_KEY=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_PASSWORD=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SETTINGS_MODULE='polity_vault.settings.dev'
DEBUG=True
BASTION_WALLET_SERVICE_STATUS_UPDATE_URL=http://wallet-infrastructure-service:8083/api/v1/wallet/webhook/
BASTION_WALLET_SERVICE_LAST_TRANSACTION_DATE=http://wallet-infrastructure-service:8083/api/v1/payment/last/
ALLOWED_HOSTS=polity-vault,localhost
DOMAIN_NAME=http://localhost
POLITY_REDIS_HOST=redis
POLITY_REDIS_PORT=6379
AWS_ACCESS_KEY_ID=value
AWS_SECRET_ACCESS_KEY=value
CO_SIGNER_DB_USER=root
CO_SIGNER_DB_HOST=host
CO_SIGNER_DB_PASSWORD=password
CO_SIGNER_IP_TYPE=Private
SSH_JUMPSERVER_IP=ip
BIZ_CALLBACK=http://**************:8083/api/v1/payment/safeheron/resolve/
AWS_REGION=us-west-1
ENVIRONMENT=dev
REDIS_PASSWORD=password

# for prod
VAULT_NAMESPACE=vault_namespace
VAULT_ROLE_ID=vault_role
VAULT_SECRET_ID=vault_secret

# for local\prod
VAULT_URL=vault_url

# for local
VAULT_TOKEN=testtoken