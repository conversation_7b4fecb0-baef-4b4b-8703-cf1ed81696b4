import logging
import os

from celery.result import AsyncResult
from django import forms
from django.contrib import admin
from django.utils.html import format_html
from wallet_credentials.models import DFNSWalletInfo
from wallet_credentials.models import SafeheronWalletInfo
from wallet_credentials.models import WalletCreationRequest

from .tasks import provision, deprovision

logger = logging.getLogger(__name__)


class _CustomTableView:

    def username(self, instance):
        return instance.creation_request.user_name

    def request_uuid(self, instance):
        return format_html(f"<a href='{instance.id}'>{instance.creation_request.request_uuid}</a>")

    def role(self, instance):
        return instance.creation_request.user_role

    def last_updated(self, instance):
        return instance.creation_request.updated_date

    def status(self, instance):
        return instance.creation_request.status

    username.admin_order_field = 'creation_request__user_name'
    username.short_description = 'user name'

    role.admin_order_field = 'creation_request__user_role'
    role.short_description = 'user role'

    last_updated.admin_order_field = 'creation_request__updated_date'
    last_updated.short_description = 'last updated'

    status.admin_order_field = 'creation_request__status'
    status.short_description = 'Status'

    request_uuid.admin_order_field = 'creation_request__request_uuid'
    request_uuid.short_description = 'request id'


class DFNSModelForm(forms.ModelForm):
    jwt_token = forms.CharField(required=False)
    private_key = forms.CharField(required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            if self.instance.store_to_vault:
                vault_data: dict = self.instance.get_from_vault()
                self.fields['jwt_token'].initial = vault_data["jwt_token"]
                self.fields['private_key'].initial = vault_data["private_key"]
        except:
            pass

    def save(self, commit=True, instance=None):
        instance = instance if instance else super().save(commit=False)
        token = self.cleaned_data.get("jwt_token")
        private_key = self.cleaned_data.get("private_key")
        if instance.store_to_vault and token and private_key:
            instance.secret = {
                "jwt_token": token,
                "private_key": private_key
            }
            if instance.store_to_vault:
                instance.save_to_vault()
            instance.save()
        self.cleaned_data["creation_request"].status = WalletCreationRequest.StatusChoices.PROVISIONED
        self.cleaned_data["creation_request"].save()
        return instance

    def clean(self):
        cleaned_data = super().clean()
        if cleaned_data.get("store_to_vault"):
            raise forms.ValidationError(
                {field: "This field is required when store_to_vault is checked." \
                    for field in ["jwt_token", "private_key"] if not cleaned_data.get(field)}
            )
        return cleaned_data

    class Meta:
        model = DFNSWalletInfo
        fields = '__all__'


@admin.register(DFNSWalletInfo)
class DFNSWalletInfoAdmin(admin.ModelAdmin, _CustomTableView):
    form = DFNSModelForm
    fields = ('creation_request', 'credentials_path', 'jwt_token', 'private_key', 'store_to_vault')
    readonly_fields = ('credentials_path',)

    list_display = ('id', 'request_uuid', 'username', 'role', 'last_updated', 'status')
    list_filter = ['creation_request__user_role', 'creation_request__status']
    search_fields = ['creation_request__user_name', 'creation_request__request_uuid']

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "creation_request":
            kwargs["queryset"] = WalletCreationRequest.objects.filter(type='dfns').order_by("-id")
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


class SafeheronModelForm(forms.ModelForm):
    public_key = forms.CharField(required=False)
    private_key = forms.CharField(required=False)
    api_key = forms.CharField(required=False)
    platform_public_key = forms.CharField(required=False)
    notif_platform_pub_key = forms.CharField(required=False)

    cosigner_public_key = forms.CharField(required=False)
    cosigner_private_key = forms.CharField(required=False)
    cosigner_api_key = forms.CharField(required=False)
    cosigner_platform_public_key = forms.CharField(required=False)
    cosigner_notif_platform_pub_key = forms.CharField(required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            if self.instance.store_to_vault:
                vault_data: dict = self.instance.get_from_vault()
                self.fields['public_key'].initial = vault_data["public_key"]
                self.fields['private_key'].initial = vault_data["private_key"]
                self.fields['api_key'].initial = vault_data["api_key"]
                self.fields['platform_public_key'].initial = vault_data["platform_public_key"]
                self.fields['notif_platform_pub_key'].initial = vault_data["notif_platform_pub_key"]

                self.fields['cosigner_public_key'].initial = vault_data["cosigner_public_key"]
                self.fields['cosigner_private_key'].initial = vault_data["cosigner_private_key"]
                self.fields['cosigner_api_key'].initial = vault_data["cosigner_api_key"]
                self.fields['cosigner_platform_public_key'].initial = vault_data["cosigner_platform_public_key"]
                self.fields['cosigner_notif_platform_pub_key'].initial = vault_data["cosigner_notif_platform_pub_key"]
        except:
            pass

    def save(self, commit=True, instance=None):
        instance = instance if instance else super().save(commit=False)

        public_key = self.cleaned_data.get("public_key")
        private_key = self.cleaned_data.get("private_key")
        api_key = self.cleaned_data.get("api_key")
        platform_public_key = self.cleaned_data.get("platform_public_key")
        notif_platform_pub_key = self.cleaned_data.get("notif_platform_pub_key")

        cosigner_public_key = self.cleaned_data.get("cosigner_public_key")
        cosigner_private_key = self.cleaned_data.get("cosigner_private_key")
        cosigner_api_key = self.cleaned_data.get("cosigner_api_key")
        cosigner_platform_public_key = self.cleaned_data.get("cosigner_platform_public_key")
        cosigner_notif_platform_pub_key = self.cleaned_data.get("cosigner_notif_platform_pub_key")

        if instance.store_to_vault:
            instance.secret = {
                "public_key": public_key,
                "private_key": private_key,
                "api_key": api_key,
                "platform_public_key": platform_public_key,
                "notif_platform_pub_key": notif_platform_pub_key,

                "cosigner_public_key": cosigner_public_key,
                "cosigner_private_key": cosigner_private_key,
                "cosigner_api_key": cosigner_api_key,
                "cosigner_platform_public_key": cosigner_platform_public_key,
                "cosigner_notif_platform_pub_key": cosigner_notif_platform_pub_key,
            }
            instance.save_to_vault()
            instance.save()
        self.cleaned_data["creation_request"].status = WalletCreationRequest.StatusChoices.IN_PROGRESS
        self.cleaned_data["creation_request"].save()
        return instance

    def clean(self):
        cleaned_data = super().clean()
        if cleaned_data.get("store_to_vault"):
            fields = [
                "public_key",
                "private_key",
                "api_key", 
                "platform_public_key",
                "notif_platform_pub_key",
                "cosigner_public_key",
                "cosigner_private_key",
                "cosigner_api_key",
                "cosigner_platform_public_key",
                "cosigner_notif_platform_pub_key"
            ]
            raise forms.ValidationError(
                {field: "This field is required when store_to_vault is checked." \
                    for field in fields if not cleaned_data.get(field)}
            )
        return cleaned_data

    class Meta:
        model = SafeheronWalletInfo
        fields = '__all__'


@admin.register(SafeheronWalletInfo)
class SafeheronWalletInfoAdmin(admin.ModelAdmin, _CustomTableView):
    form = SafeheronModelForm
    fields = ('creation_request',

              ('public_key', 'cosigner_public_key'),
              ('private_key', 'cosigner_private_key'),
              ('api_key', 'cosigner_api_key'),
              ('platform_public_key', 'cosigner_platform_public_key'),
              ('notif_platform_pub_key', 'cosigner_notif_platform_pub_key'),

              'credentials_path', 'provisioning_task_id', 'stopping_task_id', 'deprovisioning_task_id', 'cosigner_uuid', 'store_to_vault')
    readonly_fields = ('credentials_path', 'provisioning_task_id', 'stopping_task_id', 'deprovisioning_task_id', 'cosigner_uuid')
    change_form_template = 'admin/custom_change_form.html'

    list_display = ('id', 'request_uuid', 'username', 'role', 'last_updated', 'status')
    list_filter = ['creation_request__user_role', 'creation_request__status']
    search_fields = ['creation_request__user_name', 'creation_request__request_uuid']

    def response_change(self, request, obj):

        if "_provision" in request.POST:
            cos_uuid = os.environ['COSIGNER_PROVISIONING_UUID']
            if not cos_uuid:
                return self._return_response_change(
                    "COSIGNER_PROVISIONING_UUID is not set", request, obj)
            if obj.is_cosigner_exists(cos_uuid):
                return self._return_response_change(
                    "Cosigner already exist. Please use deprovision before re-create another one", request, obj)
            if obj.provisioning_task_id is not None:  # without this we lose id current running instance
                return self._return_response_change(
                    "Instance already exist. Please use deprovision before re-provisioning...", request, obj)
            self._create_and_run_instance(obj=obj, request=request, cos_uuid=cos_uuid)


        elif "_deprovision" in request.POST:
            if not obj.provisioning_task_id:
                return self._return_response_change("Cannot resume provisioning, no provisioning task found.",
                                                    request, obj)

            task_result = AsyncResult(obj.provisioning_task_id)
            if task_result.get().get("instanceId") is not None:
                self._deprovision(task_result=task_result, obj=obj, request=request)
            else:
                self.message_user(request, "Cannot deprovision, instance was't created.")

        elif "_callback" in request.POST:
            obj.creation_request.status = WalletCreationRequest.StatusChoices.PROVISIONED
            # This save will trigger the callback
            obj.creation_request.save()
        else:
            self.message_user(request, "Unknown action.")
        return super().response_change(request, obj)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "creation_request":
            kwargs["queryset"] = WalletCreationRequest.objects.filter(type='safeheron').order_by("-id")
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def _create_and_run_instance(self, obj, request, cos_uuid):
        task = provision.delay(cos_uuid, obj.secret_to_json())
        obj.provisioning_task_id = task.task_id
        obj.deprovisioning_task_id = None
        obj.cosigner_uuid = cos_uuid
        obj.save()
        self.message_user(request, "Provisioning wallet...")

    def _deprovision(self, task_result, obj, request):
        cosigner_uuid_modified = obj.cosigner_uuid.replace('-', '_')
        task = deprovision.delay(task_result.get(), cosigner_uuid_modified)
        obj.provisioning_task_id = None
        obj.deprovisioning_task_id = task.task_id
        obj.cosigner_uuid = None
        obj.save()
        self.message_user(request, "Deprovisioning wallet...")

    def _return_response_change(self, message, request, obj):
        self.message_user(request, message)
        return super().response_change(request, obj)


@admin.register(WalletCreationRequest)
class WalletCreationRequestAdmin(admin.ModelAdmin):
    readonly_fields = ('created_date', 'updated_date')
    list_display = ('id', 'request_uuid_link', 'type', 'user_name', 'user_role', 'created_date', 'status')
    list_filter = ['type', 'user_role', 'status']
    search_fields = ['user_name', 'request_uuid']

    def request_uuid_link(self, instance):
        return format_html(f"<a href='{instance.id}'>{instance.request_uuid}</a>")

    request_uuid_link.admin_order_field = 'request_uuid'
    request_uuid_link.short_description = 'request id'
