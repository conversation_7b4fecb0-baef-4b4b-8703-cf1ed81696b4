from rest_framework import serializers

from wallet_credentials.models import DFNSWalletInfo, SafeheronWalletInfo, WalletCreationRequest


class WalletCreationRequestBodySerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=WalletCreationRequest.WalletTypeChoices.choices)
    request_uuid = serializers.UUIDField()


class CoSignerTriggeredSerializer(serializers.Serializer):
    user_uuid = serializers.UUIDField()


class WalletCreationRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = WalletCreationRequest
        fields = ('type', 'request_uuid', 'user_uuid', 'user_name', 'user_role', 'status', 'id',)


class DFNSWalletInfoSerializer(serializers.ModelSerializer):
    creation_request = WalletCreationRequestSerializer(read_only=True)
    jwt_token = serializers.ReadOnlyField()
    private_key = serializers.ReadOnlyField()

    class Meta:
        model = DFNSWalletInfo
        fields = ('jwt_token', 'private_key', 'creation_request', 'id')


class SafeheronWalletInfoSerializer(serializers.ModelSerializer):
    creation_request = WalletCreationRequestSerializer(read_only=True)
    public_key = serializers.ReadOnlyField()
    private_key = serializers.ReadOnlyField()
    api_key = serializers.ReadOnlyField()
    platform_public_key = serializers.ReadOnlyField()
    notif_platform_pub_key = serializers.ReadOnlyField()

    class Meta:
        model = SafeheronWalletInfo
        fields = ('public_key', 'private_key', 'api_key', 'notif_platform_pub_key',
                  'platform_public_key', 'creation_request', 'id')
