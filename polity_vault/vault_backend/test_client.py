import unittest
from django.conf import settings
from django.test import TestCase

from vault_backend.client import HashiCorpVaultClient


class HCVaultClientTestCase(TestCase):

    def test_set_get_secret(self):

        uuid = "test_uuid"
        with HashiCorpVaultClient(vault_url=settings.VAULT_URL, role_id=settings.VAULT_ROLE_ID,
                                  secret_id=settings.VAULT_SECRET_ID, namespace=settings.VAULT_NAMESPACE,
                                  env=settings.CURRENT_ENV, token=settings.VAULT_TOKEN) as vault:
            vault.set_secret(uuid, {"test": "test"})
            secret = vault.get_secret(uuid)
        self.assertEqual(secret["data"]["data"]["test"], "test")
