import logging
import requests
from django.conf import settings

logger = logging.getLogger(__name__)


class HashiCorpVaultClient:
    def __init__(self):
        self.__client_id = settings.VAULT_CLIENT_ID
        self.__client_secret = settings.VAULT_SECRET_ID
        self.__vault_url = settings.VAULT_URL
        self.__app_name = settings.VAULT_APP_NAME
        self.__organization_id = settings.VAULT_ORGANIZATION_ID
        self.__project_id = settings.VAULT_PROJECT_ID

    def __enter__(self):
        self.__token = self._get_hcp_token()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    def _get_hcp_token(self):
        url = "https://auth.idp.hashicorp.com/oauth2/token"
        data = {
            "client_id": self.__client_id,
            "client_secret": self.__client_secret,
            "grant_type": "client_credentials",
            "audience": "https://api.hashicorp.cloud",
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()

        return response.json()["access_token"]

    def get_secret(self, secret_path: str, secret_fields: set) -> dict:
        headers = {"Authorization": f"Bearer {self.__token}"}
        secret = {}
        for field in secret_fields:
            secret_url = f"{self.__vault_url}/secrets/2023-11-28/organizations/{self.__organization_id}/projects/{self.__project_id}/apps/{self.__app_name}/secrets/{field}_{secret_path.replace('-', '')}"
            secret_info_response = requests.get(secret_url, headers=headers)
            secret_info_response.raise_for_status()
            secret_version = secret_info_response.json()["secret"]["latest_version"]

            secret_response = requests.get(
                secret_url + f"/versions/{secret_version}:open", headers=headers
            )
            secret_response.raise_for_status()
            secret[field] = secret_response.json()["static_version"]["value"]

        return secret

    def set_secret(self, secret_path: str, secret_data: dict) -> None:
        secret_url = f"https://api.cloud.hashicorp.com/secrets/2023-11-28/organizations/{self.__organization_id}/projects/{self.__project_id}/apps/{self.__app_name}/secret/kv"
        headers = {"Authorization": f"Bearer {self.__token}"}

        for key, value in secret_data.items():
            payload = {"name": f"{key}_{secret_path.replace('-', '')}", "value": value}
            secret_response = requests.post(secret_url, json=payload, headers=headers)
            secret_response.raise_for_status()
