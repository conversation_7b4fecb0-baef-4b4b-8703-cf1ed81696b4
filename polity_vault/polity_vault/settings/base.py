"""
Django settings for polity_vault project.

Generated by 'django-admin startproject' using Django 4.1.4.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""
import os
from pathlib import Path
from polity_vault.enums.cosigner_ip_type import CosignerIpType

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get("SECRET_KEY", "404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get("DEBUG", False) == 'True'

ALLOWED_HOSTS = os.environ.get("ALLOWED_HOSTS", 'localhost').split(',')
CSRF_TRUSTED_ORIGINS = os.environ.get('DOMAIN_NAME', 'http://localhost').split(',')

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'drf_yasg',
    'django_celery_results',
    'django_celery_beat',
    'custom_auth',
    'wallet_credentials',
    'vault_backend',
]

CELERY_RESULT_BACKEND = 'django-db'
CELERY_CACHE_BACKEND = 'django-cache'
REDIS_PORT = os.environ.get("POLITY_REDIS_PORT", 6379)
REDIS_HOST = os.environ.get("POLITY_REDIS_HOST", "redis")
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", "26ecfc64-50d1-4317-b6a2-2f39aa521f23")
CELERY_BROKER_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/0"

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'custom_auth.middleware.JWTAuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG',
    },
}

ROOT_URLCONF = 'polity_vault.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'polity_vault.wsgi.application'

BASTION_WALLET_SERVICE_STATUS_UPDATE_URL = os.environ.get("BASTION_WALLET_SERVICE_STATUS_UPDATE_URL",
                                                          "http://localhost:8080/api/update-status/")

BASTION_WALLET_SERVICE_LAST_TRANSACTION_DATE = os.environ.get("BASTION_WALLET_SERVICE_LAST_TRANSACTION_DATE",
                                                              "http://localhost:8083/api/v1/payment/last/")

VAULT_URL = os.environ.get("VAULT_URL", "vault_url")
VAULT_CLIENT_ID = os.environ.get("VAULT_CLIENT_ID", "client_id")
VAULT_SECRET_ID = os.environ.get("VAULT_SECRET_ID", "secret_id")
VAULT_APP_NAME = os.environ.get("VAULT_APP_NAME", "app_name")
VAULT_ORGANIZATION_ID = os.environ.get("VAULT_ORGANIZATION_ID", "organization_id")
VAULT_PROJECT_ID = os.environ.get("VAULT_PROJECT_ID", "project_id")

CURRENT_ENV = os.environ.get("ENVIRONMENT", "env")

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    }
}

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'
CELERY_TIMEZONE = 'UTC'  # set timezone as UTC

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]
STATIC_ROOT = STATIC_URL

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


# can be only Private opr Public
CO_SIGNER_IP_TYPE = os.environ.get("CO_SIGNER_IP_TYPE")
if CO_SIGNER_IP_TYPE not in CosignerIpType.values():
    CO_SIGNER_IP_TYPE = CosignerIpType.PRIVATE.value
