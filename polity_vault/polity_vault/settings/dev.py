from .base import *
from celery.schedules import crontab

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get("POSTGRES_DB", "polity_vault_infrastructure"),
        'USER': os.environ.get("POSTGRES_USER", "polity_vault_infrastructure"),
        'PASSWORD': os.environ.get("POSTGRES_PASSWORD", "qwerty"),
        'HOST': os.environ.get("POSTGRES_HOST", "postgres-polity-vault"),
        'PORT': os.environ.get("POSTGRES_PORT", "5432")
    }
}

CELERY_BEAT_SCHEDULE = {
    'stopping_cosigner_midnight': {
        'task': 'wallet_credentials.tasks.trigger_stop_cosigners',
        'schedule': crontab(minute=0, hour=0)  # Execute daily at midnight
    }
}
