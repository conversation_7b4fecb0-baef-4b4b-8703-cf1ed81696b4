version: "3.9"

services:
  postgres-auth-gateway:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - auth-gateway/.env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - 5432:5432
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: postgres-auth-gateway

  auth-gateway:
    command: gradle bootRun
    image: auth-gateway
    env_file:
      - auth-gateway/.env
    depends_on:
      - postgres-auth-gateway
    links:
      - redis
    volumes:
      - ./auth-gateway/uploads:/project/uploads
    ports:
      - 8080:8080
      - 5005:5005
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1   
        awslogs-group: polity-mvp-logs-prod 
        awslogs-stream: auth-gateway

  frontend-auth:
    image: frontend
    env_file:
      - ./frontend/.env.auth
    depends_on:
      - auth-gateway
    volumes:
      - './frontend:/usr/src/app'
    ports:
      - 3000:3000
    command: bash -c "yarn install && yarn start"
    environment:
      - VIRTUAL_HOST=mvp.polity.network
      - VIRTUAL_PORT=3000
      - LETSENCRYPT_HOST=mvp.polity.network
      - LETSENCRYPT_EMAIL=<EMAIL>
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: frontend-auth

  frontend-cabinet:
    image: frontend
    env_file:
      - ./frontend/.env.cabinet
    volumes:
      - './frontend:/usr/src/app'
    ports:
      - 3001:3000
    command: bash -c "yarn install && yarn start"
    environment:
      - VIRTUAL_HOST=cabinet.polity.network
      - VIRTUAL_PORT=3000
      - LETSENCRYPT_HOST=cabinet.polity.network
      - LETSENCRYPT_EMAIL=<EMAIL>
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: frontend-cabinet

  frontend-operator:
    image: frontend
    env_file:
      - ./frontend/.env.operator
    volumes:
      - './frontend:/usr/src/app'
    ports:
      - 3002:3000
    command: bash -c "yarn install && yarn start"
    environment:
      - VIRTUAL_HOST=operator.polity.network
      - VIRTUAL_PORT=3000
      - LETSENCRYPT_HOST=operator.polity.network
      - LETSENCRYPT_EMAIL=<EMAIL>
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: frontend-operator

  postgres-user-infrastructure:
    image: library/postgres:alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - user-infrastructure/.env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - 5435:5432
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: postgres-user-infrastructure

  user-infrastructure:
    command: gradle bootRun
    image: user-infrastructure
    env_file:
      - user-infrastructure/.env
    depends_on:
      - postgres-user-infrastructure
    volumes:
      - ./user-infrastructure/uploads:/project/uploads
    expose:
      - 8080
    ports:
      - 8081:8080
      - 5006:5005
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: user-infrastructure

  user-infrastructure-nginx:
    image: nginx
    depends_on:
      - user-infrastructure
    expose:
      - 80
    volumes:
      - ./user-infrastructure/nginx.conf:/etc/nginx/conf.d/default.conf:ro

  operator:
    command: gradle bootRun
    image: operator
    env_file:
      - operator/.env
    ports:
      - 8082:8080
      - 5007:5005
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: operator

  postgres-polity-vault:
    image: postgres:15.12-alpine
    healthcheck:
      test: "pg_isready -q -h db"
      interval: 3s
      timeout: 5s
      retries: 5
    env_file:
      - ./polity_vault/.env
    ports:
      - "5434:5432"
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: postgres-polity-vault

  polity-vault:
    build:
      context: ./polity_vault/
      dockerfile: Dockerfile
    volumes:
      - "./polity_vault/staticfiles/:/app/static/"
    env_file:
      - ./polity_vault/.env
    depends_on:
      - postgres-polity-vault
      - redis
    ports:
      - "8000:8000"
    environment:
      - VIRTUAL_HOST=admin.polity.network
      - VIRTUAL_PORT=8000
      - LETSENCRYPT_HOST=admin.polity.network
      - LETSENCRYPT_EMAIL=<EMAIL>
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: polity-vault

  wallet-infrastructure-service:
    build:
      context: ./wallet-infrastructure-service/
      target: release
      dockerfile: Dockerfile
    image: wallet-infrastructure-service
    restart: always
    env_file:
      - ./wallet-infrastructure-service/.env
    depends_on:
      - wallet-infrastructure-storage
      - polity-vault
    expose:
      - 8083
    ports:
      - "8083:8083"
    environment:
      - VIRTUAL_HOST=bwi.polity.network
      - VIRTUAL_PORT=8083
      - LETSENCRYPT_HOST=bwi.polity.network
      - LETSENCRYPT_EMAIL=<EMAIL>
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: wallet-infrastructure-service

  wallet-infrastructure-storage:
    image: postgres:15.4
    restart: always
    healthcheck:
      test: [ "CMD", "pg_isready", "-q", "-d", "$POSTGRES_DB", "-U", "$POSTGRES_USER" ]
      interval: 10s
      timeout: 3s
      retries: 5
    env_file:
      - ./wallet-infrastructure-service/.env
    ports:
      - "5433:5432"
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: wallet-infrastructure-storage

  wallet-infrastructure-nginx:
    image: nginx
    depends_on:
      - wallet-infrastructure-service
    expose:
      - 80
    volumes:
      - ./wallet-infrastructure-service/nginx.conf:/etc/nginx/conf.d/default.conf:ro

  redis:
    image: 'bitnami/redis:latest'
    env_file:
      - ./polity_vault/.env
    expose:
      - '6379'
    ports:
      - "6379:6379"
    volumes:
      - ./redis/data:/bitnami/redis/data
    logging:
      driver: awslogs
      options:
        awslogs-region: us-west-1
        awslogs-group: polity-mvp-logs-prod
        awslogs-stream: redis

networks:
  default:
    external:
      name: nginx-proxy
