package api

const (
	badRequestBodyErr = "Request body is not valid"
	encodeResponseErr = "Encode response error"
	errNoRights       = "no access to the feature"

	assetSymbolErr  = "Invalid asset symbol"
	requestIdErr    = "Invalid request id"
	walletNameErr   = "Invalid wallet name"
	walletTypeErr   = "Invalid wallet type"
	offsetErr       = "Invalid offset number"
	limitErr        = "Invalid limit number"
	balancesFlagErr = "Invalid balances flag value"

	getCredentialsDFNSErr      = "Can't get DFNS wallet credentials"
	getCredentialsSafeheronErr = "Can't get Safeheron wallet credentials"

	paymentErr               = "Can't make payment"
	createWalletErr          = "Can't create wallet"
	getWalletErr             = "Can't get wallet"
	walletCreationRequestErr = "Can't create wallet creation request"
	getDefiBalancesErr       = "Can't get DeFi products balances"
	getAssetHistoryErr       = "Can't get asset history"

	walletsErr              = "No wallets for provided user"
	walletNotFoundErr       = "Wallet doesn't exist"
	assetNotFoundErr        = "No asset for provided wallet found"
	addressesNotFoundErr    = "No addresses for provided asset found"
	addressNotFoundErr      = "Address doesn't exist"
	transactionsNotFoundErr = "No transactions found"

	walletUniquenessErr  = "Wallet already exists"
	addressUniquenessErr = "Address already exists"
	assetUniquenessErr   = "Asset already exists"
)
