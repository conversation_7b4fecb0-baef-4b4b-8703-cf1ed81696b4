package utils

import "github.com/Polity_MVP/wallet-infrastructure-service/internal/models/pagination"

// GetOffsets calculates previous and next offsets
func GetOffsets(current, limit int64, pg pagination.TransactionsHistoryMeta) (int64, int64) {
	var prev int64
	var next int64

	if current == 0 {
		prev = -1
	} else {
		prev = current - 1
	}

	if pg.Total-(current*limit) <= limit && pg.Token == "" {
		next = -1
	} else {
		next = current + 1
	}

	return prev, next
}
