package status

import (
	"fmt"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/authGateway"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/userInfrastructure"
)

type HandlerManager struct {
	container             container.WalletInfrastructureContainer
	walletRequestsService service.WalletRequestsService

	// Cached handlers
	pending     HandlerService
	provisioned HandlerService
	inProgress  HandlerService
	failed      HandlerService
}

func NewHandlerManager(container container.WalletInfrastructureContainer, safeheron service.WalletInfrastructureService[vault.SafeheronCredentials], dfns service.WalletInfrastructureService[vault.DFNSCredentials], walletRequest service.WalletRequestsService, userInfrastructureClient userInfrastructure.Client, authGatewayClient authGateway.Client) *HandlerManager {
	// define sub-handlers
	pending := PendingHandler{
		container:            container,
		walletRequestService: walletRequest,
	}
	provisioned := ProvisionedHandler{
		container:                container,
		safeheronService:         safeheron,
		dfnsService:              dfns,
		walletRequestService:     walletRequest,
		userInfrastructureClient: userInfrastructureClient,
		authGatewayClient:        authGatewayClient,
	}
	inProgress := InProgressHandler{
		container:            container,
		walletRequestService: walletRequest,
	}
	failed := FailedHandler{
		container:                container,
		walletRequestService:     walletRequest,
		userInfrastructureClient: userInfrastructureClient,
	}
	return &HandlerManager{
		container:             container,
		walletRequestsService: walletRequest,
		pending:               &pending,
		provisioned:           &provisioned,
		inProgress:            &inProgress,
		failed:                &failed,
	}
}

func (h *HandlerManager) HandleError(requestId string, err error) error {
	// Possibly in the future we can get the type of error and do actions based on its contents
	updateErr := h.walletRequestsService.UpdatePolityWalletStatus(requestId, vault.Failed)
	h.container.GetLogger().Errorf(fmt.Sprintf("Unable to set any status, defaulting to Failed status, error: %v.", err))
	if updateErr != nil {
		h.container.GetLogger().Errorf(fmt.Sprintf("Can't update wallet status in the database, wallet id: %s, error: %v", requestId, updateErr))
		return updateErr
	}
	return err
}

func (h *HandlerManager) GetStatusHandler(status vault.Status) (HandlerService, error) {
	var handler HandlerService
	switch status {
	case vault.Pending:
		handler = h.pending
	case vault.Provisioned:
		handler = h.provisioned
	case vault.InProgress:
		handler = h.inProgress
	case vault.Failed:
		handler = h.failed
	default:
		return nil, fmt.Errorf("Unknown status, %s ", status)
	}
	return handler, nil
}

func (h *HandlerManager) IsActiveWalletStatus(status vault.Status) bool {
	switch status {
	case vault.Provisioned:
		return true
	default:
		return false
	}
}
