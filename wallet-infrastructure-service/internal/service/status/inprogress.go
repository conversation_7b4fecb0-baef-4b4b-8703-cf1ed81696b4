package status

import (
	"encoding/json"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
)

type InProgressHandler struct {
	container            container.WalletInfrastructureContainer
	walletRequestService service.WalletRequestsService
}

func (i *InProgressHandler) Process(requestId string, data json.RawMessage) error {
	err := i.walletRequestService.UpdatePolityWalletStatus(requestId, vault.InProgress)
	if err != nil {
		return err
	}
	return nil
}
