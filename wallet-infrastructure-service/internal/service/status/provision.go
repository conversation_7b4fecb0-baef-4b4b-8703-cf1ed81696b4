package status

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/notification"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets/safeheron"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/authGateway"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/polityVault"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/userInfrastructure"
)

const (
	privateKeyBegin = "-----BEGIN PRIVATE KEY-----"
	privateKeyEnd   = "-----END PRIVATE KEY-----"
	publicKeyBegin  = "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----"
	publicKeyEnd    = "-----<PERSON><PERSON> PUBLIC KEY-----"
)

var (
	ErrEmptyTokenForDfnsClient      = errors.New("set 'jwt_token' for dfns client. ")
	ErrEmptyPrivateKeyForDfnsClient = errors.New("set 'private_key' for dfns client. ")
)

type ProvisionedHandler struct {
	container                container.WalletInfrastructureContainer
	safeheronService         service.WalletInfrastructureService[vault.SafeheronCredentials]
	dfnsService              service.WalletInfrastructureService[vault.DFNSCredentials]
	walletRequestService     service.WalletRequestsService
	userInfrastructureClient userInfrastructure.Client
	authGatewayClient        authGateway.Client
}

func (p *ProvisionedHandler) Process(requestId string, data json.RawMessage) error {
	wallet, err := p.walletRequestService.GetPolityWallet(requestId)
	if err != nil {
		return err
	}
	source := (*json.RawMessage)(&data)

	switch wallet.Type {
	case wallets.SafeHeron:
		safeheronCredentials := new(vault.SafeheronCredentials)
		if err := json.Unmarshal(*source, safeheronCredentials); err != nil {
			p.handleError(wallet, err)
			return fmt.Errorf("validation error: %v", err)
		}
		err = p.container.GetValidator().Validator.Struct(safeheronCredentials)
		if err != nil {
			p.handleError(wallet, err)
			return err
		}
		p.formatSafeheronCredentials(safeheronCredentials)

		if err := p.safeheronService.InitializeAssets(wallet, safeheronCredentials); err != nil {
			p.handleError(wallet, err)
			return err
		}

		err = p.authGatewayClient.SendWalletStatisticNotification(notification.WalletStatisticsNotification{
			WalletType: wallets.SafeHeron,
			Count:      safeheron.WalletAccountsPerWallet,
		})
		if err != nil {
			p.container.GetLogger().Errorf("cant send notification: %v", err)
		}
	case wallets.DFNS:
		dfnsCredentials := new(vault.DFNSCredentials)
		if err := json.Unmarshal(*source, dfnsCredentials); err != nil {
			p.handleError(wallet, err)
			return fmt.Errorf("validation error: %v", err)
		}

		dfnsCredentials.PrivateKey = polityVault.FormatPrivateKey(dfnsCredentials.PrivateKey)

		if err := p.validateCredentials(dfnsCredentials); err != nil {
			p.handleError(wallet, err)
			return err
		}

		if err := p.dfnsService.InitializeAssets(wallet, dfnsCredentials); err != nil {
			p.handleError(wallet, err)
			return err
		}

		err = p.authGatewayClient.SendWalletStatisticNotification(notification.WalletStatisticsNotification{
			WalletType: wallets.DFNS,
			Count:      len(wallets.DFNSDefaultNetworks.DefaultNetworksList),
		})
		if err != nil {
			p.container.GetLogger().Errorf("cant send notification: %v", err)
		}
	default:
		p.handleError(wallet, err)
		return fmt.Errorf("Unknown wallet type, %s ", wallet.Type)
	}

	err = p.walletRequestService.UpdatePolityWalletStatus(requestId, vault.Provisioned)
	if err != nil {
		return err
	}

	err = p.userInfrastructureClient.SendWalletCreationStateNotification(wallet.UserID, notification.WalletSetUpPayload{
		RequestID:  wallet.RequestID,
		Status:     vault.Provisioned,
		WalletName: wallet.WalletName,
	})
	if err != nil {
		p.container.GetLogger().Errorf("cant send notification: %v", err)
	}

	return nil
}

func (p *ProvisionedHandler) validateCredentials(credentials interface{}) error {
	switch cred := credentials.(type) {
	case *vault.SafeheronCredentials:
		if cred.ApiKey == "" {
			return errors.New("Set 'api_key' for safeheron client. ")
		}
		if cred.PrivateKey == "" {
			return errors.New("Set 'private_key' for safeheron client. ")
		}
		if cred.PlatformPublicKey == "" {
			return errors.New("Set 'public_key' for safeheron client. ")
		}
	case *vault.DFNSCredentials:
		if cred.JwtToken == "" {
			return ErrEmptyTokenForDfnsClient
		}
		if cred.PrivateKey == "" {
			return ErrEmptyPrivateKeyForDfnsClient
		}
	default:
		return fmt.Errorf("I don't know about type %T!\n", cred)
	}

	return nil
}

func (p *ProvisionedHandler) formatSafeheronCredentials(credentials *vault.SafeheronCredentials) *vault.SafeheronCredentials {
	cleanedKey := strings.Replace(credentials.PrivateKey, fmt.Sprintf("%s ", privateKeyBegin), "", 1)
	cleanedKey = strings.Replace(cleanedKey, fmt.Sprintf(" %s", privateKeyEnd), "", 1)
	cleanedKey = strings.Replace(cleanedKey, " ", "\n", -1)
	credentials.PrivateKey = fmt.Sprintf("%s\n%s\n%s", privateKeyBegin, cleanedKey, privateKeyEnd)

	cleanedKey = strings.Replace(credentials.PlatformPublicKey, fmt.Sprintf("%s ", publicKeyBegin), "", 1)
	cleanedKey = strings.Replace(cleanedKey, fmt.Sprintf(" %s", publicKeyEnd), "", 1)
	cleanedKey = strings.Replace(cleanedKey, " ", "\n", -1)
	credentials.PlatformPublicKey = fmt.Sprintf("%s\n%s\n%s", publicKeyBegin, cleanedKey, publicKeyEnd)

	return credentials
}

// handleError - handles previous error and updates polity wallet status into status 'Failed'.
func (p *ProvisionedHandler) handleError(wallet *polity.Wallet, err error) {
	updateErr := p.walletRequestService.UpdatePolityWalletStatus(wallet.RequestID, vault.Failed)
	if updateErr != nil {
		p.container.GetLogger().Errorf(fmt.Sprintf("Unable to set Failed status: %v. Previous error: %v", updateErr, err))
	}

	err = p.userInfrastructureClient.SendWalletCreationStateNotification(wallet.UserID, notification.WalletSetUpPayload{
		RequestID:  wallet.RequestID,
		Status:     vault.Failed,
		WalletName: wallet.WalletName,
	})
	if err != nil {
		p.container.GetLogger().Errorf("cant send notification: %v", err)
	}

	return
}
