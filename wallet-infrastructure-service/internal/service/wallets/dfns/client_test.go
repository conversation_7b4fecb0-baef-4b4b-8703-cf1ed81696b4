package dfns

import (
	"os"
	"testing"

	"github.com/Polity_MVP/wallet-infrastructure-service/config"
	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/flagger"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/dfns"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/Polity_MVP/wallet-infrastructure-service/logger"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/polityVault"
	"github.com/Polity_MVP/wallet-infrastructure-service/validator"
	goValidator "github.com/go-playground/validator/v10"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/require"
)

const (
	// PaymentReceivedEventKind - an incoming payment was received in your wallet.
	PaymentReceivedEventKind = "PaymentReceived"
	FlagConfigPath           = "../../../../flag-config.yaml"
	WalletId                 = "pk-augus-arkan-4v9nqco9ji9obp30"
)

func prepareTestContainer(t *testing.T) container.WalletInfrastructureContainer {
	// Define new config
	testConfig, err := config.NewWalletInfrastructureConfig()
	testConfig.FlagConfigPath = FlagConfigPath
	require.NoError(t, err)

	// Define new logger
	testLogger, err := logger.NewWalletInfrastructureLogger(testConfig)
	require.NoError(t, err)

	defer logger.Close(testLogger)
	// Define new validator
	v := goValidator.New()

	tr, err := validator.GetEnglishTranslator()
	require.NoError(t, err)

	testValidator := validator.NewWalletInfrastructureValidator(v, tr)
	err = testValidator.Register()
	require.NoError(t, err)

	testFlagger, err := flagger.NewWalletInfrastructureFlagger(testConfig)
	require.NoError(t, err)

	// Define new container
	return container.NewWalletInfrastructureContainer(testConfig, testLogger, testValidator, testFlagger)
}
func TestClient_InitiatePayment(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	initiateReq := &dfns.InitiatePaymentReq{
		Kind:   "Native",
		To:     "******************************************",
		Amount: "100000",
	}
	_, err = testClient.InitiatePayment(initiateReq, WalletId)
	require.NoError(t, err)
}

func TestClient_CreateWallet(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	_, err = testClient.CreateWallet(wallets.EthereumNetwork, "Ethereum wallet")
	require.NoError(t, err)
}

func TestClient_BroadcastTransaction(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	broadcastReq := &dfns.BroadcastTransactionReq{
		Kind: "Evm",
		Data: "0x40d097c3000000000000000000000000d2f77f85a50cdd650ca562f3a180284e1d5b4934",
		To:   "******************************************",
	}

	broadcastResp, err := testClient.BroadcastTransaction(broadcastReq, WalletId)
	require.NoError(t, err)

	tx, err := testClient.GetTransactionByID(broadcastResp.ID, WalletId)
	require.NoError(t, err)
	require.NotNil(t, tx)
}

func TestClient_GetTransactionByID(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	tx, err := testClient.GetTransactionByID("tx-7q3ck-h8c3u-8lsoq14snc433fjs", WalletId)
	require.NoError(t, err)
	require.NotNil(t, tx)
}

func TestClient_GetWalletByID(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	_, err = testClient.GetWalletByID(WalletId)
	require.NoError(t, err)
}

func TestClient_GetDFNSAssets(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	assets, err := testClient.GetAssets(WalletId)
	require.NoError(t, err)
	require.Equal(t, 3, len(assets))
}

func TestClient_GetPaymentsList(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	resp, err := testClient.GetPaymentsList(WalletId, 100)
	require.NotNil(t, resp)
	require.Greater(t, len(resp.Items), 0)
	require.NoError(t, err)
}

func TestClient_GetBroadcastedTransactionsList(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	resp, err := testClient.GetBroadcastedTransactionsList(WalletId)
	testClient.container.GetLogger().Info(resp.Items)
	require.NotNil(t, resp)
	require.Greater(t, len(resp.Items), 0)
	require.NoError(t, err)
}

func TestClient_GenerateSignature(t *testing.T) {
	err := godotenv.Load("../../../../.env")
	require.NoError(t, err)

	testContainer := prepareTestContainer(t)
	credentials := vault.DFNSCredentials{
		JwtToken:   os.Getenv("DFNS_TOKEN"),
		PrivateKey: polityVault.FormatPrivateKey(testContainer.GetConfig().DfnsPrivateKey),
	}
	testClient := NewClient(ProductionDfnsUrl, &credentials, testContainer)

	req := dfns.GenerateSignatureRequest{
		Kind:    "Message",
		Message: "0x49206c6f76652044666e73",
	}

	resp, err := testClient.GenerateSignature(WalletId, &req)
	testClient.container.GetLogger().Info(resp)
	require.NotNil(t, resp)
	require.NoError(t, err)
}
