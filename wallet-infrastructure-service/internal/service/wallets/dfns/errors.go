package dfns

import "fmt"

type Error string

func (err Error) Error() string {
	return string(err)
}

const (
	ErrUnauthorized     = Error("unauthorized")
	ErrForbidden        = Error("forbidden")
	ErrNotFound         = Error("not found")
	ErrBadRequest       = Error("bad request")
	ErrUnsupportedAsset = Error("wallet chain is not supported for DeFi")
	ErrUnexpectedData   = Error("unexpected data")
)

const ErrWalletProvider = "Wallet Provider Error:"

type ErrorResponse []struct {
	Field   string `json:"field"`
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (response ErrorResponse) Error() string {
	str := ""
	for _, v := range response {
		str += fmt.Sprintf("%+v\n", v)
	}
	if len(str) > 0 {
		str = str[:len(str)-1] // remove trailing newline character
	}
	return str
}
