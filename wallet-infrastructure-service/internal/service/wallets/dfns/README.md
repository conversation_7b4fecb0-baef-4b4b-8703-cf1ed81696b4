/*
 InitiatePayment

	Example of query:
		curl -X POST "/assets/asset-accounts/aa-orange-magnesium-a0606d08b2/payments" \
		-H "Content-Type: application/json" \
		-H "Authorization: Bearer <TOKEN>" \
		-d '{
				"receiver": {
					"kind": "BlockchainWalletAddress",
					"address": "AXn56FXBfqRvGejFYN57roxeiztTE87HLZwb8wz3pjWf"
				},
				"assetSymbol": "BTC",
				"amount": "1",
				"note": "testing",
				"narrative": "some payment",
				"externalId": "1-2-3-4"
			}'

	Example of Response:
			 params.id:  pa-muppet-july-8bb93a9d45
			{
				"amount": "0.*********",
				"assetAccountId": "aa-india-vermont-7b25f1ccb1",
				"assetSymbol": "ETH",
				"dateCreated": "2022-11-30T18:49:03.985Z",
				"id": "pa-muppet-july-8bb93a9d45",
				"initiator": {
					"kind": "Employee",
					"employeeId": "oe-golf-king-532cc8e2d8ad",
					"orgId": "or-golf-seven-9ff1f0cb7969"
				},
				"orgId": "or-golf-seven-9ff1f0cb7969",
				"receiver": {
					"kind": "BlockchainWalletAddress",
					"address": "******************************************"
				},
				"receiverAddress": "******************************************",
				"status": "Executed",
				"txHash": "0x35a700ff82cba79fe01f702879d739daca796ec89fd8eb9d6d28e573720cf129"
			}
*/