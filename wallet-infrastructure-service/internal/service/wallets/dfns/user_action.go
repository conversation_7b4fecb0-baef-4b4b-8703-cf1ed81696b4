package dfns

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"net/http"

	"github.com/dvse<PERSON><PERSON><PERSON>/jose2go/base64url"
	"github.com/pkg/errors"
)

type UserActionSignatureChallengeResponse struct {
	Challenge           string `json:"challenge"`
	ChallengeIdentifier string `json:"challengeIdentifier"`
	AllowCredentials    struct {
		Key []struct {
			Id string `json:"id"`
		} `json:"key"`
	} `json:"allowCredentials"`
}

type UserActionSignatureResponse struct {
	UserAction string `json:"userAction"`
}

func (client *Client) createUserActionSignatureChallenge(method, path, payload string) (*UserActionSignatureChallengeResponse, error) {
	challengeResponse := UserActionSignatureChallengeResponse{}

	data := map[string]string{
		"userActionPayload":    payload,
		"userActionHttpMethod": method,
		"userActionHttpPath":   path,
	}

	if err := client.doRequest(
		http.MethodPost,
		client.constructUrl("auth", "action", "init"),
		"",
		nil,
		data,
		&challengeResponse,
	); err != nil {
		return nil, err
	}

	return &challengeResponse, nil
}

func (client *Client) createUserActionSignature(challangeResponse *UserActionSignatureChallengeResponse) (*UserActionSignatureResponse, error) {
	signatureResponse := UserActionSignatureResponse{}

	data := map[string]string{
		"challenge":   challangeResponse.Challenge,
		"crossOrigin": "",
		"origin":      client.container.GetConfig().DfnsBaseUrl,
		"type":        "key.get",
	}
	dataStr, err := json.Marshal(data)
	if err != nil {
		return nil, errors.Wrapf(err, "can't marshal data")
	}

	keyBlock, _ := pem.Decode([]byte(client.privateKey))
	key, err := x509.ParsePKCS8PrivateKey(keyBlock.Bytes)
	if err != nil {
		return nil, errors.Wrapf(err, "can't parse private key")
	}

	privateKey, ok := key.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("can't cast type to *rsa.PrivateKey")
	}

	hashed := sha256.Sum256(dataStr)
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hashed[:])
	if err != nil {
		return nil, errors.Wrapf(err, "can't create signature")
	}

	sigReq := map[string]interface{}{
		"challengeIdentifier": challangeResponse.ChallengeIdentifier,
		"firstFactor": map[string]interface{}{
			"kind": "Key",
			"credentialAssertion": map[string]string{
				"credId":     challangeResponse.AllowCredentials.Key[0].Id,
				"clientData": base64url.Encode(dataStr),
				"signature":  base64url.Encode(signature),
			},
		},
	}

	if err := client.doRequest(
		http.MethodPost,
		client.constructUrl("auth", "action"),
		"",
		nil,
		sigReq,
		&signatureResponse,
	); err != nil {
		return nil, err
	}

	return &signatureResponse, nil
}
