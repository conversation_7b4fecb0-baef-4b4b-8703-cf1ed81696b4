package favourites

import (
	"fmt"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/storage"
)

// favoriteAddressService is struct that enables managing wallets
type favoriteAddressService struct {
	container container.WalletInfrastructureContainer
	storage   *storage.WalletInfrastructureStorage
}

// NewFavoriteAddressService  constructs an instance of the walletManagementService
func NewFavoriteAddressService(container container.WalletInfrastructureContainer, storage *storage.WalletInfrastructureStorage) service.FavoriteAddressService {
	return &favoriteAddressService{
		container: container,
		storage:   storage,
	}
}

func (f *favoriteAddressService) AddAddress(favouriteAddress *polity.FavouriteAddressEntry) error {
	err := f.storage.AddAddress(favouriteAddress)
	return err
}

func (f *favoriteAddressService) GetAddressesByUserID(userID string) ([]polity.FavouriteAddressEntry, error) {
	addresses, err := f.storage.GetAddressesByUserID(userID)
	if err != nil {
		return nil, err
	}

	return addresses, nil
}

func (f *favoriteAddressService) AddAdvisorAddresses(userID string, favouriteAddresses []*polity.FavouriteAddressEntry) error {
	for _, address := range favouriteAddresses {
		address.UserID = userID
		address.Name = fmt.Sprintf("Advisors' %s wallet", address.AssetSymbol)
		err := f.storage.AddAddress(address)
		if err != nil {
			return err
		}
	}

	return nil
}

func (f *favoriteAddressService) RemoveAddress(favouriteAddress *polity.FavouriteAddressEntry) error {
	err := f.storage.RemoveAddress(favouriteAddress)
	return err
}
