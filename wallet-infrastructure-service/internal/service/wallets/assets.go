package wallets

import (
	"math"

	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/dfns"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/safeheron"
)

const (
	AssetSymbolBitcoin          = "BTC"
	AssetSymbolBitcoinCache     = "BCH"
	AssetSymbolEthereum         = "ETH"
	AssetSymbolEthereumClassic  = "ETC"
	AssetSymbolPolygon          = "POL"
	AssetSymbolUSDC             = "USDC.ETH"
	CoinMarketCupUSDC           = "USDC"
	AssetSymbolHeco             = "HT"
	AssetSymbolTron             = "TRX"
	AssetSymbolAvalanche        = "AVAX"
	AssetSymbolDash             = "DASH"
	AssetSymbolUSDCForSafeheron = "USDC_ERC20"

	BitcoinNetwork   = "Bitcoin"
	EthereumNetwork  = "Ethereum"
	PolygonNetwork   = "Polygon"
	AvalancheNetwork = "AvalancheC"
)

const (
	SafeHeron = "safeheron"
	DFNS      = "dfns"
)

var DFNSDefaultNetworks = &dfns.Networks{
	DefaultNetworksList: []string{
		BitcoinNetwork,
		EthereumNetwork,
		PolygonNetwork,
		// AvalancheNetwork,
	},
	CoinMarketCupMap: map[string]string{
		AssetSymbolUSDC:     CoinMarketCupUSDC, // CoinMarketCup doesn't support 'USDC.ETH' currency, but supports 'USDC'
		AssetSymbolBitcoin:  AssetSymbolBitcoin,
		AssetSymbolEthereum: AssetSymbolEthereum,
		AssetSymbolPolygon:  AssetSymbolPolygon,
	},
}

var SafeheronAssetsLists = &safeheron.Assets{
	DefaultCoinsList: []string{
		//AssetSymbolHeco,
		//AssetSymbolTron,
		AssetSymbolBitcoin,
		//AssetSymbolBitcoinCache,

		// AssetSymbolAvalanche,
		//AssetSymbolEthereumClassic,
	},
	Web3List: []string{
		AssetSymbolEthereum,
		AssetSymbolPolygon,
	},
}

var WeiPerToken = math.Pow(10, 18)

var BaseNetworksAssets = map[string]string{
	AssetSymbolBitcoin: "bitcoin",
	//AssetSymbolBitcoinCache:    "10000",
	AssetSymbolEthereum: "1",
	//AssetSymbolEthereumClassic: "61",
	AssetSymbolPolygon:   "137",
	AssetSymbolAvalanche: "43114",
}

var DFNSAssetSymbolNetworkMap = map[string]string{
	"BTC":   BitcoinNetwork,
	"ETH":   EthereumNetwork,
	"POL": PolygonNetwork,
	"AVAX":  AvalancheNetwork,
}

var DFNSNetworkAssetsymbolMap = map[string]string{
	BitcoinNetwork:   "BTC",
	EthereumNetwork:  "ETH",
	PolygonNetwork:   "POL",
	AvalancheNetwork: "AVAX",
}
