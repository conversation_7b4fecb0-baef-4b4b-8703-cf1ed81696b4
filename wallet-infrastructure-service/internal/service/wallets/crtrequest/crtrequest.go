// Package crtrequest Full name is creation request
// This package contains structures that store the intent of the user to create a wallet
package crtrequest

import (
	"fmt"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/storage"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/polityVault"
)

type WalletRequestService struct {
	container         container.WalletInfrastructureContainer
	polityVaultClient polityVault.Client
	storage           *storage.WalletInfrastructureStorage
}

// NewWalletRequestService  constructs an instance of the Wallet Request Service
func NewWalletRequestService(container container.WalletInfrastructureContainer, polityVaultClient polityVault.Client, storage *storage.WalletInfrastructureStorage) *WalletRequestService {
	return &WalletRequestService{
		container:         container,
		polityVaultClient: polityVaultClient,
		storage:           storage,
	}
}

// CreateWalletRequest saves request and forwards wallet creation request to Polity Vault
func (w *WalletRequestService) CreateWalletRequest(wallet *polity.Wallet, token string) error {
	logger := w.container.GetLogger()
	logger.Debugf("Creating wallet request. wallet.RequestID: '%v', wallet.Type: '%v'\n", wallet.RequestID, wallet.Type)
	err := w.polityVaultClient.CreateWalletRequest(vault.WalletCreationRequest{
		RequestID: wallet.RequestID,
		Type:      wallet.Type,
	}, token)
	if err != nil {
		updateErr := w.storage.UpdatePolityWalletStatus(wallet.RequestID, vault.Failed)
		if updateErr != nil {
			return fmt.Errorf("Unable to set FailedStatus: %v. Previous error: %v ", updateErr, err)
		}
		return err
	}
	logger.Debug("Created Wallet Request, successfully posted to Polity Vault")

	return nil
}

func (w *WalletRequestService) CreatePolityWallet(wallet *polity.Wallet) error {
	wallet.Status = vault.Pending
	_, err := w.storage.CreatePolityWallet(wallet)
	if err != nil {
		return err
	}
	return nil
}

// UpdatePolityWalletStatus updates wallet status
func (w *WalletRequestService) UpdatePolityWalletStatus(requestID string, status vault.Status) error {
	err := w.storage.UpdatePolityWalletStatus(requestID, status)
	if err != nil {
		return err
	}

	return nil
}

// UpdatePolityWalletName updates wallet name
func (w *WalletRequestService) UpdatePolityWalletName(request polity.RenameWalletRequest) error {
	err := w.storage.UpdatePolityWalletName(request.RequestID, request.WalletName)
	if err != nil {
		return err
	}

	return nil
}

// GetPolityWalletForUser get polity wallet from db
func (w *WalletRequestService) GetPolityWalletForUser(requestID, userID string) (*polity.Wallet, error) {
	wallet, err := w.storage.GetPolityWalletForUser(requestID, userID)
	if err != nil {
		return nil, err
	}

	return wallet, nil
}

func (w *WalletRequestService) GetDefiProducts() ([]defi.DefiProduct, error) {
	products, err := w.storage.GetDefiWhitelistedProducts()
	if err != nil {
		return nil, err
	}

	return products, nil
}

// GetPolityWalletsByTypeAndUserID gets all active wallets for user by Type and  UserID
func (w *WalletRequestService) GetPolityWalletsByTypeAndUserID(walletType, userID string) ([]polity.Wallet, error) {
	wallets, err := w.storage.GetPolityWalletsByTypeAndUserID(walletType, userID)
	if err != nil {
		return nil, err
	}

	return wallets, nil
}

// GetPolityWallet get polity wallet from db
func (w *WalletRequestService) GetPolityWallet(requestID string) (*polity.Wallet, error) {
	wallet, err := w.storage.GetPolityWallet(requestID)
	if err != nil {
		return nil, err
	}

	return wallet, nil
}

// ArchivePolityWallet archives the polity wallet
func (w *WalletRequestService) ArchivePolityWallet(requestID, userID string) error {
	err := w.storage.ArchivePolityWallet(requestID, userID)
	if err != nil {
		return err
	}

	return nil
}

// GetPolityWalletsForUser gets all user wallet
func (w *WalletRequestService) GetPolityWalletsForUser(userID string) ([]polity.Wallet, error) {
	userWallets, err := w.storage.GetPolityWalletsForUser(userID)
	if err != nil {
		return nil, err
	}
	return userWallets, nil
}
