package safeheron

import (
	"context"
	"encoding/hex"
	"fmt"
	"math"
	"math/big"
	"sort"
	"strconv"
	"time"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	defiModels "github.com/Polity_MVP/wallet-infrastructure-service/internal/models/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/pagination"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/safeheron"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	sdk "github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets/safeheron/sdk"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/storage"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/evm"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/rs/xid"
	"golang.org/x/sync/errgroup"
)

const (
	createTransactionsPath         = "/v2/transactions/create"
	getTransactionPath             = "/v1/transactions/one"
	createCoinsWalletAccountPath   = "/v1/account/create"
	createWeb3WalletAccountPath    = "/v1/web3/account/create"
	createCoinPath                 = "/v1/account/coin/create"
	getAccountCoinsPath            = "/v1/account/coin/list"
	getCoinsListPath               = "/v1/coin/list"
	getWeb3AccountsPath            = "/v1/web3/account/list"
	signTransactionPath            = "/v1/web3/sign/ethSignTransaction"
	checkTransactionSignResultPath = "/v1/web3/sign/one"
	getCoinTransactionsPath        = "/v1/transactions/list"
	getWeb3TransactionsPath        = "/v1/web3/sign/list"
	personalSign                   = "/v1/web3/sign/personalSign"
)

const (
	sourceAccountType      = "VAULT_ACCOUNT"
	destinationAccountType = "ONE_TIME_ADDRESS"
	txFeeLevel             = "LOW"

	failedStatus              = "FAILED"
	rejectedStatus            = "REJECTED"
	transactionCompleteStatus = "COMPLETED"
	signCompleteStatus        = "SIGN_COMPLETED"
)

const (
	checkTransactionSignResultPeriod = 2  //sec
	retryCreateWeb3AccountPeriod     = 20 //sec
	retryCreateWeb3AccountQuantity   = 3
	stopTickerPeriod                 = 1440 //min

	defaultStartTime = **********

	maxWeb3PageSize  = 500
	maxCoinsPageSize = 100
)

const (
	ErrWalletProvider   = "Wallet Provider Error:"
	ErrUnsupportedAsset = "wallet chain is not supported for DeFi"
)

var WalletAccountsPerWallet = len(wallets.SafeheronAssetsLists.Web3List) + 1

var (
	AssetIsNotSupportedErr = errors.New("provided asset is not supported")
	InvalidAccountTypeErr  = errors.New("invalid asset account type")
	UnexpectedDataErr      = errors.New("unexpected data")
)

// safeheronWalletService wrapper for the interface implementation for Safeheron
type safeheronWalletService struct {
	container   container.WalletInfrastructureContainer
	storage     *storage.WalletInfrastructureStorage
	defiService defi.Service
}

// NewSafeheronWalletService constructs an instance of the Safeheron Wallet Service
func NewSafeheronWalletService(container container.WalletInfrastructureContainer, storage *storage.WalletInfrastructureStorage, defiService defi.Service) service.WalletInfrastructureService[vault.SafeheronCredentials] {
	return &safeheronWalletService{
		container:   container,
		storage:     storage,
		defiService: defiService,
	}
}

// MakePayment creates transaction in Safeheron
func (s *safeheronWalletService) MakePayment(userID string, txRequest *polity.TransactionRequest, clientKeys *vault.SafeheronCredentials) (*polity.TransactionResponse, error) {
	var transactionResponse *polity.TransactionResponse
	var sourceAsset safeheron.Asset

	wallet, err := s.storage.GetPolityWalletForUser(txRequest.From, userID)
	if err != nil {
		return nil, err
	}

	for _, asset := range wallet.SafeheronWallet {
		if asset.AssetSymbol == txRequest.Asset {
			sourceAsset = asset
			break
		}
	}

	switch sourceAsset.AccountType {
	case safeheron.Web3Wallet:
		transactionResponse, err = s.makeWeb3Payment(txRequest, clientKeys, wallet, sourceAsset)
		if err != nil {
			return nil, err
		}
		return transactionResponse, nil
	case safeheron.CoinsWallet:
		transactionResponse, err = s.makeCoinsPayment(&sourceAsset, txRequest, clientKeys)
		if err != nil {
			return nil, err
		}
		return transactionResponse, nil
	default:
		return nil, InvalidAccountTypeErr
	}
}

func (s *safeheronWalletService) makeWeb3Payment(txReq *polity.TransactionRequest, credentials *vault.SafeheronCredentials, wallet *polity.Wallet, sourceAsset safeheron.Asset) (*polity.TransactionResponse, error) {
	safeheronClient := &sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	chainClient, err := evm.NewChainClient(s.container, evm.Chain(sourceAsset.AssetSymbol))
	if err != nil {
		return nil, err
	}

	from, err := s.getWeb3WalletAddress(safeheronClient, sourceAsset.SafeheronWallet)
	if err != nil {
		return nil, err
	}

	createdAndSignedTx, err := chainClient.CreateTransaction(from.Address, txReq.Value, txReq.To)
	if err != nil {
		return nil, err
	}

	floatValue, err := strconv.ParseFloat(txReq.Value, 64)
	if err != nil {
		return nil, err
	}

	bigfloat := new(big.Float)
	bigfloat.SetFloat64(floatValue * evm.WeiPerEth)
	bigint := new(big.Int)
	bigfloat.Int(bigint)
	txReq.Value = bigint.String()

	transactionResponse, err := s.BroadcastTransaction(wallet, &polity.TransactionRequest{
		From:     sourceAsset.SafeheronWallet,
		To:       createdAndSignedTx.To().String(),
		Asset:    txReq.Asset,
		Data:     txReq.Data,
		Value:    txReq.Value,
		GasPrice: createdAndSignedTx.GasPrice().String(),
		GasLimit: strconv.FormatUint(createdAndSignedTx.Gas(), 10),
		Nonce:    createdAndSignedTx.Nonce(),
		ChainID:  createdAndSignedTx.ChainId().String(),
	}, chainClient, credentials)
	if err != nil {
		return nil, err
	}
	return transactionResponse, nil
}

func (s *safeheronWalletService) makeCoinsPayment(sourceAsset *safeheron.Asset, txReq *polity.TransactionRequest, clientKeys *vault.SafeheronCredentials) (*polity.TransactionResponse, error) {
	var safeheronRes safeheron.MakePaymentResponse
	var txRes safeheron.TransactionDetails

	safeheronReq := safeheron.MakePaymentRequest{
		SourceAccountKey:       sourceAsset.SafeheronWallet,
		SourceAccountType:      sourceAccountType,
		DestinationAccountType: destinationAccountType,
		DestinationAddress:     txReq.To,
		CoinKey:                txReq.Asset,
		TxAmount:               txReq.Value,
		CustomerRefId:          uuid.New().String(),
	}

	if txReq.GasPrice != "" {
		safeheronReq.FeeRateDto.FeeRate = txReq.GasPrice
		safeheronReq.FeeRateDto.MaxFee = txReq.MaxFee
		safeheronReq.FeeRateDto.MaxPriorityFee = txReq.MaxPriorityFee

		if txReq.GasLimit != "" {
			safeheronReq.FeeRateDto.GasLimit = txReq.GasLimit
		}
	} else {
		safeheronReq.TxFeeLevel = txFeeLevel
	}

	safeheronCient := sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            clientKeys.ApiKey,
			PrivateKey:        []byte(clientKeys.PrivateKey),
			PlatformPublicKey: []byte(clientKeys.PlatformPublicKey),
		},
	}

	err := safeheronCient.SendRequest(safeheronReq, &safeheronRes, createTransactionsPath)
	if err != nil {
		return nil, errors.Wrap(err, ErrWalletProvider)
	}

	err = safeheronCient.SendRequest(safeheronRes, &txRes, getTransactionPath)
	if err != nil {
		return nil, errors.Wrap(err, ErrWalletProvider)
	}

	return &polity.TransactionResponse{
		TransactionID:   safeheronRes.TxKey,
		TransactionHash: txRes.Hash,
		Status:          txRes.Status,
	}, nil
}

// InitializeAssets creates new wallet account in Safeheron
func (s *safeheronWalletService) InitializeAssets(wallet *polity.Wallet, credentials *vault.SafeheronCredentials) error {
	client := &sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	walletID, err := s.storage.GetPolityWalletID(wallet.RequestID)
	if err != nil {
		return err
	}

	err = s.createCoinsWalletAccount(client, walletID, wallet.WalletName)
	if err != nil {
		return err
	}

	err = s.createWeb3WalletAccounts(client, walletID, wallet.WalletName)
	if err != nil {
		return err
	}

	wallet.InitializedAt = time.Now()
	wallet.Status = vault.Provisioned
	err = s.storage.UpdatePolityWalletFields(wallet.RequestID, wallet)
	if err != nil {
		return err
	}

	return nil
}

// CreateCoinsWalletAccount creates assets wallet account in Safeheron, operations on that object should be done via "default" API https://docs.safeheron.com/api/index.html#API%20List
func (s *safeheronWalletService) createCoinsWalletAccount(client *sdk.Client, walletID uint, walletName string) error {
	var response safeheron.CreateWalletAccountResponse

	request := safeheron.CreateWalletAccountRequest{
		AccountName: xid.New().String(),
		HiddenOnUI:  false,
	}

	err := client.SendRequest(request, &response, createCoinsWalletAccountPath)
	if err != nil {
		return errors.Wrap(err, ErrWalletProvider)
	}

	err = s.createDefaultTypesOfCoins(client, response.AccountKey, walletID)
	if err != nil {
		return err
	}

	return nil
}

// createDefaultTypesOfCoins creates default safeheron coins for safeheron assets wallet account
func (s *safeheronWalletService) createDefaultTypesOfCoins(client *sdk.Client, accountKey string, walletID uint) error {
	var addCoinRequests []safeheron.AddCoinRequest
	var err error

	for _, coinKey := range wallets.SafeheronAssetsLists.DefaultCoinsList {
		addCoinRequests = append(addCoinRequests, safeheron.AddCoinRequest{
			AccountKey: accountKey,
			CoinKey:    coinKey,
		})
	}

	for _, addCoinRequest := range addCoinRequests {
		err = client.SendRequest(addCoinRequest, &safeheron.AddCoinResponse{}, createCoinPath)
		if err != nil {
			return errors.Wrap(err, ErrWalletProvider)
		}

		err = s.storage.CreateSafeheronAsset(&safeheron.Asset{
			SafeheronWallet: accountKey,
			AssetSymbol:     addCoinRequest.CoinKey,
			WalletID:        walletID,
			AccountType:     safeheron.CoinsWallet,
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// createWeb3WalletAccount creates web3 wallet account in Safeheron, operations on that object should be done via web3 API https://docs.safeheron.com/api/index.html#Web3%20API
func (s *safeheronWalletService) createWeb3WalletAccounts(client *sdk.Client, walletID uint, walletName string) error {
	var response safeheron.CreateWalletAccountResponse
	var err error

	for _, coinKey := range wallets.SafeheronAssetsLists.Web3List {
		request := safeheron.CreateWalletAccountRequest{
			AccountName: xid.New().String(),
			HiddenOnUI:  false,
		}

		for retries := 0; ; retries++ {
			err = client.SendRequest(request, &response, createWeb3WalletAccountPath)
			if err != nil {
				if retries > retryCreateWeb3AccountQuantity {
					return errors.Wrap(err, ErrWalletProvider)
				}
				time.Sleep(retryCreateWeb3AccountPeriod * time.Second)
				continue
			}
			break
		}

		err = s.storage.CreateSafeheronAsset(&safeheron.Asset{
			SafeheronWallet: response.AccountKey,
			AssetSymbol:     coinKey,
			WalletID:        walletID,
			AccountType:     safeheron.Web3Wallet,
		})
		if err != nil {
			return err
		}
		time.Sleep(retryCreateWeb3AccountPeriod * time.Second)
	}

	return nil
}

// AddAsset - creates for safeheron account new custom coin
func (s *safeheronWalletService) AddAsset(wallet *polity.Wallet, assetSymbol string, credentials *vault.SafeheronCredentials) error {
	var assetAccountType safeheron.AccountType
	var err error

	client := sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	for _, coinKey := range wallets.SafeheronAssetsLists.Web3List {
		if coinKey == assetSymbol {
			assetAccountType = safeheron.Web3Wallet
			break
		}
	}

	switch assetAccountType {
	case safeheron.Web3Wallet:
		var response safeheron.CreateWalletAccountResponse

		request := safeheron.CreateWalletAccountRequest{
			AccountName: xid.New().String(),
			HiddenOnUI:  false,
		}

		for retries := 0; ; retries++ {
			err = client.SendRequest(request, &response, createWeb3WalletAccountPath)
			if err != nil {
				if retries > retryCreateWeb3AccountQuantity {
					return errors.Wrap(err, ErrWalletProvider)
				}
				time.Sleep(retryCreateWeb3AccountPeriod * time.Second)
				continue
			}
			break
		}

		err = s.storage.CreateSafeheronAsset(&safeheron.Asset{
			SafeheronWallet: response.AccountKey,
			AssetSymbol:     assetSymbol,
			WalletID:        wallet.ID,
			AccountType:     safeheron.Web3Wallet,
		})
		if err != nil {
			return err
		}
	default:
		var response safeheron.CreateWalletAccountResponse
		var accountKey string

		for _, asset := range wallet.SafeheronWallet {
			if asset.AccountType == safeheron.CoinsWallet {
				accountKey = asset.SafeheronWallet
				break
			}
		}

		if accountKey == "" {
			request := safeheron.CreateWalletAccountRequest{
				AccountName: xid.New().String(),
				HiddenOnUI:  false,
			}

			err := client.SendRequest(request, &response, createCoinsWalletAccountPath)
			if err != nil {
				return errors.Wrap(err, ErrWalletProvider)
			}
			accountKey = response.AccountKey
		}

		addCoinRequest := safeheron.AddCoinRequest{
			AccountKey: accountKey,
			CoinKey:    assetSymbol,
		}
		addCoinResponse := safeheron.AddCoinResponse{}

		err := client.SendRequest(addCoinRequest, &addCoinResponse, createCoinPath)
		if err != nil {
			return errors.Wrap(err, ErrWalletProvider)
		}

		err = s.storage.CreateSafeheronAsset(&safeheron.Asset{
			SafeheronWallet: accountKey,
			AssetSymbol:     assetSymbol,
			WalletID:        wallet.ID,
			AccountType:     safeheron.CoinsWallet,
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *safeheronWalletService) GetAssets(requestID, userID string, credentials *vault.SafeheronCredentials) (*polity.GetAssetsResponse, error) {
	wallet, err := s.storage.GetPolityWalletForUser(requestID, userID)
	if err != nil {
		return nil, err
	}
	if len(wallet.SafeheronWallet) < 1 {
		return &polity.GetAssetsResponse{
			BalanceUSD: "0",
			Assets:     []polity.Asset{},
		}, nil
	}

	client := &sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	var g errgroup.Group
	type assetBalance struct {
		AssetSymbol string
		Balance     string
		UsdPrice    float64
	}
	assetChan := make(chan assetBalance, len(wallet.SafeheronWallet))

	for _, asset := range wallet.SafeheronWallet {
		asset := asset
		g.Go(func() error {
			var address polity.AssetAddress

			switch asset.AccountType {
			case safeheron.CoinsWallet:
				address, err = s.getCoinsWalletAddress(client, asset.SafeheronWallet, asset.AssetSymbol)
				if err != nil {
					return err
				}
			case safeheron.Web3Wallet:
				address, err = s.getWeb3WalletAddress(client, asset.SafeheronWallet)
				if err != nil {
					return err
				}
			}
			balance, err := s.defiService.GetWalletAssetBalance(defiModels.Wallet{AssetSymbol: asset.AssetSymbol, Address: address.Address})
			if err != nil {
				s.container.GetLogger().Debugf("Can't get asset balance: '%v'. Error: '%v'\n", asset.AssetSymbol, err)
				assetChan <- assetBalance{
					AssetSymbol: asset.AssetSymbol,
					Balance:     "0",
					UsdPrice:    0,
				}
				return err
			}

			assetChan <- assetBalance{
				AssetSymbol: asset.AssetSymbol,
				Balance:     fmt.Sprintf("%.15f", balance.Balance),
				UsdPrice:    balance.BalanceUSD,
			}
			return nil
		})
	}
	go func() {
		if err := g.Wait(); err != nil {
			s.container.GetLogger().Debugf("Can't close chan. Error: '%v'\n", err)
		}
		close(assetChan)
	}()
	if err := g.Wait(); err != nil {
		s.container.GetLogger().Debugf("Can't get balance for all accounts. Error: '%v'\n", err)
	}

	totalBalance := 0.0
	assets := make([]polity.Asset, 0)
	for i := range assetChan {
		s.container.GetLogger().Debugf(" i.AssetSymbol: '%v', i.Balance: '%v', i.UsdPrice: '%v'\n", i.AssetSymbol, i.Balance, i.UsdPrice)
		assets = append(assets, polity.Asset{
			AssetSymbol: i.AssetSymbol,
			Balance:     i.Balance,
			BalanceUSD:  fmt.Sprintf("%.2f", i.UsdPrice),
		})
		totalBalance += i.UsdPrice
	}

	return &polity.GetAssetsResponse{
		BalanceUSD: fmt.Sprintf("%.2f", totalBalance),
		Assets:     assets,
	}, nil
}

func (s *safeheronWalletService) ArchiveAsset(requestID, userID, assetSymbol string) error {
	err := s.storage.ArchiveSafeheronAsset(requestID, userID, assetSymbol)
	if err != nil {
		return err
	}
	return nil
}

func (s *safeheronWalletService) GetAssetAddresses(requestID, userID, asset string, credentials *vault.SafeheronCredentials) ([]polity.AssetAddress, error) {
	resultAssetAddresses := make([]polity.AssetAddress, 0)

	wallet, err := s.storage.GetPolityWalletForUser(requestID, userID)
	if err != nil {
		return nil, err
	}

	client := sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	for _, safeheronAsset := range wallet.SafeheronWallet {
		if safeheronAsset.AssetSymbol == asset {
			switch safeheronAsset.AccountType {
			case safeheron.CoinsWallet:
				address, err := s.getCoinsWalletAddress(&client, safeheronAsset.SafeheronWallet, asset)
				if err != nil {
					return nil, err
				}
				resultAssetAddresses = append(resultAssetAddresses, address)
			case safeheron.Web3Wallet:
				address, err := s.getWeb3WalletAddress(&client, safeheronAsset.SafeheronWallet)
				if err != nil {
					return nil, err
				}
				resultAssetAddresses = append(resultAssetAddresses, address)
			default:
				return nil, InvalidAccountTypeErr
			}
		}
	}

	return resultAssetAddresses, nil
}

func (s *safeheronWalletService) getCoinsWalletAddress(safeheronClient *sdk.Client, accountKey, asset string) (polity.AssetAddress, error) {
	clientResponse := make([]safeheron.GetCoinAddressResponse, 0, len(wallets.SafeheronAssetsLists.DefaultCoinsList))

	clientRequest := safeheron.GetCoinsRequest{
		AccountKey: accountKey,
	}

	err := safeheronClient.SendRequest(clientRequest, &clientResponse, getAccountCoinsPath)
	if err != nil {
		return polity.AssetAddress{}, errors.Wrap(err, ErrWalletProvider)
	}

	for _, coins := range clientResponse {
		if coins.CoinKey == asset {
			// note: bwi do not add several addresses for UTXO-Based coins (first in slice is those we need)
			return polity.AssetAddress{Address: coins.AddressList[0].Address}, nil
		}
	}

	return polity.AssetAddress{}, errors.New("no such asset on safeheron")
}

func (s *safeheronWalletService) getWeb3WalletAddress(safeheronClient *sdk.Client, accountKey string) (polity.AssetAddress, error) {
	var clientResponse []safeheron.GetWeb3WalletAccountResponse

	err := safeheronClient.SendRequest(struct{}{}, &clientResponse, getWeb3AccountsPath)
	if err != nil {
		return polity.AssetAddress{}, errors.Wrap(err, ErrWalletProvider)
	}

	for _, account := range clientResponse {
		if account.AccountKey == accountKey {
			return polity.AssetAddress{Address: account.AddressList[0].Address}, nil
		}
	}

	return polity.AssetAddress{}, errors.New("no such asset on safeheron")
}

func (s *safeheronWalletService) GetPolityWallet(account string) (*polity.Wallet, error) {
	wallet, err := s.storage.GetPolityWalletBySafeheronWallet(account)
	if err != nil {
		return nil, err
	}
	return wallet, nil
}

func (s *safeheronWalletService) BroadcastTransaction(wallet *polity.Wallet, txReq *polity.TransactionRequest, chainClient evm.BlockchainHandlerService, credentials *vault.SafeheronCredentials) (*polity.TransactionResponse, error) {
	customerRefId := uuid.New().String()

	safeheronClient := &sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	s.container.GetLogger().Debugf("Tx to broadcast: %v", txReq)

	txKey, err := s.createSignTransaction(txReq, chainClient, safeheronClient, customerRefId)
	if err != nil {
		return nil, err
	}

	resp, err := s.queryWeb3SignTransaction(customerRefId, safeheronClient)
	if err != nil {
		return nil, err
	}
	s.container.GetLogger().Debugf("Got signed transaction data: %s", resp.Transaction.SignedTransaction)

	txHash, err := chainClient.Broadcast(resp.Transaction.SignedTransaction)
	if err != nil {
		return nil, err
	}
	s.container.GetLogger().Debugf("Broadcast success, transaction hash: %s", txHash)

	return &polity.TransactionResponse{
		TransactionID:   txKey,
		TransactionHash: txHash,
		Status:          resp.TransactionStatus,
	}, nil
}

func (s *safeheronWalletService) createSignTransaction(txReq *polity.TransactionRequest, chainClient evm.BlockchainHandlerService, safeheronClient *sdk.Client, customerRefId string) (string, error) {
	var safeheronRes safeheron.EthSignTransactionResponse

	if txReq.MaxPriorityFee == "" {
		maxFeePerGas, maxPriorityFeePerGas, err := chainClient.GetDefaultFees()
		if err != nil {
			return "", err
		}

		txReq.MaxFee = maxFeePerGas.String()
		txReq.MaxPriorityFee = maxPriorityFeePerGas.String()
	}

	if txReq.GasLimit == "" {
		gasLimit, err := chainClient.GetGasLimit(txReq.From, txReq.To, txReq.Value, txReq.Data)
		if err != nil {
			return "", err
		}
		txReq.GasLimit = gasLimit
	}

	safeheronReq := safeheron.EthSignTransactionRequest{
		AccountKey:    txReq.From,
		CustomerRefId: customerRefId,
		Transaction: safeheron.TransactionToSigh{
			To:                   txReq.To,
			Value:                txReq.Value,
			ChainId:              txReq.ChainID,
			GasLimit:             txReq.GasLimit,
			GasPrice:             txReq.GasPrice,
			MaxPriorityFeePerGas: txReq.MaxPriorityFee,
			MaxFeePerGas:         txReq.MaxFee,
			Nonce:                txReq.Nonce,
			Data:                 txReq.Data,
		},
	}

	err := safeheronClient.SendRequest(safeheronReq, &safeheronRes, signTransactionPath)
	if err != nil {
		return "", errors.Wrap(err, ErrWalletProvider)
	}

	return safeheronRes.TxKey, nil
}

func (s *safeheronWalletService) queryWeb3SignTransaction(customerRefId string, safeheronClient *sdk.Client) (*safeheron.Web3SignQueryResponse, error) {
	var safeheronRes safeheron.Web3SignQueryResponse

	ticker := time.NewTicker(checkTransactionSignResultPeriod * time.Second)
	defer ticker.Stop()

	safeheronReq := safeheron.Web3SignQueryRequest{
		CustomerRefId: customerRefId,
	}

	stopTime := time.Now().Add(time.Minute * stopTickerPeriod) // wait for a day and stop the ticker
	for tick := range ticker.C {
		if tick.After(stopTime) {
			break
		}
		err := safeheronClient.SendRequest(safeheronReq, &safeheronRes, checkTransactionSignResultPath)
		if err != nil {
			return nil, errors.Wrap(err, ErrWalletProvider)
		}
		s.container.GetLogger().Debugf("Transaction status: %v, TxKey: %v", safeheronRes.TransactionStatus, safeheronRes.TxKey)
		if safeheronRes.TransactionStatus == failedStatus || safeheronRes.TransactionStatus == rejectedStatus {
			return nil, fmt.Errorf("web3 sign task was %s", safeheronRes.TransactionStatus)
		} else if safeheronRes.TransactionStatus == signCompleteStatus {
			return &safeheronRes, nil
		}
	}

	return nil, errors.New("can't get web3 sign task result")
}

func (s *safeheronWalletService) getAccountType(safeheronClient *sdk.Client, assetSymbol string) (safeheron.AccountType, error) {
	var coinsList safeheron.CoinsList
	var accountType safeheron.AccountType

	err := safeheronClient.SendRequest(struct{}{}, &coinsList, getCoinsListPath)
	if err != nil {
		return "", errors.Wrap(err, ErrWalletProvider)
	}

	for _, coin := range coinsList.BizContent {
		if coin.Symbol == assetSymbol {
			switch coin.BlockchainType {
			case "EVM":
				accountType = safeheron.Web3Wallet
			default:
				accountType = safeheron.CoinsWallet
			}
			break
		}
	}

	if accountType == "" {
		return "", AssetIsNotSupportedErr
	}

	return accountType, nil
}

func (s *safeheronWalletService) GetAssetTransactions(wallet *polity.Wallet, asset string, paginationParameters pagination.Parameters, credentials *vault.SafeheronCredentials) (*polity.TransactionsHistory, error) {
	client := sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	for _, safeheronAsset := range wallet.SafeheronWallet {
		if safeheronAsset.AssetSymbol == asset {
			switch safeheronAsset.AccountType {
			case safeheron.CoinsWallet:
				transactions, total, err := s.getCoinTransactions(&client, safeheronAsset.SafeheronWallet, asset, paginationParameters)
				if err != nil {
					return nil, err
				}

				return &polity.TransactionsHistory{
					Data:       transactions,
					Pagination: pagination.TransactionsHistoryMeta{Total: total},
				}, nil
			case safeheron.Web3Wallet:
				transactions, total, err := s.getWeb3Transactions(&client, safeheronAsset.SafeheronWallet, asset, paginationParameters)
				if err != nil {
					return nil, err
				}
				return &polity.TransactionsHistory{
					Data:       transactions,
					Pagination: pagination.TransactionsHistoryMeta{Total: total},
				}, nil
			default:
				return nil, InvalidAccountTypeErr
			}
		}
	}

	return nil, errors.New("asset not found")
}

// gatherOutgoingTransactions collects outgoing transaction data and writes to the channel
func (s *safeheronWalletService) gatherOutgoingTransactions(safeheronClient *sdk.Client, accountKey, asset string, outputChan chan<- polity.Transaction) (*errgroup.Group, error) {
	var clientResponse safeheron.GetCoinTransactionsResponse
	g, gCtx := errgroup.WithContext(context.Background())

	clientRequest := safeheron.GetCoinTransactionsRequest{
		PageSize:          "100", // Max for 1 request
		PageNumber:        1,
		TransactionStatus: transactionCompleteStatus,
		CoinKey:           asset,
		SourceAccountKey:  accountKey,
	}

	if err := safeheronClient.SendRequest(clientRequest, &clientResponse, getCoinTransactionsPath); err != nil {
		return nil, errors.Wrap(err, ErrWalletProvider)
	}
	currentPageCount := uint32(len(clientResponse.Content))
	totalCount := uint32(clientResponse.TotalElements)

	if currentPageCount == totalCount {
		g.Go(func() error {
			for _, transaction := range clientResponse.Content {
				date := time.Unix(0, transaction.CreateTime*int64(time.Millisecond))

				var transactionType string
				switch transaction.VaultTxDirection {
				case "RECEIVE":
					transactionType = "received"
				case "SEND":
					transactionType = "sent"
				default:
					return UnexpectedDataErr
				}

				tr := polity.Transaction{
					Date:     date,
					From:     transaction.SourceAddress,
					To:       transaction.DestinationAddress,
					Hash:     transaction.Hash,
					Value:    transaction.Amount,
					Type:     transactionType,
					Gas:      transaction.TxFee,
					ValueUSD: transaction.TxAmountToUsd,
				}
				select {
				case <-gCtx.Done():
					s.container.GetLogger().Errorf("While getting incoming transactions goroutine was interrupted, err: %v", gCtx.Err())
					return gCtx.Err()
				case outputChan <- tr:
				}
			}
			return nil
		})
		return g, nil
	}

	targetPageCount := math.Ceil((float64(totalCount) - float64(currentPageCount)) / 100)
	startPage := 2 // First page already processed
	for page := startPage; page < (int(targetPageCount) + startPage); page++ {
		page := page // https://golang.org/doc/faq#closures_and_goroutines
		clientRequest := clientRequest // https://golang.org/doc/faq#closures_and_goroutines
		g.Go(func() error {
			var clientResponse safeheron.GetCoinTransactionsResponse

			clientRequest.PageNumber = int32(page)
			if err := safeheronClient.SendRequest(clientRequest, &clientResponse, getCoinTransactionsPath); err != nil {
				return errors.Wrap(err, ErrWalletProvider)
			}

			for _, transaction := range clientResponse.Content {
				currentPageCount += uint32(len(clientResponse.Content))
				date := time.Unix(0, transaction.CreateTime*int64(time.Millisecond))

				var transactionType string
				switch transaction.VaultTxDirection {
				case "RECEIVE":
					transactionType = "received"
				case "SEND":
					transactionType = "sent"
				default:
					return UnexpectedDataErr
				}

				tr := polity.Transaction{
					Date:     date,
					From:     transaction.SourceAddress,
					To:       transaction.DestinationAddress,
					Hash:     transaction.Hash,
					Value:    transaction.Amount,
					Type:     transactionType,
					Gas:      transaction.TxFee,
					ValueUSD: transaction.TxAmountToUsd,
				}
				select {
				case <-gCtx.Done():
					s.container.GetLogger().Errorf("While getting incoming transactions goroutine was interrupted, err: %v", gCtx.Err())
					return gCtx.Err()
				case outputChan <- tr:
				}
			}
			return nil
		})
	}
	return g, nil
}

// gatherIncomingTransactions collects incoming transaction data and writes to the channel
func (s *safeheronWalletService) gatherIncomingTransactions(safeheronClient *sdk.Client, accountKey, asset string, outputChan chan<- polity.Transaction) (*errgroup.Group, error) {
	var clientResponse safeheron.GetCoinTransactionsResponse
	g, gCtx := errgroup.WithContext(context.Background())

	clientRequest := safeheron.GetCoinTransactionsRequest{
		PageSize:              "100", // Max for 1 request
		PageNumber:            1,
		TransactionStatus:     transactionCompleteStatus,
		CoinKey:               asset,
		DestinationAccountKey: accountKey,
	}

	if err := safeheronClient.SendRequest(clientRequest, &clientResponse, getCoinTransactionsPath); err != nil {
		return nil, errors.Wrap(err, ErrWalletProvider)
	}
	currentPageCount := uint32(len(clientResponse.Content))
	totalCount := uint32(clientResponse.TotalElements)

	if currentPageCount == totalCount {
		g.Go(func() error {
			for _, transaction := range clientResponse.Content {
				date := time.Unix(0, transaction.CreateTime*int64(time.Millisecond))

				var transactionType string
				switch transaction.VaultTxDirection {
				case "RECEIVE":
					transactionType = "received"
				case "SEND":
					transactionType = "sent"
				default:
					return UnexpectedDataErr
				}

				tr := polity.Transaction{
					Date:     date,
					From:     transaction.SourceAddress,
					To:       transaction.DestinationAddress,
					Hash:     transaction.Hash,
					Value:    transaction.Amount,
					Type:     transactionType,
					Gas:      transaction.TxFee,
					ValueUSD: transaction.TxAmountToUsd,
				}

				select {
				case <-gCtx.Done():
					s.container.GetLogger().Errorf("While getting incoming transactions goroutine was interrupted, err: %v", gCtx.Err())
					return gCtx.Err()
				case outputChan <- tr:
				}
			}
			return nil
		})
		return g, nil
	}

	targetPageCount := math.Ceil((float64(totalCount) - float64(currentPageCount)) / 100)
	startPage := 2 // First page already processed
	for page := startPage; page < (int(targetPageCount) + startPage); page++ {
		page := page // https://golang.org/doc/faq#closures_and_goroutines
		clientRequest := clientRequest // https://golang.org/doc/faq#closures_and_goroutines
		g.Go(func() error {
			var clientResponse safeheron.GetCoinTransactionsResponse

			clientRequest.PageNumber = int32(page)
			if err := safeheronClient.SendRequest(clientRequest, &clientResponse, getCoinTransactionsPath); err != nil {
				return errors.Wrap(err, ErrWalletProvider)
			}

			for _, transaction := range clientResponse.Content {
				currentPageCount += uint32(len(clientResponse.Content))
				date := time.Unix(0, transaction.CreateTime*int64(time.Millisecond))

				var transactionType string
				switch transaction.VaultTxDirection {
				case "RECEIVE":
					transactionType = "received"
				case "SEND":
					transactionType = "sent"
				default:
					return UnexpectedDataErr
				}

				tr := polity.Transaction{
					Date:     date,
					From:     transaction.SourceAddress,
					To:       transaction.DestinationAddress,
					Hash:     transaction.Hash,
					Value:    transaction.Amount,
					Type:     transactionType,
					Gas:      transaction.TxFee,
					ValueUSD: transaction.TxAmountToUsd,
				}
				select {
				case <-gCtx.Done():
					s.container.GetLogger().Errorf("While getting incoming transactions goroutine was interrupted, err: %v", gCtx.Err())
					return gCtx.Err()
				case outputChan <- tr:
				}
			}
			return nil
		})
	}
	return g, nil
}

type TotalTransactions = int64

// getCoinTransactions returns transaction history for a coin
func (s *safeheronWalletService) getCoinTransactions(safeheronClient *sdk.Client, accountKey, asset string, paginationParameters pagination.Parameters) ([]polity.Transaction, TotalTransactions, error) {
	transactions := make([]polity.Transaction, 0, 20)
	combinedTransactionsChan := make(chan polity.Transaction)
	incomingGroup, err := s.gatherIncomingTransactions(safeheronClient, accountKey, asset, combinedTransactionsChan)
	if err != nil {
		return nil, 0, err
	}
	outgoingGroup, err := s.gatherOutgoingTransactions(safeheronClient, accountKey, asset, combinedTransactionsChan)
	if err != nil {
		return nil, 0, err
	}

	go func() {
		_ = incomingGroup.Wait()
		_ = outgoingGroup.Wait()
		s.container.GetLogger().Debugf("Closing chan\n")
		close(combinedTransactionsChan)
	}()

	for val := range combinedTransactionsChan {
		transactions = append(transactions, val)
	}

	if err := incomingGroup.Wait(); err != nil {
		return nil, 0, err
	}
	if err := outgoingGroup.Wait(); err != nil {
		return nil, 0, err
	}

	total := int64(len(transactions))

	if total == 0 {
		return []polity.Transaction{}, 0, nil
	}

	sort.Slice(transactions, func(i, j int) bool { return transactions[i].Date.After(transactions[j].Date) })

	if total <= paginationParameters.Limit {
		return transactions, total, nil
	}

	var lastItemIndex uint16
	if total <= (paginationParameters.Offset+1)*paginationParameters.Limit {
		lastItemIndex = uint16(total)
	} else {
		lastItemIndex = uint16((paginationParameters.Offset + 1) * paginationParameters.Limit)
	}

	return transactions[paginationParameters.Offset*paginationParameters.Limit : lastItemIndex], total, nil
}

func (s *safeheronWalletService) getWeb3Transactions(safeheronClient *sdk.Client, accountKey, asset string, paginationParameters pagination.Parameters) ([]polity.Transaction, int64, error) {
	var clientResponse []safeheron.GetWeb3TransactionResponse
	var transactions []polity.Transaction
	//TODO: get for destination also if possible

	for {
		clientRequest := safeheron.GetWeb3TransactionsRequest{
			StartTime:        defaultStartTime,
			SourceAccountKey: accountKey,
		}

		err := safeheronClient.SendRequest(clientRequest, &clientResponse, getWeb3TransactionsPath)
		if err != nil {
			return nil, 0, errors.Wrap(err, ErrWalletProvider)
		}

		for _, content := range clientResponse {
			if content.Status == signCompleteStatus {
				date := time.Unix(0, content.CreateTime*int64(time.Millisecond))

				var formattedValue string
				if content.Transaction.Value == "" || content.Transaction.Value == "0" {
					formattedValue = ""
				} else {
					value, err := strconv.ParseFloat(content.Transaction.Value, 64)
					if err != nil {
						return nil, 0, err
					}

					formattedValue = strconv.FormatFloat(value/wallets.WeiPerToken, 'f', -1, 64)

				}

				transactions = append(transactions, polity.Transaction{
					Date:  date,
					From:  content.SourceAddress,
					To:    content.Transaction.To,
					Hash:  content.Transaction.Hash,
					Value: formattedValue,
				})
			}
		}

		if len(clientResponse) < maxWeb3PageSize {
			break
		}
	}

	total := int64(len(transactions))
	if total == 0 || total <= paginationParameters.Offset {
		return []polity.Transaction{}, 0, nil
	}

	sort.Slice(transactions, func(i, j int) bool { return transactions[i].Date.After(transactions[j].Date) })

	if total <= paginationParameters.Offset+paginationParameters.Limit {
		return transactions[paginationParameters.Offset:], total, nil
	}

	transactions = transactions[paginationParameters.Offset:(paginationParameters.Offset + paginationParameters.Limit)]

	return transactions, total, nil
}

// PersonalSign is send sign transaction to safeheron api and check its status
func (s *safeheronWalletService) PersonalSign(wallet *polity.Wallet, signData, requestID, assetSymbol string, credentials *vault.SafeheronCredentials) (*polity.TransactionResponse, error) {
	var personalSignResponse safeheron.PersonalSignResponse
	var accountKey string
	customerRefId := uuid.New().String()
	logger := s.container.GetLogger()

	safeheronClient := sdk.Client{
		Config: sdk.ApiConfig{
			BaseUrl:           s.container.GetConfig().SafeHeronBaseUrl,
			ApiKey:            credentials.ApiKey,
			PrivateKey:        []byte(credentials.PrivateKey),
			PlatformPublicKey: []byte(credentials.PlatformPublicKey),
		},
	}

	chainClient, err := evm.NewChainClient(s.container, evm.Chain(assetSymbol))
	if err != nil {
		logger.Errorf("Can`t create new chain client for asset: '%v'. Error: '%v'", assetSymbol, err)
		return nil, err
	}

	addresses, err := s.GetAssetAddresses(requestID, wallet.UserID, assetSymbol, credentials)
	if err != nil {
		logger.Errorf("Can`t get adresses for asset: '%v'. Error: '%v'", assetSymbol, err)
		return nil, err
	}

	chainID, _, err := chainClient.GetAddressDetails(addresses[0].Address)
	if err != nil {
		logger.Errorf("Can`t get chainId: Error: '%v'", err)
		return nil, err
	}

	chainIDInt, err := strconv.Atoi(chainID)
	if err != nil {
		logger.Errorf("Can`t convert string to int for chainId: '%v'. Error: '%v'", chainID, err)
		return nil, err
	}

	for _, asset := range wallet.SafeheronWallet {
		if asset.AssetSymbol == assetSymbol {
			accountKey = asset.SafeheronWallet
			break
		}
	}

	decoded, err := hex.DecodeString(signData[2:])
	if err != nil {
		logger.Errorf("Can`t decode string: Error: '%v'", err)
		return nil, err
	}

	personalSignReq := safeheron.PersonalSignRequest{
		AccountKey:    accountKey,
		CustomerRefId: customerRefId,
		Message: safeheron.Message{
			ChainId: int64(chainIDInt),
			Data:    string(decoded),
		},
	}

	err = safeheronClient.SendRequest(personalSignReq, &personalSignResponse, personalSign)
	if err != nil {
		logger.Errorf("Can`t send request to safecheron api. Error: '%v'", err)
		return nil, err
	}

	resp, err := s.queryWeb3SignTransaction(customerRefId, &safeheronClient)
	if err != nil {
		logger.Errorf("Can`t get sign transaction status. Error: '%v'", err)
		return nil, err
	}

	return &polity.TransactionResponse{
		TransactionID:   resp.TxKey,
		Status:          resp.TransactionStatus,
		TransactionHash: "0x" + resp.Message.Sig.Sig,
	}, nil
}
