package service

import (
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/dfns"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/notification"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/pagination"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/safeheron"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/evm"
)

// WalletInfrastructureService is a universal interface to interact with different wallets
type WalletInfrastructureService[T vault.DataConstraints] interface {
	MakePayment(userID string, request *polity.TransactionRequest, credentials *T) (*polity.TransactionResponse, error)
	InitializeAssets(wallet *polity.Wallet, credentials *T) error
	AddAsset(wallet *polity.Wallet, assetSymbol string, credentials *T) error
	GetAssets(requestID, userID string, credentials *T) (*polity.GetAssetsResponse, error)
	ArchiveAsset(requestID, assetSymbol, userID string) error
	GetPolityWallet(account string) (*polity.Wallet, error)
	GetAssetAddresses(requestID, userID, asset string, credentials *T) ([]polity.AssetAddress, error)
	BroadcastTransaction(wallet *polity.Wallet, r *polity.TransactionRequest, client evm.BlockchainHandlerService, credentials *T) (*polity.TransactionResponse, error)
	GetAssetTransactions(wallet *polity.Wallet, assetSymbol string, paginationParameters pagination.Parameters, credentials *T) (*polity.TransactionsHistory, error)
	PersonalSign(wallet *polity.Wallet, signData, requestID, assetSymbol string, credentials *T) (*polity.TransactionResponse, error)
}

// WalletRequestsService is an interface to create wallet creation requests
type WalletRequestsService interface {
	CreatePolityWallet(wallet *polity.Wallet) error
	CreateWalletRequest(wallet *polity.Wallet, token string) error
	UpdatePolityWalletStatus(requestID string, status vault.Status) error
	UpdatePolityWalletName(request polity.RenameWalletRequest) error
	GetPolityWallet(requestID string) (*polity.Wallet, error)
	GetPolityWalletForUser(requestID, userID string) (*polity.Wallet, error)
	GetDefiProducts() ([]defi.DefiProduct, error)
	GetPolityWalletsForUser(userID string) ([]polity.Wallet, error)
	GetPolityWalletsByTypeAndUserID(walletType, userID string) ([]polity.Wallet, error)
	ArchivePolityWallet(requestID, userID string) error
}

// SafeheronCallbackService is an interface to approve safeheron transactions
type SafeheronCallbackService interface {
	ResolveApprovalTask(request *safeheron.EncryptedCallbackRequest, credentials *vault.SafeheronCredentials) (*safeheron.EncryptedCallbackResponse, error)
	ProcessNotification(request *safeheron.EncryptedCallbackRequest, credentials *vault.SafeheronCredentials) (*notification.TransactionPayload, error)
}

// DFNSCallbackService represents methods for webhook connection with DFNS. We use it for approving DFNS transactions
type DFNSCallbackService interface {
	ProcessNotification(request *dfns.GetPaymentByIDResp) (*notification.TransactionPayload, error)
	CreateWebhook(request *dfns.CreateWebhookRequest, credentials *vault.DFNSCredentials) (*dfns.Webhook, error)
	GetListWebhooks(credentials *vault.DFNSCredentials) (*dfns.ListWebhooksResponse, error)
	DeleteWebhook(webhookId string, credentials *vault.DFNSCredentials) (bool, error)
}

// FavoriteAddressService is an interface to make common wallets' operation (pin favourites, archive, etc.)
type FavoriteAddressService interface {
	AddAddress(favouriteAddress *polity.FavouriteAddressEntry) error
	AddAdvisorAddresses(userID string, favouriteAddresses []*polity.FavouriteAddressEntry) error
	RemoveAddress(favouriteAddress *polity.FavouriteAddressEntry) error
	GetAddressesByUserID(userID string) ([]polity.FavouriteAddressEntry, error)
}
