package defi

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/machinebox/graphql"
	"golang.org/x/sync/errgroup"
)

const (
	ErrUnsupportedAsset     = "wallet chain is not supported for DeFi"
	ErrAssetBalanceNotFound = "asset balance is not found"
)

var (
	depositsFeatures = []string{"delegation", "staking", "pools", "lockedBalances"}
	lendingFeatures  = []string{"lending"}
)

type Service interface {
	GetWalletAssetsBalances(wallet defi.Wallet) ([]defi.AssetBalance, error)
	GetWalletAssetBalance(wallet defi.Wallet) (*defi.AssetBalance, error)
	GetWalletDepositsBalance(wallet defi.Wallet) (*defi.ChainProtocols, error)
	GetWalletLendingBalance(wallet defi.Wallet) (*defi.ChainProtocols, error)
}

// defiService wrapper for the interface implementation
type defiService struct {
	container       container.WalletInfrastructureContainer
	graphqlClient   *graphql.Client
	chains          map[int]string
	networkFeatures defi.NetworkFeatures
}

// NewDefiService constructs an instance of the User Infrastructure Client
func NewDefiService(container container.WalletInfrastructureContainer) (Service, error) {
	graphqlClient := graphql.NewClient(container.GetConfig().DeFiApiUrl)
	chains, err := getChains(graphqlClient, container.GetConfig().DeFiApiKey)
	if err != nil {
		return nil, err
	}

	networkFeatures, err := getNetworkFeatures(graphqlClient, container.GetConfig().DeFiApiKey)
	if err != nil {
		return nil, err
	}

	return &defiService{
		graphqlClient:   graphqlClient,
		container:       container,
		chains:          chains,
		networkFeatures: networkFeatures,
	}, nil
}

func getChains(graphqlClient *graphql.Client, apiKey string) (map[int]string, error) {
	var response *defi.ChainsDefiResponse
	chains := make(map[int]string, 50)

	request := graphql.NewRequest(queryGetChains)
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("X-Api-Key", apiKey)

	err := graphqlClient.Run(context.Background(), request, &response)
	if err != nil {
		return nil, err
	}

	for _, chain := range response.Chains {
		chains[chain.ID] = chain.AbsoluteChainID
	}

	return chains, nil
}

func getNetworkFeatures(graphqlClient *graphql.Client, apiKey string) (defi.NetworkFeatures, error) {
	var response *defi.ProtocolDefiResponse
	networkFeatures := make(map[int]*defi.Feature, 50)

	request := graphql.NewRequest(queryGetProtocols)
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("X-Api-Key", apiKey)

	err := graphqlClient.Run(context.Background(), request, &response)
	if err != nil {
		return defi.NetworkFeatures{}, err
	}

	for _, protocol := range response.Protocols {
		for _, features := range protocol.Features {
			for _, feature := range features.List {
				_, ok := networkFeatures[features.Chain.Id]
				if !ok {
					newFeatureProtocolsMap := make(map[string][]string)
					networkFeatures[features.Chain.Id] = &defi.Feature{FeatureProtocols: newFeatureProtocolsMap, MaxProtocolsQuantity: 0}
				}
				networkFeatures[features.Chain.Id].FeatureProtocols[feature] = append(networkFeatures[features.Chain.Id].FeatureProtocols[feature], protocol.Slug)
				networkFeatures[features.Chain.Id].MaxProtocolsQuantity += 1
			}
		}
	}

	return defi.NetworkFeatures{NetworkFeatures: networkFeatures}, nil
}

func (d *defiService) GetWalletAssetsBalances(wallet defi.Wallet) ([]defi.AssetBalance, error) {
	var balances []defi.AssetBalance
	var chainID int
	var absChainID string

	d.container.GetLogger().Debugf("defi chains (chain:absolute chain): %v", d.chains)
	for defiChainID, absoluteChainID := range d.chains {
		if wallets.BaseNetworksAssets[wallet.AssetSymbol] == absoluteChainID {
			chainID = defiChainID
			absChainID = absoluteChainID
			d.container.GetLogger().Debugf("chainID: %d", chainID)
			break
		}
	}

	if chainID == 0 {
		d.container.GetLogger().Debugf("Chain ID is not supported for DeFi: asset %s", wallet.AssetSymbol)
		return nil, errors.New(ErrUnsupportedAsset)
	}

	response, err := d.getBalances(wallet.Address, wallet.AssetSymbol, chainID)
	if err != nil {
		return nil, err
	}	

	for _, balance := range response.AssetBalances.Assets {
		if absChainID == d.chains[balance.Asset.ChainID] {
			continue
		}

		balances = append(balances, defi.AssetBalance{
			Name:        balance.Asset.Name,
			DisplayName: balance.Asset.DisplayName,
			AssetSymbol: balance.Asset.Symbol,
			Address:     balance.Asset.Address,
			ChainID:     d.chains[balance.Asset.ChainID],
			Icon:        balance.Asset.Icon,
			Price:       balance.Asset.Price,
			Balance:     balance.Balance,
			BalanceUSD:  balance.Total,
		})
	}

	return balances, nil
}

func (d *defiService) GetWalletAssetBalance(wallet defi.Wallet) (*defi.AssetBalance, error) {
	var chainID int

	d.container.GetLogger().Debugf("defi chains (chain:absolute chain): %v", d.chains)
	for defiChainID, absoluteChainID := range d.chains {
		if wallets.BaseNetworksAssets[wallet.AssetSymbol] == absoluteChainID {
			chainID = defiChainID
			d.container.GetLogger().Debugf("chainID: %d", chainID)
			break
		}
	}

	if chainID == 0 {
		d.container.GetLogger().Debugf("Chain ID is not supported for DeFi: asset %s", wallet.AssetSymbol)
		return nil, errors.New(ErrUnsupportedAsset)
	}

	response, err := d.getBalances(wallet.Address, wallet.AssetSymbol, chainID)
	if err != nil {
		return nil, err
	}

	for _, balance := range response.AssetBalances.Assets {
		if balance.Asset.ChainID != chainID {
			continue
		}

		return &defi.AssetBalance{
			AssetSymbol: balance.Asset.Symbol,
			Balance:     balance.Balance,
			BalanceUSD:  balance.Total,
		}, nil
	}

	return nil, errors.New(ErrAssetBalanceNotFound)
}

func (d *defiService) getBalances(address, assetSymbol string, chainID int) (*defi.AssetBalancesDefiResponse, error) {
	var response *defi.AssetBalancesDefiResponse

	d.container.GetLogger().Debugf("k: %s", d.container.GetConfig().DeFiApiKey[0:2])
	if chainID == 0 {
		d.container.GetLogger().Debugf("Chain ID is not supported for DeFi: asset %s", assetSymbol)
		return nil, errors.New(ErrUnsupportedAsset)
	}

	query := fmt.Sprintf(queryGetBalances, chainID, address)

	request := graphql.NewRequest(query)
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("X-Api-Key", d.container.GetConfig().DeFiApiKey)

	err := d.graphqlClient.Run(context.Background(), request, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (d *defiService) GetWalletDepositsBalance(wallet defi.Wallet) (*defi.ChainProtocols, error) {
	response := defi.ChainProtocols{}

	protocolBalances, err := d.getProtocolBalances(wallet.Address, wallet.AssetSymbol, depositsFeatures)
	if err != nil {
		return nil, err
	}

	response.ProtocolBalances = protocolBalances

	for _, v := range protocolBalances {
		response.TotalBorrowed += v.TotalBorrowed
		v.TotalBorrowed = d.roundToLower(v.TotalBorrowed)

		response.TotalSupplied += v.TotalSupplied
		v.TotalSupplied = d.roundToLower(v.TotalSupplied)

		response.TotalRewarded += v.TotalRewarded
		v.TotalRewarded = d.roundToLower(v.TotalRewarded)

		response.TotalNetWorth += v.TotalNetWorth
		v.TotalNetWorth = d.roundToLower(v.TotalNetWorth)
	}

	response.TotalBorrowed = d.roundToLower(response.TotalBorrowed)
	response.TotalSupplied = d.roundToLower(response.TotalSupplied)
	response.TotalRewarded = d.roundToLower(response.TotalRewarded)
	response.TotalNetWorth = d.roundToLower(response.TotalNetWorth)

	return &response, nil
}

func (d *defiService) GetWalletLendingBalance(wallet defi.Wallet) (*defi.ChainProtocols, error) {
	response := defi.ChainProtocols{}

	protocolBalances, err := d.getProtocolBalances(wallet.Address, wallet.AssetSymbol, lendingFeatures)
	if err != nil {
		return nil, err
	}

	response.ProtocolBalances = protocolBalances

	for _, v := range protocolBalances {
		response.TotalBorrowed += v.TotalBorrowed
		v.TotalBorrowed = d.roundToLower(v.TotalBorrowed)

		response.TotalSupplied += v.TotalSupplied
		v.TotalSupplied = d.roundToLower(v.TotalSupplied)

		response.TotalRewarded += v.TotalRewarded
		v.TotalRewarded = d.roundToLower(v.TotalRewarded)

		response.TotalNetWorth += v.TotalNetWorth
		v.TotalNetWorth = d.roundToLower(v.TotalNetWorth)
	}

	response.TotalBorrowed = d.roundToLower(response.TotalBorrowed)
	response.TotalSupplied = d.roundToLower(response.TotalSupplied)
	response.TotalRewarded = d.roundToLower(response.TotalRewarded)
	response.TotalNetWorth = d.roundToLower(response.TotalNetWorth)

	return &response, nil
}

func (d *defiService) getProtocolBalances(address, assetSymbol string, features []string) ([]defi.ProtocolBalances, error) {
	response := []defi.ProtocolBalances{}
	var chainID int

	for defiChainID, absoluteChainID := range d.chains {
		if wallets.BaseNetworksAssets[assetSymbol] == absoluteChainID {
			chainID = defiChainID
			break
		}
	}

	if chainID == 0 {
		d.container.GetLogger().Debugf("Chain ID is not supported for DeFi: asset %s", assetSymbol)
		return nil, errors.New(ErrUnsupportedAsset)
	}

	d.container.GetLogger().Debugf("START get protocols at %v \n", time.Now())
	var g errgroup.Group
	protocolsChan := make(chan *defi.ProtocolBalances, d.networkFeatures.NetworkFeatures[chainID].MaxProtocolsQuantity)

	for _, feature := range features {
		feature := feature
		for _, protocol := range d.networkFeatures.NetworkFeatures[chainID].FeatureProtocols[feature] {
			protocol := protocol

			g.Go(func() error {
				var protocolResponse *defi.ProtocolBalancesDefiResponse

				query := fmt.Sprintf(queryGetProtocolBalances, chainID, protocol, address)
				request := graphql.NewRequest(query)
				request.Header.Set("Content-Type", "application/json")
				request.Header.Set("X-Api-Key", d.container.GetConfig().DeFiApiKey)

				err := d.graphqlClient.Run(context.Background(), request, &protocolResponse)
				if err != nil {
					d.container.GetLogger().Debugf("Err from defi: %v", err)
					return err
				}

				for _, protocolBalance := range protocolResponse.ProtocolBalance {
					if protocolBalance.Total == 0 {
						continue
					}

					balance := &defi.ProtocolBalances{
						Name:          protocolBalance.Protocol.Name,
						Slug:          protocolBalance.Protocol.Slug,
						TotalNetWorth: protocolBalance.Total,
					}

					var positions []defi.Position
					for _, position := range protocolBalance.Chains[0].Positions {
						if position.Feature != feature {
							continue
						} else {
							totalRewarded := d.countTotalValue(position.Rewarded)
							totalSupplied := d.countTotalValue(position.Supplied)
							totalBorrowed := d.countTotalValue(position.Borrowed)

							newPosition := defi.Position{
								Supplied: d.formatValues(position.Supplied),
								Borrowed: d.formatValues(position.Borrowed),
								Rewarded: d.formatValues(position.Rewarded),

								TotalRewarded: totalRewarded,
								TotalSupplied: totalSupplied,
								TotalBorrowed: totalBorrowed,
								TotalNetWorth: totalSupplied - totalBorrowed,
							}

							balance.TotalRewarded += totalRewarded
							balance.TotalSupplied += totalSupplied
							balance.TotalBorrowed += totalBorrowed

							positions = append(positions, newPosition)
						}
					}

					balance.Positions = positions
					protocolsChan <- balance
				}
				return nil
			})
		}
	}

	if err := g.Wait(); err != nil {
		d.container.GetLogger().Debugf("Can't get balance for all protocols. Error: '%v'\n", err)
	}
	close(protocolsChan)

	for protocol := range protocolsChan {
		response = append(response, *protocol)
	}

	d.container.GetLogger().Debugf("FINISH get protocols at %v", time.Now())

	return response, nil
}

func (d *defiService) formatValues(positions []defi.FeatureDetails) []defi.FeatureDetails {
	for i, v := range positions {
		positions[i].Value = d.roundToLower(v.Value)
		positions[i].APR = d.roundToLower(v.APR)
		positions[i].Token.Price = d.roundToLower(v.Token.Price)
	}

	return positions
}

func (d *defiService) countTotalValue(positions []defi.FeatureDetails) float64 {
	var total float64

	for _, v := range positions {
		total += v.Value
	}

	return total
}

func (d *defiService) roundToLower(value float64) float64 {
	return math.Floor(value*100) / 100
}
