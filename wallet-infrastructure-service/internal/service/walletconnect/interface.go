package walletconnect

import (
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/blowfish"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/identity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/walletconnect"
)

type HandlerService interface {
	Process(user identity.Identity, request *walletconnect.Request, assetSymbol string) (*polity.TransactionResponse, error)
	Scan(user identity.Identity, request *walletconnect.Request, assetSymbol string) (*blowfish.ScanTransactionResponse, error)
}
