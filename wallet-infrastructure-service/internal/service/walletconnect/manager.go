package walletconnect

import (
	"fmt"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/walletconnect/eth"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/blowfish"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/polityVault"
)

const (
	ethPaymentMethod   = "eth_sendTransaction"
	personalSignMethod = "personal_sign"
)

type HandlerManager struct {
	container             container.WalletInfrastructureContainer
	walletRequestsService service.WalletRequestsService

	// Cached handlers
	ethPayment   HandlerService
	personalSign HandlerService
}

func NewHandlerManager(container container.WalletInfrastructureContainer, walletRequestsService service.WalletRequestsService, polityWalletClient polityVault.Client, safeheron service.WalletInfrastructureService[vault.SafeheronCredentials], dfns service.WalletInfrastructureService[vault.DFNSCredentials], blowfishService blowfish.Service) *HandlerManager {
	// define sub-handlers
	ethPayment := eth.NewPaymentHandler(container, walletRequestsService, polityWalletClient, safeheron, dfns, blowfishService)
	personalSign := NewPersonalSignHandler(container, walletRequestsService, polityWalletClient, safeheron, dfns, blowfishService)

	return &HandlerManager{
		container:             container,
		walletRequestsService: walletRequestsService,
		ethPayment:            &ethPayment,
		personalSign:          &personalSign,
	}
}

func (h *HandlerManager) GetMethodHandler(assetSymbol, requestMethod string) (HandlerService, error) {
	var handler HandlerService
	switch assetSymbol {
	case wallets.AssetSymbolEthereum:
		switch requestMethod {
		case ethPaymentMethod:
			handler = h.ethPayment
		case personalSignMethod:
			handler = h.personalSign
		default:
			return nil, fmt.Errorf("Unknown eth walletconnect, %s ", requestMethod)
		}
	case wallets.AssetSymbolPolygon:
		switch requestMethod {
		case ethPaymentMethod:
			handler = h.ethPayment
		case personalSignMethod:
			handler = h.personalSign
		default:
			return nil, fmt.Errorf("Unknown poligon walletconnect, %s ", requestMethod)
		}
	default:
		return nil, fmt.Errorf("Unknown asset symbol, %s ", assetSymbol)
	}

	return handler, nil
}
