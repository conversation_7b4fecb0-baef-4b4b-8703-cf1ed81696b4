package walletconnect

import (
	"encoding/json"
	"fmt"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	blowfishModels "github.com/Polity_MVP/wallet-infrastructure-service/internal/models/blowfish"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/identity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/walletconnect"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/blowfish"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/polityVault"
)

type PersonalSignHandler struct {
	container             container.WalletInfrastructureContainer
	walletRequestsService service.WalletRequestsService
	polityVaultClient     polityVault.Client
	safeheronService      service.WalletInfrastructureService[vault.SafeheronCredentials]
	dfnsService           service.WalletInfrastructureService[vault.DFNSCredentials]
}

func NewPersonalSignHandler(
	container container.WalletInfrastructureContainer,
	walletRequestsService service.WalletRequestsService,
	polityVaultClient polityVault.Client,
	safeheronService service.WalletInfrastructureService[vault.SafeheronCredentials],
	dfnsService service.WalletInfrastructureService[vault.DFNSCredentials],
	blowfishService blowfish.Service) PersonalSignHandler {
	return PersonalSignHandler{
		container:             container,
		walletRequestsService: walletRequestsService,
		polityVaultClient:     polityVaultClient,
		safeheronService:      safeheronService,
		dfnsService:           dfnsService,
	}
}

func (p *PersonalSignHandler) Process(user identity.Identity, request *walletconnect.Request, assetSymbol string) (*polity.TransactionResponse, error) {
	logger := p.container.GetLogger()
	source := (*json.RawMessage)(&request.Data)
	requestPersonalSign := walletconnect.PersonalSignRequest{}
	var transactionResponse *polity.TransactionResponse

	if err := json.Unmarshal(*source, &requestPersonalSign); err != nil {
		return nil, err
	}

	err := p.container.GetValidator().Validator.Struct(requestPersonalSign)
	if err != nil {
		return nil, err
	}

	wallet, err := p.walletRequestsService.GetPolityWalletForUser(request.RequestID, user.UserID)
	if err != nil {
		logger.Errorf("Cant get polity wallet for user. Error: '%v'", err)
		return nil, err
	}

	switch wallet.Type {
	case wallets.SafeHeron:
		credentials, _, err := p.polityVaultClient.GetSafeheronWalletCredentials(
			user.Token, p.container.GetConfig().SafeheronCosignerUuid, p.polityVaultClient.DefaultOptions())
		if err != nil {
			p.container.GetLogger().Errorf("Can`t get credentials for Safeheron. Error: '%v'", err)
			return nil, err
		}

		transactionResponse, err = p.safeheronService.PersonalSign(wallet, requestPersonalSign.SignData, request.RequestID, assetSymbol, credentials)
		if err != nil {
			logger.Errorf("Can`t create personal sign for safeheron wallet: '%v'. Error: '%v'", wallet.WalletName, err)
			return nil, err
		}

	case wallets.DFNS:
		credentials, _, err := p.polityVaultClient.GetDFNSWalletCredentials(user.Token)
		if err != nil {
			p.container.GetLogger().Errorf("Can`t get credentials for Safeheron. Error: '%v'", err)
			return nil, err
		}

		transactionResponse, err = p.dfnsService.PersonalSign(wallet, requestPersonalSign.SignData, request.RequestID, assetSymbol, credentials)
		if err != nil {
			logger.Errorf("Can`t create personal sign for safeheron wallet: '%v'. Error: '%v'", wallet.WalletName, err)
			return nil, err
		}
	default:
		return nil, fmt.Errorf("Unknown wallet type, %s ", wallet.Type)
	}

	return transactionResponse, nil
}

// TODO: write Scan method if you need
func (p *PersonalSignHandler) Scan(user identity.Identity, request *walletconnect.Request, assetSymbol string) (*blowfishModels.ScanTransactionResponse, error) {
	return nil, nil
}
