package dfns

import "gorm.io/gorm"

type Asset struct {
	gorm.Model
	Kind     string  `json:"kind"`
	Contract string  `json:"contract"`
	Symbol   string  `json:"symbol"`
	Decimals float64 `json:"decimals"`
	Verified bool    `json:"verified"`
	Balance  string  `json:"balance"`

	DfnsWalletID uint
}

// Networks is a struct that represents lists of network entities for DFNS
type Networks struct {
	DefaultNetworksList []string
	CoinMarketCupMap    map[string]string
}

type GetAssetsResponse struct {
	WalletId string  `json:"walletId"`
	Network  string  `json:"network"`
	Assets   []Asset `json:"assets"`
}

// TableName overrides the table name used by Asset to `dfns_assets`
func (Asset) TableName() string {
	return "dfns_wallet_assets"
}
