package dfns

type GetPaymentsResponse struct {
	Items []struct {
		Index       string   `json:"index"`
		Fee         string   `json:"fee"`
		TxHash      string   `json:"txHash"`
		BlockNumber int64    `json:"blockNumber"`
		Timestamp   string   `json:"timestamp"`
		Contract    string   `json:"contract"`
		WalletId    string   `json:"walletId"`
		Network     string   `json:"network"`
		Value       string   `json:"value"`
		Kind        string   `json:"kind"`
		Froms       []string `json:"froms"`
		Direction   string   `json:"direction"`
		Tos         []string `json:"tos"`
		Metadata    struct {
			Asset struct {
				Symbol   string `json:"symbol"`
				Decimals int64  `json:"decimals"`
				Verified bool   `json:"verified"`
				Quotes   struct {
					Eur float64 `json:"EUR"`
					Usd float64 `json:"USD"`
				} `json:"quotes"`
			} `json:"asset"`
			Fee struct {
				Symbol   string `json:"symbol"`
				Decimals int64  `json:"decimals"`
				Verified bool   `json:"verified"`
				Quotes   struct {
					Eur float64 `json:"EUR"`
					Usd float64 `json:"USD"`
				} `json:"quotes"`
			} `json:"fee"`
		} `json:"metadata"`
	} `json:"items"`
	NextPageToken string `json:"nextPageToken"`
}

type GetBroadcastedTransactionsResponse struct {
	Items []struct {
		DateCreated string `json:"dateCreated"`
		Hash        string `json:"txHash"`
		Transaction struct {
			To    string `json:"to"`
			Value string `json:"value"`
		} `json:"transaction"`
	} `json:"items"`
}

type GenerateSignatureRequest struct {
	// Transaction, Message, EIP712, Hash
	// Link to documentation: https://docs.dfns.co/d/api-docs/wallets/generate-signature-from-wallet/evm-generate-signature
	Kind string `json:"kind"`
	// The unsigned hex encoded transaction as shown below
	Message string `json:"message"`
}

type GenerateSignatureResponse struct {
	Id        string `json:"id"`
	Status    string `json:"status"`
	Signature struct {
		Encoded string `json:"encoded"`
	} `json:"signature"`
}
