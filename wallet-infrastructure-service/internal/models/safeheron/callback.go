package safeheron

// EncryptedCallbackRequest is encrypted callback request from Safeheron
type EncryptedCallbackRequest struct {
	BizContent string `json:"bizContent" example:"4m/ra7f3gyJYmqHL8rTpMAR+dakd/rgnTkXNNAKc6zb3fomWaxR8KS/szHnwqU0XMk01wAqnKs3UMSsaXvJES3LkJFuoSP7r1U7bepDih24ESpuxQ3M+JvL3yw6U51yXa2prAFg+8T8uKhUfyxflXod6M5ROj/MMsd0aXxXI3ZbTbiET85kPxk0OUmY3yc7LgVb7fyncxfLCHGTErkTH14rXevomOZdyFp7/mkt2hW4p/J75oJ4eMsvBGE7Z3T7WbYE/Y+K0y28VNfvxcqpENt5kbHFqWUycVtQKgUShJEZkk/9ddTzrU+nqzz4fddTwCdpG9QgiYSV30rA7+PxMJwnWRtbhOtYFnc1X61BA+blC6qm8Du9kTbruG17Rrxf+Ej0Adwv90m/Oe++Qo0cByoLvBCUINcKKKOVDTy6tPX4MKjdQimgluW8Gn7UF28vV/6WyV+5z/dOsP6H2Uym/rVbSHGR+vjVYy8TxxJwr8tL6QrIEw7RnVAtGsS+RMA5I8Un4bam7CdVPBybQ+TPCKlYK39x2ReSEIpi9ZpJxxxS6zmFo70jfk0/1s7safkYRI3NecuvaTT7qJjIOvDwzUhfNsh07+P2SC/sutIgQj4qkxftwpO31VnHfwFN2yH0/279y37cJpzQbs7trSE9BQAnQZL1EN6VmmNBgz74u5JnIrxrLgUk4YErQudWleEFJv4j0tSNT4EZgbvi6GYfpETlPSx3MkhfD+P7TMn+K+ho4NrUH5wClePxc3k2hxB2BvG7GIEoRclN+vhfHxUOPEDPI/iCzmWTeyz7Exfa2sY4Zq52j23pW7qrPjhfi18uJYc1vrlvkAiSI0v3eDlKCLXQ25hpoSoPHXuWJDRY53PILmgQxMnFGviaLjGpjuvsVSZlcf1+tfq8BdtAiy/p3j3LX5E6/uE/Toczm4lNHlURw8DijVF8HCrVXcDXOjHJK2R8GSXvflQbupU8GR+7zDIODM/WfvF9ARGk7+iZe5FtNHTz4tEkxURFwa3iatvhjxdItXbHLCrRTKCXnidgNkuL606Mc4jb7WypuDyDl9MkdcBVRvX5alAZJIGSTdZdp6wo/+GsYh31jWA1ozLaDyuB3GhCJ/YFmgFriRxVVxje1mt84SSyh9eeRHZSQONn2qIwgW12ZolFzDd4JWc3+NFKu4cZk75QfnNhex0/wWVkC3kCMizzoPKuQSIBj6rUanshoEyQf76HE2h01qTxlrSa4XS0NdpEcZY7g7ftigx35u4geiv4yYDwyk82gdpGyDb+F51wWrlGIlDQWNT7tPw=="`
	Signature  string `json:"sig" example:"dA4HBamyVlBtwHKTV17ULv3pcl6ks0r6qnYn8scNnyAn8LALtdBJfpCLSXnn4NgFuVwe9hDxDP6DS5PzZcEeCsx1oGPqLQnS/GKNfxDZTB5BsXN6C+Q/qM9ffcCfSwFna2ZEqGQ6yH8s9wqM6ld/nYDjx1dVhBfB8PXOK22M7SVIaLg0wrqwI4EWIObJxrJGhK34w+v1to4DFiTcqVNhgcn+GmG5HRTBPSNculaDZ1hih4Pd+15cUi7e1gjjfOwDpt6dDSZ4I7FT24yfFzYjNbMpziaFqQySqqWL6cI0tgUQrlSnMv6NIRc1EVDtwLamcLPIqffvZqp0GyRZtCzNykjU/aNrqRnLj7oemOASWUq9NGuFHkILdvsQapAx8YH8S6sXr4GNPYoo/H06M6MorxugZbbuInQ9IjE19t4o7U9qossqVYTYKchb2NGnERDtY1JVpRSOVAd817g7AoLQro1Hx25HKARIofWDtOeTHhMqb+txidzZnQveIw4h32aY3/DtZE0Qt83pqOUJx1/grXDjhLXnYNGdXWfAWjG/cbf3qjawcefWh6t73ZNlcpS3guYDUURMTSwEnT41JClXMI2UpUzLisF0w5sYwwGjC6xu89wD70vMPPF3QN5+XGDREmRRIo4SGaY174n+x+1WEG9seyWCOvSpZ76Yi6u6fd4="`
	Key        string `json:"key" example:"UytGIJVrDzcgst/ihNrptcZwt8thcEO126yg3lWSUU2MSyrId9R0UnpI/bm2QebNKvO0tATZ+4gSW65KuHAiSlNjsoluswd47F5KDZZHqPXSXuTuuPPo7sYMhAPy3JfGkWVcH/nOfANFNWYptHyHljpvYWqzPVNmqJ0i6z9fMpzkGhtfl5wzR+TEpnIpS0uevYwcnxN6ay3CLCtKgZBfgilnoTaq0bBU8RqvyX4lXoYIXiYOqJVnuA1G0gCOt57UYhYcFylmRnagjvpQ8w30Y8G9mGsU4hmC6cu1RtH5iKlD0tJU3Xnk28SUOf9OABOKIau5C2JeVkRshpOQfTmCAYTJuGCIN+XdzYlFnjc7icxNf6UgTSfw3Kg3V/QOQA2OU8QRNFi5VWvF6i12bEUmMQcVBKe3HDpVev9GMzUErtaBqFkFlxJMQJAOV4aXnuBKXgRbMzix3/kTS5t7ynxhJCVp2XErYb5XmrAHFQ49Cm8Nor3dElb/Yvl50jd1PNTsyu56zfv7qHt+TUM5xI7zoSAckBNNP7o8TjZJtFqUrVvM0QEm8jOo6tsNuKgaiI0VPxTf6ER/sl+j5kzSH07TAWheIDeqMU/Trtz1ZPTSyOW4JVZuQQX4xfrZtMZyLagnUVYvOHzbyj8xmZTtd5MXE992dHCXIjn/A8+jXURJVZg="`
	Timestamp  string `json:"timestamp" example:"1677662804141"`
}

// EncryptedCallbackResponse is encrypted callback response for Safeheron
type EncryptedCallbackResponse struct {
	Code       int    `json:"code"`
	Message    string `json:"message"`
	Timestamp  string `json:"timestamp,omitempty"`
	BizContent string `json:"bizContent,omitempty"`
	Key        string `json:"key,omitempty"`
	Signature  string `json:"sig,omitempty"`
}

// DecryptedBusinessTask is decrypted business task
type DecryptedBusinessTask struct {
	Type string `json:"type"`
	CustomerContent CustomerContent `json:"customerContent"`
}

// CustomerContent is content of the transaction
type CustomerContent struct {
	TxKey string `json:"txKey"`
}

// DecryptedNotification is decrypted notification
type DecryptedNotification struct {
	Type   string             `json:"eventType"`
	Detail NotificationDetail `json:"eventDetail"`
}

// NotificationDetail is a model with details about event
type NotificationDetail struct {
	TransactionID        string `json:"txKey"`
	AssetSymbol          string `json:"coinKey"`
	Amount               string `json:"txAmount"`
	SourceAccount        string `json:"sourceAccountKey"`
	SourceAddress        string `json:"sourceAddress"`
	DestinationAccount   string `json:"destinationAccountKey"`
	DestinationAddress   string `json:"destinationAddress"`
	TransactionStatus    string `json:"transactionStatus"`
	TransactionSubStatus string `json:"transactionSubStatus"`
}

// TransactionApprovalTaskResponse is decrypted bizContent field of safeheron callback request for TRANSACTION type
type TransactionApprovalTaskResponse struct {
	Approve bool `json:"approve"`
	TxKey string `json:"txKey"`
}
