package rates

type CoinMarketCapResponse struct {
	Data []struct {
		Symbol      string  `json:"symbol"`
		ID          int64   `json:"id"`
		Name        string  `json:"name"`
		Amount      float64 `json:"amount"`
		LastUpdated string  `json:"last_updated"`
		Quote       map[string]struct {
			Price       float64 `json:"price"`
			LastUpdated string  `json:"last_updated"`
		} `json:"quote"`
	} `json:"data"`
	Status struct {
		Timestamp    string `json:"timestamp"`
		ErrorCode    int    `json:"error_code"`
		ErrorMessage string `json:"error_message"`
		Elapsed      int    `json:"elapsed"`
		CreditCount  int    `json:"credit_count"`
	} `json:"status"`
}
