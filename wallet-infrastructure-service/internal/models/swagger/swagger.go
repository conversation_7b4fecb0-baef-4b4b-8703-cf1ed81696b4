package swagger

import (
	"time"
)

// WalletCreationRequest is a request that user sends when want to create wallet
type WalletCreationRequest struct {
	RequestID          string `json:"request_id" example:"6df3d4db-acb3-4d36-b03f-f37655869cba"`
	Type               string `json:"type" example:"safeheron"`
	ReferencingAddress string `json:"address" example:"mcf3d4db-acb3-4d36-b03f-f37655869cba"`
	WalletName         string `json:"wallet_name" example:"My Super Wallet"`
}

// WalletCreationStatusUpdate is an example of request that polity vault sends when want report about status
type WalletCreationStatusUpdate struct {
	RequestID string `json:"request_uuid" example:"6df3d4db-acb3-4d36-b03f-f37655869cba"`
	Status    string `json:"status" example:"provisioned"`
	Data      struct {
		PublicKey  string `json:"public_key" example:"MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAlkiGIW48hb4fwQIhpSgM7b++0ELepVW6+P6+N"`
		PrivateKey string `json:"private_key" example:"MIIJRAIBADANBgkqhkiG9w0BAQEFAASCCS4wggkqAgEAAoICAQDTX7ln3YOJnU+b 7AZK3iD"`
		ApiKey     string `json:"api_key" example:"fe00000000000000000000000000"`
	} `json:"data"`
}

// FavouriteAddressEntry is a model to save users favourite wallets in db
type FavouriteAddressEntry struct {
	Name        string `json:"name" example:"My friends' Bob wallet'"`
	AssetSymbol string `json:"asset_symbol" example:"BTC"`
	Address     string `json:"address" example:"******************************************"`
}

// FavouriteAddressEntryToDelete is a model to remove users favourite wallet from db
type FavouriteAddressEntryToDelete struct {
	AssetSymbol string `json:"asset_symbol" example:"BTC"`
	Address     string `json:"address" example:"******************************************"`
}

type Wallet struct {
	// RequestID - ID of the request
	RequestID string `json:"request_id" validate:"required,request_id"`
	// Corresponds to the uuid in the request to Vault
	UserID             string `json:"uuid" validate:"required,user_id" `
	Type               string `json:"type" validate:"required,type"`
	ReferencingAddress string `json:"address" validate:"required,address" `
	WalletName         string `json:"wallet_name" validate:"required,wallet_name"`
	// Status for interaction with end-user
	// Initially it should be 'Pending Creation'
	// Then 'Provisioned', set when signaled by the Vault
	// Finally 'Successfully Created' when a corresponding asset-account/wallet initiated
	// May be 'Failed' at any point
	Status      string `json:"status"`
	DfnsWallets []Asset
	// Nullable, set when create if type = Safeheron
	SafeheronWallet []SafeheronAsset
	// Nullable, signals that this is archived
	ArchivedAt time.Time `json:"archived_at"`
	// Set when all the steps of creation are complete
	InitializedAt time.Time `json:"initialized_at"`
}

type Asset struct {
	AccountID string `json:"id"`
	// OrganizationID represents 'orgId' in response for CreateAssetAccount func
	OrganizationID string `json:"orgId"`
	AssetSymbol    string `json:"assetSymbol"`
	Name           string `json:"name"`
	GroupSize      int    `json:"groupSize"`
	GroupThreshold int    `json:"groupThreshold"`
	// Nullable, signals that this is archived
	ArchivedAt time.Time `json:"archived_at"`

	WalletID uint
}

type SafeheronAsset struct {
	SafeheronWallet string    `json:"safeheron_wallet"`
	AssetSymbol     string    `json:"asset_symbol"`
	ArchivedAt      time.Time `json:"archived_at"`

	WalletID uint
}

// WalletConnectRequest is a model that represents request from frontend with WalletConnect request details
type WalletConnectRequest struct {
	RequestID string `json:"request_id" example:"6df3d4db-acb3-4d36-b03f-f37655869cba"`
	Method    string `json:"method" example:"eth_sendTransaction"`
	// NOTE: example of data object, it can depend on method
	Data struct {
		From     string `json:"from" example:"******************************************"`
		To       string `json:"to" example:"******************************************"`
		Value    string `json:"value" example:"0x31bce709442b"`
		Gas      string `json:"gas" example:"0x26465"`
		GasPrice string `json:"gasPrice" example:"0x4a817c800"`
		Data     string `json:"data" example:"0xd9627aa40000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000031bce709442b0000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee000000000000000000000000fdc4a3fc36df16a78edcaf1b837d3acaaedb2cb4869584cd00000000000000000000000010000000000000000000000000000000000000110000000000000000000000000000000000000000000000cc3caa3829638e2057"`
	} `json:"data"`
}

// Receiver - use the following fields in the nested receiver object to initiate a payment to any address supported by the target chain.
type Receiver struct {
	// Kind - specify: "BlockchainWalletAddress". Required
	Kind string `json:"kind"`
	// Address represents external blockchain address. Required
	Address string `json:"address"`
}

type Initiator struct {
	Kind       string `json:"kind"`
	OrgID      string `json:"orgId"`
	EmployeeId string `json:"employeeId"`
}

type GetPaymentByIDResp struct {
	Amount         string `json:"amount"`
	AssetAccountID string `json:"assetAccountId"`
	AssetSymbol    string `json:"assetSymbol"`
	DateCreated    string `json:"dateCreated"`
	ExternalID     string `json:"externalId"`
	// ID - unique identifier of the AssetAccount like: aa-orange-magnesium-a0606d08b2
	ID              string    `json:"id"`
	Initiator       Initiator `json:"initiator"`
	Narrative       string    `json:"narrative"`
	Note            string    `json:"note"`
	Direction       string    `json:"direction"`
	OrgID           string    `json:"orgId"`
	Receiver        Receiver  `json:"receiver"`
	ReceiverAddress string    `json:"receiverAddress"`
	Status          string    `json:"status"`
	TxHash          string    `json:"txHash"`
}

type GetListOfWalletsResponse struct {
	Wallets []WalletData `json:"wallets"`
}

type WalletData struct {
	RequestID  string    `json:"request_id"`
	WalletName string    `json:"wallet_name"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
}

// ApiResponse represents json-response body for http server, when we have errors on our Server.
type ApiResponse struct {
	Message string `json:"message,omitempty"`
}

type GetDeFiProductResponse struct {
	DefiProducts []DefiProduct `json:"defi_products"`
}

type DefiProduct struct {
	Name        string `json:"name"`
	Link        string `json:"link"`
	Description string `json:"description"`
}

type AssetBalance struct {
	Name        string  `json:"name"`
	DisplayName string  `json:"display_name"`
	AssetSymbol string  `json:"asset_symbol"`
	Address     string  `json:"address"`
	ChainID     string  `json:"chain_id"`
	Icon        string  `json:"icon"`
	Price       float64 `json:"price"`
	Balance     float64 `json:"balance"`
	BalanceUSD  float64 `json:"balance_usd"`
}

type TransactionsHistory struct {
	Data       []Transaction `json:"data"`
	Pagination struct {
		Next     string `json:"next" example:"http://**************:8083/api/v1/wallet/06a3a106-22e0-46ba-ab6f-86c4db387e1d/POL/transactions?offset=10&limit=5"`
		Previous string `json:"prev" example:"http://**************:8083/api/v1/wallet/06a3a106-22e0-46ba-ab6f-86c4db387e1d/POL/transactions?offset=5&limit=5"`
		Total    int64  `json:"total" example:"20"`
	} `json:"pagination"`
}

// Transaction is a model to represent historic summary of transaction
type Transaction struct {
	Date  time.Time `json:"date"`
	From  string    `json:"from" validate:"required,wallet_address" example:"******************************************"`
	To    string    `json:"to" validate:"required,wallet_address" example:"******************************************"`
	Hash  string    `json:"hash" validate:"required" example:"0x55c2e4ff28b2ca05b8c6b5728fed6e84b8013bf6f73cf0ff265b94d70a046ac3"`
	Value string    `json:"value" validate:"required,value" example:"0.00001"`
}

type TransactionResponse struct {
	Report            *ScanTransactionResponse `json:"report"`
	TransactionResult *TransactionResult       `json:"transaction_result,omitempty"`
}

type TransactionResult struct {
	TransactionID   string `json:"transaction_id,omitempty"`
	TransactionHash string `json:"transaction_hash,omitempty" example:"0x55c2e4ff28b2ca05b8c6b5728fed6e84b8013bf6f73cf0ff265b94d70a046ac3"`
	Status          string
}

type ScanTransactionResponse struct {
	Action   string `json:"action" example:"BLOCK"`
	Warnings []struct {
		Kind     string `json:"kind" example:"KNOWN_MALICIOUS"`
		Message  string `json:"message" example:"We believe this transaction is malicious and unsafe to sign. Approving may lead to loss of funds."`
		Severity string `json:"severity" example:"CRITICAL"`
	} `json:"warnings"`
}

type ProtocolBalancesResponse struct {
	ChainsProtocols []ChainProtocols `json:"chains_protocols"`

	TotalNetWorth float64 `json:"total_net_worth,omitempty"`
	TotalSupplied float64 `json:"total_supplied,omitempty"`
	TotalBorrowed float64 `json:"total_borrowed,omitempty"`
	TotalRewarded float64 `json:"total_rewarded,omitempty"`
}

type ChainProtocols struct {
	AssetSymbol      string             `json:"asset_symbol"`
	ProtocolBalances []ProtocolBalances `json:"protocol_balances"`

	TotalNetWorth float64 `json:"total_net_worth,omitempty"`
	TotalSupplied float64 `json:"total_supplied,omitempty"`
	TotalBorrowed float64 `json:"total_borrowed,omitempty"`
	TotalRewarded float64 `json:"total_rewarded,omitempty"`
}

type ProtocolBalances struct {
	Name string `json:"name,omitempty"`
	Slug string `json:"slug"`

	TotalNetWorth float64 `json:"total_net_worth,omitempty"`
	TotalSupplied float64 `json:"total_supplied"`
	TotalBorrowed float64 `json:"total_borrowed"`
	TotalRewarded float64 `json:"total_rewarded"`

	Positions []Position `json:"positions"`
}

type FeatureDetails struct {
	APR    float64 `json:"apr,omitempty"`
	Amount float64 `json:"amount"`
	Value  float64 `json:"value"`
	TVL    float64 `json:"tvl,omitempty"`
	Token  Token   `json:"token"`
}

type Position struct {
	TotalSupplied float64 `json:"total_supplied"`
	TotalBorrowed float64 `json:"total_borrowed"`
	TotalRewarded float64 `json:"total_rewarded"`

	Supplied []FeatureDetails `json:"supplied,omitempty"`
	Borrowed []FeatureDetails `json:"borrowed,omitempty"`
	Rewarded []FeatureDetails `json:"rewarded,omitempty"`
}

type Token struct {
	ID      int     `json:"id"`
	Address string  `json:"address"`
	Name    string  `json:"name"`
	Symbol  string  `json:"symbol"`
	Icon    string  `json:"icon"`
	Price   float64 `json:"price"`
}
