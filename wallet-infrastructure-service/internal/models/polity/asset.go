package polity

// AddAssetRequest is request to add new asset for user wallet
type AddAssetRequest struct {
	RequestID   string `json:"request_uuid" validate:"required,request_id" example:"6df3d4db-acb3-4d36-b03f-f37655869cba"`
	AssetSymbol string `json:"asset_symbol,omitempty" validate:"required,asset_symbol" example:"ETH"`
}

// GetAssetsRequest is request to get assets of user wallet
type GetAssetsRequest struct {
	RequestID string `json:"request_id" validate:"required,request_id"`
}

// GetAssetsResponse is response with list of assets for user wallet
type GetAssetsResponse struct {
	BalanceUSD string  `json:"balance_usd" example:"14.35"`
	Assets     []Asset `json:"assets"`
}

// Asset is struct that contains necessary details about assets
type Asset struct {
	AssetSymbol string `json:"asset_symbol" validate:"required,asset_symbol" example:"ETH"`
	Balance     string `json:"balance" example:"0.007241362958373209"`
	BalanceUSD  string `json:"balance_usd" example:"12.99"`
}

// AssetAddress is struct with asset address
type AssetAddress struct {
	Address string `json:"asset_address" validate:"required,wallet_address" example:"******************************************"`
}

type GetAssetAddressesResponse struct {
	StaticAddresses  []FavouriteAddress `json:"static_addresses,omitempty"`
	DynamicAddresses []FavouriteAddress `json:"dynamic_addresses"`
}

type FavouriteAddress struct {
	AssetSymbol string `json:"asset_symbol"`
	Address     string `json:"address"`
	Name        string `json:"name"`
}
