package pagination

type Parameters struct {
	Limit  int64
	Offset int64
}

type TransactionsHistoryMeta struct {
	Next     string `json:"next" example:"http://54.219.223.195:8083/api/v1/wallet/06a3a106-22e0-46ba-ab6f-86c4db387e1d/POL/transactions?offset=10&limit=5"`
	Previous string `json:"prev" example:"http://54.219.223.195:8083/api/v1/wallet/06a3a106-22e0-46ba-ab6f-86c4db387e1d/POL/transactions?offset=5&limit=5"`
	Total    int64  `json:"total" example:"20"`
	Token    string `json:"token" example:"WszQXoENUIYyoBQjJm4DE6QhCk2sB7WAh9kykUMaTQcD25SToKbuXkgf3td8ZYb2LrtopPLo35u407gwwA1Sug=="`
}
