package vault

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// DfnsWalletCredentialsResponse is a model to interact with Polity Vault
type DfnsWalletCredentialsResponse struct {
	ApiKey string `json:"api_key"`
}

// WalletCreationRequest is a model to interact with Polity Vault
type WalletCreationRequest struct {
	RequestID string `json:"request_uuid"`
	Type      string `json:"type"`
}

// WalletCreationStatusUpdate is a model to interact with Polity Vault
type WalletCreationStatusUpdate struct {
	RequestID string          `json:"request_uuid" validate:"required,request_id"`
	Status    Status          `json:"status" validate:"required"`
	Data      json.RawMessage `json:"data" validate:"required,data"`
}

type DataConstraints interface {
	SafeheronCredentials | DFNSCredentials
}

// SafeheronCredentials is a model that contains credentials to create safeheron coins
type SafeheronCredentials struct {
	ApiKey                         string `json:"api_key" validate:"required"`
	PrivateKey                     string `json:"private_key" validate:"required"`
	PublicKey                      string `json:"public_key" validate:"required"`
	PlatformPublicKey              string `json:"platform_public_key" validate:"required"`
	NotificationsPlatformPublicKey string `json:"notif_platform_pub_key" validate:"required"`
}

// DFNSCredentials is a model that contains credentials to create dfns asset accounts
type DFNSCredentials struct {
	JwtToken   string `json:"jwt_token" validate:"required"`
	PrivateKey string `json:"private_key" validate:"required"`
}

const (
	Pending     Status = "pending"
	Provisioned Status = "provisioned"
	InProgress  Status = "in_progress"
	Failed      Status = "failed"
)

type Status string

// Scan scan value into Jsonb, implements sql.Scanner interface
func (s *Status) Scan(value interface{}) error {
	*s = Status(value.(string))
	return nil
}

// Value return json value, implement driver.Valuer interface
func (s Status) Value() (driver.Value, error) {
	return string(s), nil
}

func (s Status) Validate() (bool, error) {
	walletStatuses := []Status{Pending, Provisioned, InProgress, Failed}
	for _, status := range walletStatuses {
		if s == status {
			return true, nil
		}
	}
	return false, fmt.Errorf("Unknown status for wallet: %v ", s)
}
