package migrations

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/Polity_MVP/wallet-infrastructure-service/config"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/dfns"
	"github.com/pkg/errors"
)

const (
	url = "https://api.dfns.io/wallets"
)

func GetDFNSWalletData(cfg *config.WalletInfrastructureConfig, walletId string) (*dfns.Wallet, error) {
	var body []byte
	var err error
	asset := new(dfns.Wallet)

	httpRequest, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s/%s", url, walletId), bytes.NewReader(body))
	if err != nil {
		return nil, errors.Errorf("can't create request: %v", err)
	}

	httpRequest.Header.Set("content-type", "application/json;charset=UTF-8")
	httpRequest.Header.Set("X-DFNS-APPID", cfg.DfnsApiKey)
	httpRequest.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cfg.DfnsToken))

	res, err := http.DefaultClient.Do(httpRequest)
	if err != nil {
		return nil, errors.Errorf("GetDFNSWalletData: can't send request: %v", err)
	}
	defer res.Body.Close()

	data, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, errors.Errorf("GetDFNSWalletData: can't read body: %v", err)
	}

	if res.StatusCode != http.StatusOK {
		return nil, errors.Errorf("GetDFNSWalletData: status code is not OK: %v, data: %v", res.StatusCode, string(data))
	}

	if err := json.Unmarshal(data, asset); err != nil {
		return nil, errors.Errorf("GetDFNSWalletData: can't unmarshal data: %v", err)
	}

	return asset, nil
}

func GetDFNSWalletAssets(cfg *config.WalletInfrastructureConfig, walletId string) ([]dfns.Asset, error) {
	var body []byte
	var err error
	response := new(dfns.GetAssetsResponse)

	httpRequest, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s/%s/assets", url, walletId), bytes.NewReader(body))
	if err != nil {
		return nil, errors.Errorf("GetDFNSWalletAssets: can't create request: %v", err)
	}

	httpRequest.Header.Set("content-type", "application/json;charset=UTF-8")
	httpRequest.Header.Set("X-DFNS-APPID", cfg.DfnsApiKey)
	httpRequest.Header.Add("Authorization", fmt.Sprintf("Bearer %s", cfg.DfnsToken))

	res, err := http.DefaultClient.Do(httpRequest)
	if err != nil {
		return nil, errors.Errorf("GetDFNSWalletAssets: can't send request: %v", err)
	}
	defer res.Body.Close()

	data, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, errors.Errorf("GetDFNSWalletAssets: can't read body: %v", err)
	}

	if res.StatusCode != http.StatusOK {
		return nil, errors.Errorf("GetDFNSWalletAssets: status code is not OK: %v, data: %v", res.StatusCode, string(data))
	}

	if err := json.Unmarshal(data, response); err != nil {
		return nil, errors.Errorf("GetDFNSWalletAssets: can't unmarshal data: %v", err)
	}

	return response.Assets, nil
}
