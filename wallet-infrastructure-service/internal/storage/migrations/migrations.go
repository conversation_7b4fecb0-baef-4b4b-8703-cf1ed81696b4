package migrations

import (
	"log"
	"time"

	"github.com/Polity_MVP/wallet-infrastructure-service/config"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/dfns"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/safeheron"
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

var (
	products = []defi.DefiProduct{
		{
			Model: gorm.Model{
				ID:        1,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:        "PancakeSwap",
			Link:        "https://pancakeswap.finance/",
			Icon:        "https://cryptologos.cc/logos/pancakeswap-cake-logo.png",
			Description: "DeFi platform to query Web3 information and create swaps",
		},
		{
			Model: gorm.Model{
				ID:        2,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:        "Zapper",
			Link:        "https://zapper.xyz/",
			Icon:        "https://studio.zapper.fi/img/logo.png",
			Description: "DeFi platform to trade, earn, and win crypto",
		},
		{
			Model: gorm.Model{
				ID:        3,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:        "dHedge",
			Link:        "https://www.dhedge.org/",
			Icon:        "https://storage.googleapis.com/job-listing-logos/8bf06c71-4fa3-4c7e-bf2e-cbc82620170b.png",
			Description: "DeFi Social Asset Management",
		},
		{
			Model: gorm.Model{
				ID:        4,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:        "Clearpool",
			Link:        "https://clearpool.finance/",
			Icon:        "https://ik.imagekit.io/l3jhuoqd2/Companies/project_logo_clearpool1687783300290.png",
			Description: "DeFi marketplace for unsecured institutional liquidity",
		},
		{
			Model: gorm.Model{
				ID:        5,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:        "ParaSwap",
			Link:        "https://app.paraswap.io",
			Icon:        "https://app.paraswap.io/static/media/psp_gradient.2b75d809.svg",
			Description: "The leading DeFi aggregator that unites the liquidity of decentralized exchanges and lending protocols",
		},
		{
			Model: gorm.Model{
				ID:        6,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			Name:        "Swapzone",
			Link:        "https://swapzone.io",
			Icon:        "https://swapzone.io/favicon-32x32.png",
			Description: "Crypto exchange aggregator",
		},
	}
)

func Migrate(cfg *config.WalletInfrastructureConfig, db *gorm.DB) error {
	m := gormigrate.New(db, gormigrate.DefaultOptions, []*gormigrate.Migration{
		// Here should be defined migration steps
		// ID - should be unique and static defined
		// DON'T change the sequence! Just add a new item to bottom
		// A new table you SHOULD define in InitSchema also!

		/*{
			ID: "step1",
			Migrate: func(tx *gorm.DB) error {
				return tx.AutoMigrate(&models.NewTable{})
			},
			Rollback: func(tx *gorm.DB) error {
				return tx.Migrator().DropTable("new_tables")
			},
		}, */
		{
			ID: "************",
			Migrate: func(tx *gorm.DB) error {
				type Asset struct {
					AccountType string `json:"account_type" gorm:"account_type"`
				}
				return tx.AutoMigrate(&safeheron.Asset{})
			},
			Rollback: func(tx *gorm.DB) error {
				return tx.Migrator().DropColumn("safeheron_assets", "account_type")
			},
		},
		{
			ID: "************",
			Migrate: func(tx *gorm.DB) error {
				return tx.Migrator().DropIndex("favourite_address_entries", "idx_user_asset_address")
			},
			Rollback: func(tx *gorm.DB) error {
				return tx.Migrator().CreateIndex("favourite_address_entries", "idx_user_asset_address")
			},
		},
		{
			ID: "************",
			Migrate: func(tx *gorm.DB) error {
				type DefiProduct struct {
					Icon string `json:"icon" gorm:"icon;size:200"`
				}
				return tx.AutoMigrate(&defi.DefiProduct{})
			},
			Rollback: func(tx *gorm.DB) error {
				return tx.Migrator().DropColumn("defi_products", "icon")
			},
		},
		{
			ID: "************",
			Migrate: func(tx *gorm.DB) error {
				txDelete := tx.Exec("DELETE FROM defi_products")
				if txDelete.Error != nil {
					return txDelete.Error
				}
				for _, product := range products {
					txInsert := tx.Exec(`
			  INSERT INTO defi_products (id, name, link, description, icon, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?);
		`, product.ID, product.Name, product.Link, product.Description, product.Icon, product.CreatedAt, product.UpdatedAt)
					if txInsert.Error != nil {
						log.Printf("Can`t add DeFi products. Error: '%v'\n", txInsert.Error)
						return txInsert.Error
					}
				}
				log.Printf("DeFi products added successfully. ")
				return nil
			},
		},
		{
			ID: "000000000006",
			Migrate: func(tx *gorm.DB) error {
				txDelete := tx.Exec("DELETE FROM defi_products")
				if txDelete.Error != nil {
					return txDelete.Error
				}
				for _, product := range products {
					txInsert := tx.Exec(`
			  INSERT INTO defi_products (id, name, link, description, icon, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?);
		`, product.ID, product.Name, product.Link, product.Description, product.Icon, product.CreatedAt, product.UpdatedAt)
					if txInsert.Error != nil {
						log.Printf("Can`t add DeFi products. Error: '%v'\n", txInsert.Error)
						return txInsert.Error
					}
				}
				log.Printf("DeFi products added successfully. ")
				return nil
			},
		},
		{
			ID: "000000000007",
			Migrate: func(tx *gorm.DB) error {
				txDelete := tx.Exec("DELETE FROM defi_products")
				if txDelete.Error != nil {
					return txDelete.Error
				}
				for _, product := range products {
					txInsert := tx.Exec(`
			  INSERT INTO defi_products (id, name, link, description, icon, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?);
		`, product.ID, product.Name, product.Link, product.Description, product.Icon, product.CreatedAt, product.UpdatedAt)
					if txInsert.Error != nil {
						log.Printf("Can`t add DeFi products. Error: '%v'\n", txInsert.Error)
						return txInsert.Error
					}
				}
				log.Printf("DeFi products added successfully. ")
				return nil
			},
		},
		{
			ID: "000000000008",
			Migrate: func(tx *gorm.DB) error {
				txDelete := tx.Exec("DELETE FROM defi_products")
				if txDelete.Error != nil {
					return txDelete.Error
				}
				for _, product := range products {
					txInsert := tx.Exec(`
			  INSERT INTO defi_products (id, name, link, description, icon, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?);
		`, product.ID, product.Name, product.Link, product.Description, product.Icon, product.CreatedAt, product.UpdatedAt)
					if txInsert.Error != nil {
						log.Printf("Can`t add DeFi products. Error: '%v'\n", txInsert.Error)
						return txInsert.Error
					}
				}
				log.Printf("DeFi products added successfully. ")
				return nil
			},
		},
		{
			ID: "************",
			Migrate: func(tx *gorm.DB) error {
				tx.AutoMigrate(&dfns.Wallet{})
				tx.AutoMigrate(&dfns.Asset{})
				tx.AutoMigrate(&dfns.DfnsWalletSigningKey{})

				// Find old records
				var oldRecords []map[string]interface{}
				tx.Table("dfns_assets").Find(&oldRecords)
				for _, oldRecord := range oldRecords {
					// Get data from dfns
					newAccount, err := GetDFNSWalletData(cfg, oldRecord["public_key"].(string))
					if err != nil {
						return err
					}
					assets, err := GetDFNSWalletAssets(cfg, oldRecord["public_key"].(string))
					if err != nil {
						return err
					}
					switch oldRecord["wallet_id"].(type) {
					case int32:
						newAccount.PolityWalletID = uint(oldRecord["wallet_id"].(int32))
					case int64:
						newAccount.PolityWalletID = uint(oldRecord["wallet_id"].(int64))
					}
					newAccount.Assets = assets
					// Create new record with new fields
					if res := tx.Table("dfns_wallets").Create(newAccount); res.Error != nil {
						return res.Error
					}
				}
				// Delete old table
				if txDelete := tx.Exec("DROP TABLE dfns_assets;"); txDelete.Error != nil {
					return txDelete.Error
				}
				return nil
			},
		},
		{
			ID: "************",
			Migrate: func(tx *gorm.DB) error {
				tx.AutoMigrate(&dfns.DfnsWalletSigningKey{})
				return nil
			},
		},
	})
	// Init schema on clean DB
	m.InitSchema(func(tx *gorm.DB) error {
		err := tx.AutoMigrate(
			&polity.Wallet{},
			&dfns.Asset{},
			&safeheron.Asset{},
			&polity.FavouriteAddressEntry{},
			&defi.DefiProduct{},
		)
		if err != nil {
			log.Printf("Automigrate failed.. Error: '%v'\n", err)
			return err
		}
		log.Printf("Migration schema successfully finished. ")

		for _, product := range products {
			txInsert := tx.Exec(`
			  INSERT INTO defi_products (id, name, link, description, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?);
		`, product.ID, product.Name, product.Link, product.Description, product.CreatedAt, product.UpdatedAt)
			if txInsert.Error != nil {
				log.Printf("Can`t add DeFi products. Error: '%v'\n", txInsert.Error)
				return txInsert.Error
			}
		}
		log.Printf("DeFi products added successfully. ")

		return nil
	})

	if err := m.Migrate(); err != nil {
		log.Fatalf("Could not migrate: %v", err)
	}
	log.Printf("Migration did run successfully")
	return nil
}
