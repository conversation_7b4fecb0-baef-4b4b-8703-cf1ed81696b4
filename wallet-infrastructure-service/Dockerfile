ARG GOLANG_VERSION=1.19

#===========================================================
# Base
#===========================================================
FROM golang:${GOLANG_VERSION}-alpine as base

#===========================================================
# Builder
#===========================================================
FROM base as builder
WORKDIR /app
COPY . ./
ARG BUILD_VERSION
ARG APP_VERSION

RUN apk --no-cache add --update bash
RUN apk --no-cache add --update alpine-sdk
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-X main.build=${BUILD_VERSION} -X main.version=${APP_VERSION}" -v -o wallet-infrastructure-service cmd/main.go

#===========================================================
# Release
#===========================================================
FROM alpine as release
WORKDIR /
RUN apk --no-cache add --update curl
COPY --from=builder /app/wallet-infrastructure-service /wallet-infrastructure-service
COPY ./flag-config.yaml /flag-config.goff.example.yaml

CMD ["./wallet-infrastructure-service"]