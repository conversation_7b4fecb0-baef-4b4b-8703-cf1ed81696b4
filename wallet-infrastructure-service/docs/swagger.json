{"swagger": "2.0", "info": {"description": "This is service for storing wallets and providing interaction between MPC wallet providers and Polity customers.", "title": "Bastion Wallet Infrastructure", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "basePath": "/api/v1", "paths": {"/assets/{asset_symbol}/invoke": {"post": {"security": [{"BearerAuth": []}], "description": "Invoke appropriate APIs of wallet managers (safeheron/dfns) for Wallet Connect call", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["wallet connect"], "summary": "Invoke appropriate APIs", "parameters": [{"type": "string", "description": "Asset Symbol of requested operation", "name": "asset_symbol", "in": "path", "required": true}, {"description": "Request details input", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.WalletConnectRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.TransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/assets/{asset_symbol}/invoke/scan/": {"post": {"security": [{"BearerAuth": []}], "description": "Scan invoking appropriate APIs of wallet managers (safeheron/dfns) for Wallet Connect call", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["wallet connect"], "summary": "Scan invoking appropriate APIs", "parameters": [{"type": "string", "description": "Asset Symbol of requested operation", "name": "asset_symbol", "in": "path", "required": true}, {"description": "Request details input", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.WalletConnectRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.ScanTransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/defi/products/": {"get": {"security": [{"BearerAuth": []}], "description": "Display list of the whitelisted products for DeFi.", "produces": ["application/json"], "tags": ["defi"], "summary": "Get list of DeFi products, stored on the backend", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.GetDeFiProductResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.GetDeFiProductResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.GetDeFiProductResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.GetDeFiProductResponse"}}}}}, "/defi/{request_uuid}/assets/": {"get": {"security": [{"BearerAuth": []}], "description": "Get assets balance for users wallet", "produces": ["application/json"], "tags": ["defi"], "summary": "Get assets balance", "parameters": [{"type": "string", "description": "Wallet Request ID of notification to process", "name": "request_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/swagger.AssetBalance"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/defi/{request_uuid}/deposits/": {"get": {"security": [{"BearerAuth": []}], "description": "Get deposits balances for users wallet", "produces": ["application/json"], "tags": ["defi"], "summary": "Get deposits balances", "parameters": [{"type": "string", "description": "Wallet Request ID", "name": "request_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.ProtocolBalancesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/defi/{request_uuid}/lending/": {"get": {"security": [{"BearerAuth": []}], "description": "Get lending balances for users wallet", "produces": ["application/json"], "tags": ["defi"], "summary": "Get lending balances", "parameters": [{"type": "string", "description": "Wallet Request ID", "name": "request_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.ProtocolBalancesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/favourites/address": {"get": {"security": [{"BearerAuth": []}], "description": "GetFavouriteAddresses returns static addresses and dynamic addresses for current user.\nIf we have role==Avatar -> return static and dynamic addresses. If jwt role!=Avatar -> return only dynamic addresses.", "produces": ["application/json"], "tags": ["favourites"], "summary": "Get addresses from list of favourites", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/polity.GetAssetAddressesResponse"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Add wallet address to the users' favourites list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["favourites"], "summary": "Add address to list of favourites", "parameters": [{"description": "Request with details about address to add", "name": "address", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.FavouriteAddressEntry"}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Remove the wallet address from the preconfigured favourite wallets list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["favourites"], "summary": "Remove address from list of favourites", "parameters": [{"description": "Request with details about address to delete", "name": "address", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.FavouriteAddressEntryToDelete"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/payment/dfns": {"post": {"security": [{"BearerAuth": []}], "description": "Initiate transaction from DFNS wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Initiate transaction", "parameters": [{"description": "Transaction details input", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.TransactionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/polity.TransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/payment/dfns/notification": {"post": {"description": "Receives event notification from DFNS and sends notification to User Infrastructure that funds have been received", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Notify when user receives funds", "parameters": [{"description": "Encrypted notification", "name": "notification", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.GetPaymentByIDResp"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/safeheron.EncryptedCallbackResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/payment/dfns/scan/": {"post": {"security": [{"BearerAuth": []}], "description": "Scan transaction from DFNS wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Scan transaction", "parameters": [{"description": "Transaction details input", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.TransactionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.ScanTransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/payment/last/": {"post": {"description": "Display dates of the last transactions across users wallets", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Get list of last users transactions dates", "parameters": [{"description": "List of users", "name": "users", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.LastTransactionsRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/polity.LastTransactionsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/payment/safeheron": {"post": {"security": [{"BearerAuth": []}], "description": "Initiate transaction from Safeheron wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Initiate transaction", "parameters": [{"description": "Transaction details input", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.TransactionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.TransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/payment/safeheron/notification/{request_uuid}": {"post": {"description": "Receives event notification from Safeheron and sends notification to User Infrastructure that funds have been received", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Notify when user receives funds", "parameters": [{"type": "string", "description": "Wallet Request ID of notification to process", "name": "request_uuid", "in": "path", "required": true}, {"description": "Encrypted notification", "name": "notification", "in": "body", "required": true, "schema": {"$ref": "#/definitions/safeheron.EncryptedCallbackRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/safeheron.EncryptedCallbackResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/payment/safeheron/resolve/{request_uuid}": {"post": {"security": [{"BearerAuth": []}], "description": "Resolve safeheron task to approve transaction", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Resolve approval task", "parameters": [{"type": "string", "description": "Wallet Request ID of task to resolve", "name": "request_uuid", "in": "path", "required": true}, {"description": "Encrypted details about task to be resolved", "name": "task", "in": "body", "required": true, "schema": {"$ref": "#/definitions/safeheron.EncryptedCallbackRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/safeheron.EncryptedCallbackResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/payment/safeheron/scan/": {"post": {"security": [{"BearerAuth": []}], "description": "Scan transaction from Safeheron wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Scan transaction", "parameters": [{"description": "Transaction details input", "name": "transaction", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.TransactionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.ScanTransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet": {"post": {"security": [{"BearerAuth": []}], "description": "Save wallet request details in db and passes request to Polity Vault", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["wallets"], "summary": "Create polity wallet", "parameters": [{"description": "Request with details about Wallet to be created", "name": "wallet", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.WalletCreationRequest"}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Set the wallet as archived", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["wallets"], "summary": "Archives polity wallet", "parameters": [{"description": "Request with id of polity wallet to be archived", "name": "wallet", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.RequestID"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}, "patch": {"security": [{"BearerAuth": []}], "description": "Set the another wallet_name for wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["wallets"], "summary": "Rename wallet", "parameters": [{"description": "Request with wallet id and new name", "name": "wallet", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.RenameWalletRequest"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/dfns/asset": {"post": {"security": [{"BearerAuth": []}], "description": "Create new asset for users wallet on dfns", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["assets"], "summary": "Create Asset", "parameters": [{"description": "Request with details to create asset", "name": "asset", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.AddAssetRequest"}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Archives asset for users dfns wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["assets"], "summary": "Archives Asset", "parameters": [{"description": "Request with details to archive asset", "name": "asset", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.AddAssetRequest"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/dfns/list/active/assets": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of active DFNS assets accounts for specified wallet in our system", "produces": ["application/json"], "tags": ["assets"], "summary": "Get list of active DFNS assets for user", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/dfns/{request_uuid}/assets": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of asset accounts on dfns for specified wallet", "produces": ["application/json"], "tags": ["assets"], "summary": "Get assets list", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/polity.GetAssetsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/dfns/{request_uuid}/{asset_symbol}/address": {"get": {"security": [{"BearerAuth": []}, {"BearerAuth": []}], "description": "Get addresses of asset, which specified on dfns wallet. Method returns all assets for each asset in wallet", "produces": ["application/json"], "tags": ["assets"], "summary": "Get addresses of DFNS asset account", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}, {"type": "string", "description": "Asset for which address user is looking for", "name": "asset_symbol", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/polity.AssetAddress"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/safeheron/asset": {"post": {"security": [{"BearerAuth": []}], "description": "Set new asset for users wallet on safeheron", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["assets"], "summary": "Create Asset", "parameters": [{"description": "Request with details to create asset", "name": "asset", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.AddAssetRequest"}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Archives asset for users safeheron wallet", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["assets"], "summary": "Archives Asset", "parameters": [{"description": "Request with details to archive asset", "name": "asset", "in": "body", "required": true, "schema": {"$ref": "#/definitions/polity.AddAssetRequest"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/safeheron/list/active/assets": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of active Safeheron assets accounts for specified wallet in our system", "produces": ["application/json"], "tags": ["assets"], "summary": "Get list of active Safeheron assets for user", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/safeheron/{request_uuid}/assets": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of assets on specified safeheron wallet", "produces": ["application/json"], "tags": ["assets"], "summary": "Get assets list", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/polity.GetAssetsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/safeheron/{request_uuid}/{asset_symbol}/address": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of assets on specified safeheron wallet. Method returns all assets for each asset in wallet.", "produces": ["application/json"], "tags": ["assets"], "summary": "Get assets list", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}, {"type": "string", "description": "Asset for which address user is looking for", "name": "asset_symbol", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/polity.AssetAddress"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/webhook": {"post": {"description": "Process polity wallet status change and create appropriate wallets with default assets on dfns/safeheron. Request field data depends on wallet type (dfns/safeheron)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["wallets"], "summary": "Finish wallet creation", "parameters": [{"description": "Request status for specified request and appropriate credentials", "name": "status", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.WalletCreationStatusUpdate"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/wallet/{request_uuid}": {"get": {"security": [{"BearerAuth": []}], "description": "Get wallet for current user. You can’t access the endpoint if you are not the owner of the wallet (if user id from JWT is not equal to user id of the wallet owner)", "produces": ["application/json"], "tags": ["assets"], "summary": "Get polity wallet", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}, {"description": "Wallet detailed for current user details", "name": "wallet", "in": "body", "required": true, "schema": {"$ref": "#/definitions/swagger.Wallet"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.Wallet"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallet/{request_uuid}/{asset_symbol}/transactions": {"get": {"security": [{"BearerAuth": []}, {"BearerAuth": []}], "description": "Get the full history of transaction for a particular wallet and its particular asset. Specify optional parameters like /transactions?offset=5&limit=5", "produces": ["application/json"], "tags": ["assets"], "summary": "Get history of transactions for provided wallet asset", "parameters": [{"type": "string", "description": "Request ID of wallet", "name": "request_uuid", "in": "path", "required": true}, {"type": "string", "description": "Asset for which user is looking for history of transactions", "name": "asset_symbol", "in": "path", "required": true}, {"type": "string", "description": "Optional transactions limit, default = 10", "name": "limit", "in": "path"}, {"type": "string", "description": "Optional transactions offset, default = 0", "name": "offset", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/swagger.TransactionsHistory"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallets/dfns/": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of active DFNS wallets for user. Specify optional parameter like /dfns?balances=true", "produces": ["application/json"], "tags": ["wallets"], "summary": "Get list of active DFNS wallets.", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/polity.WalletData"}}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}, "/wallets/safeheron/": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of active Safeheron wallets for user. Specify optional parameter like /safeheron?balances=true", "produces": ["application/json"], "tags": ["wallets"], "summary": "Get list of active Safeheron wallets.", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/polity.WalletData"}}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/swagger.ApiResponse"}}}}}}, "definitions": {"polity.AddAssetRequest": {"type": "object", "required": ["asset_symbol", "request_uuid"], "properties": {"asset_symbol": {"type": "string", "example": "ETH"}, "request_uuid": {"type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}}}, "polity.Asset": {"type": "object", "required": ["asset_symbol"], "properties": {"asset_symbol": {"type": "string", "example": "ETH"}, "balance": {"type": "string", "example": "0.007241362958373209"}, "balance_usd": {"type": "string", "example": "12.99"}}}, "polity.AssetAddress": {"type": "object", "required": ["asset_address"], "properties": {"asset_address": {"type": "string", "example": "******************************************"}}}, "polity.FavouriteAddress": {"type": "object", "properties": {"address": {"type": "string"}, "asset_symbol": {"type": "string"}, "name": {"type": "string"}}}, "polity.GetAssetAddressesResponse": {"type": "object", "properties": {"dynamic_addresses": {"type": "array", "items": {"$ref": "#/definitions/polity.FavouriteAddress"}}, "static_addresses": {"type": "array", "items": {"$ref": "#/definitions/polity.FavouriteAddress"}}}}, "polity.GetAssetsResponse": {"type": "object", "properties": {"assets": {"type": "array", "items": {"$ref": "#/definitions/polity.Asset"}}, "balance_usd": {"type": "string", "example": "14.35"}}}, "polity.LastTransaction": {"type": "object", "properties": {"date": {"type": "string"}, "request_id": {"type": "string"}, "uuid": {"type": "string"}}}, "polity.LastTransactionsRequest": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/definitions/polity.User"}}, "wallet_type": {"type": "string"}}}, "polity.LastTransactionsResponse": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/definitions/polity.LastTransaction"}}}}, "polity.RenameWalletRequest": {"type": "object", "required": ["request_id", "wallet_name"], "properties": {"request_id": {"type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}, "wallet_name": {"type": "string", "example": "Super Wallet"}}}, "polity.RequestID": {"type": "object", "required": ["request_id"], "properties": {"request_id": {"type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}}}, "polity.TransactionRequest": {"type": "object", "required": ["asset", "from", "to", "value"], "properties": {"asset": {"description": "Asset type (ETH, BTC etc)", "type": "string", "example": "ETH"}, "chainID": {"description": "ChainID is the chain id where transaction is to gonna be executed", "type": "string"}, "data": {"description": "Data: DATA - The compiled code of a contract OR the hash of the invoked walletconnect signature and encoded parameters.\nFor details see Ethereum Contract ABI: https://github.com/ethereum/wiki/wiki/Ethereum-Contract-ABI", "type": "string", "example": "0xd9627aa40000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000031bce709442b0000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee000000000000000000000000fdc4a3fc36df16a78edcaf1b837d3acaaedb2cb4869584cd00000000000000000000000010000000000000000000000000000000000000110000000000000000000000000000000000000000000000cc3caa3829638e2057"}, "from": {"description": "Customers wallet name from which he wants to send assets", "type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}, "gas_limit": {"description": "NOTE: the fields bellow are optional, if not set - default services values will be used\nGasLimit of transaction", "type": "string"}, "gas_price": {"description": "GasPrice of transaction", "type": "string"}, "max_fee": {"description": "MaxFee of transaction", "type": "string"}, "max_priority_fee": {"description": "MaxPriorityFee of transaction", "type": "string"}, "narrative": {"description": "Narrative - broader context on the  DFNS payment for customer use. Optional", "type": "string"}, "nonce": {"description": "Nonce -nonce is the total number of transactions which has been sent from this wallet so far", "type": "string"}, "note": {"description": "Note - a short payment description for DFNS payments. Optional", "type": "string"}, "to": {"description": "Some transaction destination address", "type": "string", "example": "******************************************"}, "value": {"description": "Quantity of assets to be transferred", "type": "string", "example": "0.00001"}}}, "polity.TransactionResponse": {"type": "object", "properties": {"status": {"type": "string"}, "transaction_hash": {"type": "string", "example": "0x55c2e4ff28b2ca05b8c6b5728fed6e84b8013bf6f73cf0ff265b94d70a046ac3"}, "transaction_id": {"type": "string"}}}, "polity.User": {"type": "object", "required": ["uuid"], "properties": {"uuid": {"type": "string"}}}, "polity.WalletData": {"type": "object", "properties": {"balance_usd": {"type": "string"}, "created_at": {"type": "string"}, "request_id": {"type": "string"}, "status": {"type": "string"}, "wallet_name": {"type": "string"}}}, "safeheron.EncryptedCallbackRequest": {"type": "object", "properties": {"bizContent": {"type": "string", "example": "4m/ra7f3gyJYmqHL8rTpMAR+dakd/rgnTkXNNAKc6zb3fomWaxR8KS/szHnwqU0XMk01wAqnKs3UMSsaXvJES3LkJFuoSP7r1U7bepDih24ESpuxQ3M+JvL3yw6U51yXa2prAFg+8T8uKhUfyxflXod6M5ROj/MMsd0aXxXI3ZbTbiET85kPxk0OUmY3yc7LgVb7fyncxfLCHGTErkTH14rXevomOZdyFp7/mkt2hW4p/J75oJ4eMsvBGE7Z3T7WbYE/Y+K0y28VNfvxcqpENt5kbHFqWUycVtQKgUShJEZkk/9ddTzrU+nqzz4fddTwCdpG9QgiYSV30rA7+PxMJwnWRtbhOtYFnc1X61BA+blC6qm8Du9kTbruG17Rrxf+Ej0Adwv90m/Oe++Qo0cByoLvBCUINcKKKOVDTy6tPX4MKjdQimgluW8Gn7UF28vV/6WyV+5z/dOsP6H2Uym/rVbSHGR+vjVYy8TxxJwr8tL6QrIEw7RnVAtGsS+RMA5I8Un4bam7CdVPBybQ+TPCKlYK39x2ReSEIpi9ZpJxxxS6zmFo70jfk0/1s7safkYRI3NecuvaTT7qJjIOvDwzUhfNsh07+P2SC/sutIgQj4qkxftwpO31VnHfwFN2yH0/279y37cJpzQbs7trSE9BQAnQZL1EN6VmmNBgz74u5JnIrxrLgUk4YErQudWleEFJv4j0tSNT4EZgbvi6GYfpETlPSx3MkhfD+P7TMn+K+ho4NrUH5wClePxc3k2hxB2BvG7GIEoRclN+vhfHxUOPEDPI/iCzmWTeyz7Exfa2sY4Zq52j23pW7qrPjhfi18uJYc1vrlvkAiSI0v3eDlKCLXQ25hpoSoPHXuWJDRY53PILmgQxMnFGviaLjGpjuvsVSZlcf1+tfq8BdtAiy/p3j3LX5E6/uE/Toczm4lNHlURw8DijVF8HCrVXcDXOjHJK2R8GSXvflQbupU8GR+7zDIODM/WfvF9ARGk7+iZe5FtNHTz4tEkxURFwa3iatvhjxdItXbHLCrRTKCXnidgNkuL606Mc4jb7WypuDyDl9MkdcBVRvX5alAZJIGSTdZdp6wo/+GsYh31jWA1ozLaDyuB3GhCJ/YFmgFriRxVVxje1mt84SSyh9eeRHZSQONn2qIwgW12ZolFzDd4JWc3+NFKu4cZk75QfnNhex0/wWVkC3kCMizzoPKuQSIBj6rUanshoEyQf76HE2h01qTxlrSa4XS0NdpEcZY7g7ftigx35u4geiv4yYDwyk82gdpGyDb+F51wWrlGIlDQWNT7tPw=="}, "key": {"type": "string", "example": "UytGIJVrDzcgst/ihNrptcZwt8thcEO126yg3lWSUU2MSyrId9R0UnpI/bm2QebNKvO0tATZ+4gSW65KuHAiSlNjsoluswd47F5KDZZHqPXSXuTuuPPo7sYMhAPy3JfGkWVcH/nOfANFNWYptHyHljpvYWqzPVNmqJ0i6z9fMpzkGhtfl5wzR+TEpnIpS0uevYwcnxN6ay3CLCtKgZBfgilnoTaq0bBU8RqvyX4lXoYIXiYOqJVnuA1G0gCOt57UYhYcFylmRnagjvpQ8w30Y8G9mGsU4hmC6cu1RtH5iKlD0tJU3Xnk28SUOf9OABOKIau5C2JeVkRshpOQfTmCAYTJuGCIN+XdzYlFnjc7icxNf6UgTSfw3Kg3V/QOQA2OU8QRNFi5VWvF6i12bEUmMQcVBKe3HDpVev9GMzUErtaBqFkFlxJMQJAOV4aXnuBKXgRbMzix3/kTS5t7ynxhJCVp2XErYb5XmrAHFQ49Cm8Nor3dElb/Yvl50jd1PNTsyu56zfv7qHt+TUM5xI7zoSAckBNNP7o8TjZJtFqUrVvM0QEm8jOo6tsNuKgaiI0VPxTf6ER/sl+j5kzSH07TAWheIDeqMU/Trtz1ZPTSyOW4JVZuQQX4xfrZtMZyLagnUVYvOHzbyj8xmZTtd5MXE992dHCXIjn/A8+jXURJVZg="}, "sig": {"type": "string", "example": "dA4HBamyVlBtwHKTV17ULv3pcl6ks0r6qnYn8scNnyAn8LALtdBJfpCLSXnn4NgFuVwe9hDxDP6DS5PzZcEeCsx1oGPqLQnS/GKNfxDZTB5BsXN6C+Q/qM9ffcCfSwFna2ZEqGQ6yH8s9wqM6ld/nYDjx1dVhBfB8PXOK22M7SVIaLg0wrqwI4EWIObJxrJGhK34w+v1to4DFiTcqVNhgcn+GmG5HRTBPSNculaDZ1hih4Pd+15cUi7e1gjjfOwDpt6dDSZ4I7FT24yfFzYjNbMpziaFqQySqqWL6cI0tgUQrlSnMv6NIRc1EVDtwLamcLPIqffvZqp0GyRZtCzNykjU/aNrqRnLj7oemOASWUq9NGuFHkILdvsQapAx8YH8S6sXr4GNPYoo/H06M6MorxugZbbuInQ9IjE19t4o7U9qossqVYTYKchb2NGnERDtY1JVpRSOVAd817g7AoLQro1Hx25HKARIofWDtOeTHhMqb+txidzZnQveIw4h32aY3/DtZE0Qt83pqOUJx1/grXDjhLXnYNGdXWfAWjG/cbf3qjawcefWh6t73ZNlcpS3guYDUURMTSwEnT41JClXMI2UpUzLisF0w5sYwwGjC6xu89wD70vMPPF3QN5+XGDREmRRIo4SGaY174n+x+1WEG9seyWCOvSpZ76Yi6u6fd4="}, "timestamp": {"type": "string", "example": "1677662804141"}}}, "safeheron.EncryptedCallbackResponse": {"type": "object", "properties": {"bizContent": {"type": "string"}, "code": {"type": "integer"}, "key": {"type": "string"}, "message": {"type": "string"}, "sig": {"type": "string"}, "timestamp": {"type": "string"}}}, "swagger.ApiResponse": {"type": "object", "properties": {"message": {"type": "string"}}}, "swagger.Asset": {"type": "object", "properties": {"archived_at": {"description": "Nullable, signals that this is archived", "type": "string"}, "assetSymbol": {"type": "string"}, "groupSize": {"type": "integer"}, "groupThreshold": {"type": "integer"}, "id": {"type": "string"}, "name": {"type": "string"}, "orgId": {"description": "OrganizationID represents 'orgId' in response for CreateAssetAccount func", "type": "string"}, "walletID": {"type": "integer"}}}, "swagger.AssetBalance": {"type": "object", "properties": {"address": {"type": "string"}, "asset_symbol": {"type": "string"}, "balance": {"type": "number"}, "balance_usd": {"type": "number"}, "chain_id": {"type": "string"}, "display_name": {"type": "string"}, "icon": {"type": "string"}, "name": {"type": "string"}, "price": {"type": "number"}}}, "swagger.ChainProtocols": {"type": "object", "properties": {"asset_symbol": {"type": "string"}, "protocol_balances": {"type": "array", "items": {"$ref": "#/definitions/swagger.ProtocolBalances"}}, "total_borrowed": {"type": "number"}, "total_net_worth": {"type": "number"}, "total_rewarded": {"type": "number"}, "total_supplied": {"type": "number"}}}, "swagger.DefiProduct": {"type": "object", "properties": {"description": {"type": "string"}, "link": {"type": "string"}, "name": {"type": "string"}}}, "swagger.FavouriteAddressEntry": {"type": "object", "properties": {"address": {"type": "string", "example": "******************************************"}, "asset_symbol": {"type": "string", "example": "BTC"}, "name": {"type": "string", "example": "My friends' Bob wallet'"}}}, "swagger.FavouriteAddressEntryToDelete": {"type": "object", "properties": {"address": {"type": "string", "example": "******************************************"}, "asset_symbol": {"type": "string", "example": "BTC"}}}, "swagger.FeatureDetails": {"type": "object", "properties": {"amount": {"type": "number"}, "apr": {"type": "number"}, "token": {"$ref": "#/definitions/swagger.Token"}, "tvl": {"type": "number"}, "value": {"type": "number"}}}, "swagger.GetDeFiProductResponse": {"type": "object", "properties": {"defi_products": {"type": "array", "items": {"$ref": "#/definitions/swagger.DefiProduct"}}}}, "swagger.GetPaymentByIDResp": {"type": "object", "properties": {"amount": {"type": "string"}, "assetAccountId": {"type": "string"}, "assetSymbol": {"type": "string"}, "dateCreated": {"type": "string"}, "direction": {"type": "string"}, "externalId": {"type": "string"}, "id": {"description": "ID - unique identifier of the AssetAccount like: aa-orange-magnesium-a0606d08b2", "type": "string"}, "initiator": {"$ref": "#/definitions/swagger.Initiator"}, "narrative": {"type": "string"}, "note": {"type": "string"}, "orgId": {"type": "string"}, "receiver": {"$ref": "#/definitions/swagger.Receiver"}, "receiverAddress": {"type": "string"}, "status": {"type": "string"}, "txHash": {"type": "string"}}}, "swagger.Initiator": {"type": "object", "properties": {"employeeId": {"type": "string"}, "kind": {"type": "string"}, "orgId": {"type": "string"}}}, "swagger.Position": {"type": "object", "properties": {"borrowed": {"type": "array", "items": {"$ref": "#/definitions/swagger.FeatureDetails"}}, "rewarded": {"type": "array", "items": {"$ref": "#/definitions/swagger.FeatureDetails"}}, "supplied": {"type": "array", "items": {"$ref": "#/definitions/swagger.FeatureDetails"}}, "total_borrowed": {"type": "number"}, "total_rewarded": {"type": "number"}, "total_supplied": {"type": "number"}}}, "swagger.ProtocolBalances": {"type": "object", "properties": {"name": {"type": "string"}, "positions": {"type": "array", "items": {"$ref": "#/definitions/swagger.Position"}}, "slug": {"type": "string"}, "total_borrowed": {"type": "number"}, "total_net_worth": {"type": "number"}, "total_rewarded": {"type": "number"}, "total_supplied": {"type": "number"}}}, "swagger.ProtocolBalancesResponse": {"type": "object", "properties": {"chains_protocols": {"type": "array", "items": {"$ref": "#/definitions/swagger.ChainProtocols"}}, "total_borrowed": {"type": "number"}, "total_net_worth": {"type": "number"}, "total_rewarded": {"type": "number"}, "total_supplied": {"type": "number"}}}, "swagger.Receiver": {"type": "object", "properties": {"address": {"description": "Address represents external blockchain address. Required", "type": "string"}, "kind": {"description": "Kind - specify: \"BlockchainWalletAddress\". Required", "type": "string"}}}, "swagger.SafeheronAsset": {"type": "object", "properties": {"archived_at": {"type": "string"}, "asset_symbol": {"type": "string"}, "safeheron_wallet": {"type": "string"}, "walletID": {"type": "integer"}}}, "swagger.ScanTransactionResponse": {"type": "object", "properties": {"action": {"type": "string", "example": "BLOCK"}, "warnings": {"type": "array", "items": {"type": "object", "properties": {"kind": {"type": "string", "example": "KNOWN_MALICIOUS"}, "message": {"type": "string", "example": "We believe this transaction is malicious and unsafe to sign. Approving may lead to loss of funds."}, "severity": {"type": "string", "example": "CRITICAL"}}}}}}, "swagger.Token": {"type": "object", "properties": {"address": {"type": "string"}, "icon": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "price": {"type": "number"}, "symbol": {"type": "string"}}}, "swagger.Transaction": {"type": "object", "required": ["from", "hash", "to", "value"], "properties": {"date": {"type": "string"}, "from": {"type": "string", "example": "******************************************"}, "hash": {"type": "string", "example": "0x55c2e4ff28b2ca05b8c6b5728fed6e84b8013bf6f73cf0ff265b94d70a046ac3"}, "to": {"type": "string", "example": "0x2E14B26d4466Caa62744e4B92493c88CDFd64dd0"}, "value": {"type": "string", "example": "0.00001"}}}, "swagger.TransactionResponse": {"type": "object", "properties": {"report": {"$ref": "#/definitions/swagger.ScanTransactionResponse"}, "transaction_result": {"$ref": "#/definitions/swagger.TransactionResult"}}}, "swagger.TransactionResult": {"type": "object", "properties": {"status": {"type": "string"}, "transaction_hash": {"type": "string", "example": "0x55c2e4ff28b2ca05b8c6b5728fed6e84b8013bf6f73cf0ff265b94d70a046ac3"}, "transaction_id": {"type": "string"}}}, "swagger.TransactionsHistory": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/swagger.Transaction"}}, "pagination": {"type": "object", "properties": {"next": {"type": "string", "example": "http://**************:8083/api/v1/wallet/06a3a106-22e0-46ba-ab6f-86c4db387e1d/POL/transactions?offset=10&limit=5"}, "prev": {"type": "string", "example": "http://**************:8083/api/v1/wallet/06a3a106-22e0-46ba-ab6f-86c4db387e1d/POL/transactions?offset=5&limit=5"}, "total": {"type": "integer", "example": 20}}}}}, "swagger.Wallet": {"type": "object", "required": ["address", "request_id", "type", "uuid", "wallet_name"], "properties": {"address": {"type": "string"}, "archived_at": {"description": "Nullable, signals that this is archived", "type": "string"}, "dfnsWallets": {"type": "array", "items": {"$ref": "#/definitions/swagger.Asset"}}, "initialized_at": {"description": "Set when all the steps of creation are complete", "type": "string"}, "request_id": {"description": "RequestID - ID of the request", "type": "string"}, "safeheronWallet": {"description": "Nullable, set when create if type = Safeheron", "type": "array", "items": {"$ref": "#/definitions/swagger.SafeheronAsset"}}, "status": {"description": "Status for interaction with end-user\nInitially it should be 'Pending Creation'\nThen 'Provisioned', set when signaled by the Vault\nFinally 'Successfully Created' when a corresponding asset-account/wallet initiated\nMay be 'Failed' at any point", "type": "string"}, "type": {"type": "string"}, "uuid": {"description": "Corresponds to the uuid in the request to <PERSON><PERSON>", "type": "string"}, "wallet_name": {"type": "string"}}}, "swagger.WalletConnectRequest": {"type": "object", "properties": {"data": {"description": "NOTE: example of data object, it can depend on method", "type": "object", "properties": {"data": {"type": "string", "example": "0xd9627aa40000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000031bce709442b0000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee000000000000000000000000fdc4a3fc36df16a78edcaf1b837d3acaaedb2cb4869584cd00000000000000000000000010000000000000000000000000000000000000110000000000000000000000000000000000000000000000cc3caa3829638e2057"}, "from": {"type": "string", "example": "******************************************"}, "gas": {"type": "string", "example": "0x26465"}, "gasPrice": {"type": "string", "example": "0x4a817c800"}, "to": {"type": "string", "example": "******************************************"}, "value": {"type": "string", "example": "0x31bce709442b"}}}, "method": {"type": "string", "example": "eth_sendTransaction"}, "request_id": {"type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}}}, "swagger.WalletCreationRequest": {"type": "object", "properties": {"address": {"type": "string", "example": "mcf3d4db-acb3-4d36-b03f-f37655869cba"}, "request_id": {"type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}, "type": {"type": "string", "example": "safeheron"}, "wallet_name": {"type": "string", "example": "My Super Wallet"}}}, "swagger.WalletCreationStatusUpdate": {"type": "object", "properties": {"data": {"type": "object", "properties": {"api_key": {"type": "string", "example": "fe00000000000000000000000000"}, "private_key": {"type": "string", "example": "MIIJRAIBADANBgkqhkiG9w0BAQEFAASCCS4wggkqAgEAAoICAQDTX7ln3YOJnU+b 7AZK3iD"}, "public_key": {"type": "string", "example": "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAlkiGIW48hb4fwQIhpSgM7b++0ELepVW6+P6+N"}}}, "request_uuid": {"type": "string", "example": "6df3d4db-acb3-4d36-b03f-f37655869cba"}, "status": {"type": "string", "example": "provisioned"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}