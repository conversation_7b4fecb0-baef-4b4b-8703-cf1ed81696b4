package container

import (
	"github.com/Polity_MVP/wallet-infrastructure-service/config"
	"github.com/Polity_MVP/wallet-infrastructure-service/flagger"
	"github.com/Polity_MVP/wallet-infrastructure-service/validator"
	"go.uber.org/zap"
)

// WalletInfrastructureContainer represents an interface for accessing the data which sharing in overall application
type WalletInfrastructureContainer interface {
	GetConfig() *config.WalletInfrastructureConfig
	GetLogger() *zap.SugaredLogger
	GetValidator() *validator.WalletInfrastructureValidator
	GetFlagger() flagger.WalletInfrastructureFlagger
}

// container struct is for sharing data such as the setting of application and logger in overall this application
type container struct {
	config    *config.WalletInfrastructureConfig
	logger    *zap.SugaredLogger
	validator *validator.WalletInfrastructureValidator
	flagger   flagger.WalletInfrastructureFlagger
}

// NewWalletInfrastructureContainer is constructor
func NewWalletInfrastructureContainer(config *config.WalletInfrastructureConfig, logger *zap.SugaredLogger, validator *validator.WalletInfrastructureValidator, flagger flagger.WalletInfrastructureFlagger) WalletInfrastructureContainer {
	return &container{
		config:    config,
		logger:    logger,
		validator: validator,
		flagger:   flagger,
	}
}

// GetConfig returns the object of configuration
func (c *container) GetConfig() *config.WalletInfrastructureConfig {
	return c.config
}

// GetLogger returns the object of logger
func (c *container) GetLogger() *zap.SugaredLogger {
	return c.logger
}

// GetValidator returns the object of validator
func (c *container) GetValidator() *validator.WalletInfrastructureValidator {
	return c.validator
}

// GetFlagger returns the object of validator
func (c *container) GetFlagger() flagger.WalletInfrastructureFlagger {
	return c.flagger
}
