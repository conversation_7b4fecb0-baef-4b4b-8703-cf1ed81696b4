package polityVault

import "fmt"

type Error string

func (err Error) Error() string {
	return string(err)
}

const (
	ErrNotImplemented      = Error("Polity Vault: this functionality has not yet been implemented")
	ErrUnauthorized        = Error("Polity Vault Service: unauthorized")
	ErrForbidden           = Error("Polity Vault Service: forbidden")
	ErrNotFound            = Error("Polity Vault Service: not found")
	ErrInternalServerError = Error("unexpected response from Polity Vault Service: internal server error")
)

type ErrorResponse []struct {
	Field   string `json:"field"`
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (response ErrorResponse) Error() string {
	var str string
	for _, v := range response {
		str += fmt.Sprintf("%+v\n", v)
	}
	if len(str) > 0 {
		str = str[:len(str)-1] // remove trailing newline character
	}
	return str
}
