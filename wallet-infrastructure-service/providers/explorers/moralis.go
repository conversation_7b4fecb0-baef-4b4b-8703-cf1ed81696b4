package explorers

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/explorers"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/pagination"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/rates"
	"golang.org/x/sync/errgroup"
)

const (
	maxMoralisTransactionsPageSize = 100
	weiPerETH                      = 1000000000000000000

	typeSent     = "sent"
	typeReceived = "received"

	DefaultCurrencySymbol = "USD"
)

var UnsupportedAssetErr = errors.New("unsupported asset")

type moralisService struct {
	container   container.WalletInfrastructureContainer
	rateService rates.RateService
}

func NewMoralisService(container container.WalletInfrastructureContainer, rateService rates.RateService) ExplorerService {
	return &moralisService{
		container:   container,
		rateService: rateService,
	}
}

func (m *moralisService) GetTransactionsHistory(asset, address string, paginationParameters pagination.Parameters) (*polity.TransactionsHistory, error) {
	var cursor string
	var pages int
	transactionsHistory := []polity.Transaction{}

	val, ok := wallets.BaseNetworksAssets[asset]
	if !ok {
		return nil, UnsupportedAssetErr
	}
	intVal, err := strconv.ParseInt(val, 10, 64)
	if err != nil {
		return nil, err
	}
	chain := fmt.Sprintf("0x%s", strconv.FormatInt(intVal, 16))

	total, err := m.getWalletStats(chain, address)
	if err != nil {
		return nil, err
	}
	if paginationParameters.Offset >= total {
		return &polity.TransactionsHistory{Data: transactionsHistory, Pagination: pagination.TransactionsHistoryMeta{Total: total}}, nil
	}

	moralisTransactions := make([]explorers.MoralisTransaction, 0, maxMoralisTransactionsPageSize)
	url := fmt.Sprintf("%s%s?chain=%s", m.container.GetConfig().MoralisBaseUrl, address, chain)

	if paginationParameters.Offset == 0 {
		pages = 1
	} else {
		pages = int(math.Ceil(float64(paginationParameters.Offset) / float64(maxMoralisTransactionsPageSize)))
	}

	for page := 1; page <= pages; page++ {
		if int(paginationParameters.Offset) <= (page * maxMoralisTransactionsPageSize) {
			switch cursor {
			case "":
				history, err := m.getTransactions(fmt.Sprintf("%s&limit=%d", url, maxMoralisTransactionsPageSize))
				if err != nil {
					return nil, err
				}

				lack := int(paginationParameters.Limit) - len(history.Result[paginationParameters.Offset:])
				if lack <= 0 {
					moralisTransactions = history.Result[paginationParameters.Offset:(paginationParameters.Limit + paginationParameters.Offset)]
				} else if len(history.Result[paginationParameters.Offset:]) >= int(total) {
					moralisTransactions = history.Result[paginationParameters.Offset:]
				} else {
					moralisTransactions = history.Result[paginationParameters.Offset:]

					history, err = m.getTransactions(fmt.Sprintf("%s&cursor=%s", url, history.Cursor))
					if err != nil {
						return nil, err
					}

					if len(history.Result) > lack {
						moralisTransactions = append(moralisTransactions, history.Result[0:lack]...)
					} else {
						moralisTransactions = append(moralisTransactions, history.Result...)
					}
				}
			default:
				history, err := m.getTransactions(fmt.Sprintf("%s&cursor=%s", url, cursor))
				if err != nil {
					return nil, err
				}

				offsetInPage := int(paginationParameters.Offset) - (page-1)*maxMoralisTransactionsPageSize
				lack := paginationParameters.Limit - int64(len(history.Result[offsetInPage:]))
				if lack <= 0 {
					moralisTransactions = history.Result[offsetInPage:(int(paginationParameters.Limit) + offsetInPage)]
				} else {
					moralisTransactions = history.Result[offsetInPage:]

					history, err = m.getTransactions(fmt.Sprintf("%s&cursor=%s", url, history.Cursor))
					if err != nil {
						return nil, err
					}
					moralisTransactions = append(moralisTransactions, history.Result...)
				}
			}
			break
		}

		switch cursor {
		case "":
			history, err := m.getTransactions(url)
			if err != nil {
				return nil, err
			}
			cursor = history.Cursor
		default:
			history, err := m.getTransactions(fmt.Sprintf("%s&limit=%d&cursor=%s", url, paginationParameters.Limit, cursor))
			if err != nil {
				return nil, err
			}
			cursor = history.Cursor
		}
	}

	transactionsHistory, err = m.setValueUSD(asset, address, moralisTransactions)
	if err != nil {
		return nil, err
	}

	return &polity.TransactionsHistory{
		Data:       transactionsHistory,
		Pagination: pagination.TransactionsHistoryMeta{Total: total},
	}, nil
}

func (m *moralisService) getWalletStats(chain, address string) (int64, error) {
	var stats explorers.MoralisWalletStats
	var payload io.Reader

	url := fmt.Sprintf("%swallets/%s/stats?chain=%s", m.container.GetConfig().MoralisBaseUrl, address, chain)

	req, err := http.NewRequest(http.MethodGet, url, payload)
	if err != nil {
		return 0, err
	}
	req.Header.Add("Accept", "application/json")
	req.Header.Add("X-API-Key", m.container.GetConfig().MoralisApiKey)

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return 0, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return 0, err
	}

	err = json.Unmarshal(body, &stats)
	if err != nil {
		return 0, err
	}

	total, err := strconv.ParseFloat(stats.Transactions.Total, 64)
	if err != nil {
		return 0, err
	}

	return int64(total), nil
}

func (m *moralisService) setValueUSD(asset, address string, moralisTransactions []explorers.MoralisTransaction) ([]polity.Transaction, error) {
	transactionsHistory := []polity.Transaction{}

	var g errgroup.Group
	type transactions struct {
		usdPrice           float64
		moralisTransaction explorers.MoralisTransaction
	}
	transactionsChan := make(chan transactions, len(moralisTransactions))
	for _, transaction := range moralisTransactions {
		transaction := transaction
		g.Go(func() error {
			assetSymbol, ok := wallets.DFNSDefaultNetworks.CoinMarketCupMap[asset]
			if !ok {
				return fmt.Errorf("Can't convert assetSymbol '%v' to coinMarkectCup symbol. ", asset)
			}

			usdPrice, err := m.rateService.GetRate(assetSymbol, DefaultCurrencySymbol, transaction.Value, false)
			if err != nil {
				transactionsChan <- transactions{
					moralisTransaction: transaction,
					usdPrice:           0,
				}
				return nil
			}
			transactionsChan <- transactions{
				moralisTransaction: transaction,
				usdPrice:           usdPrice,
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		m.container.GetLogger().Debugf("Can't get usd value, error: '%v'\n", err)
	}
	close(transactionsChan)

	for i := range transactionsChan {
		date, err := time.Parse(time.RFC3339, i.moralisTransaction.Date)
		if err != nil {
			return nil, err
		}
		value, err := m.weiToETH(i.moralisTransaction.Value)
		if err != nil {
			return nil, err
		}

		var txType string

		switch i.moralisTransaction.From {
		case strings.ToLower(address):
			txType = typeSent
		default:
			txType = typeReceived
		}

		transactionsHistory = append(transactionsHistory, polity.Transaction{
			Date:     date,
			From:     i.moralisTransaction.From,
			To:       i.moralisTransaction.To,
			Hash:     i.moralisTransaction.Hash,
			Value:    value,
			ValueUSD: fmt.Sprintf("%.2f", i.usdPrice),
			Gas:      i.moralisTransaction.Gas,
			Type:     txType,
		})
	}

	sort.Slice(transactionsHistory, func(i, j int) bool {
		return transactionsHistory[i].Date.After(transactionsHistory[j].Date)
	},
	)

	return transactionsHistory, nil
}

func (m *moralisService) getTransactions(url string) (*explorers.MoralisTransactionsHistory, error) {
	var history explorers.MoralisTransactionsHistory
	var payload io.Reader
	req, err := http.NewRequest(http.MethodGet, url, payload)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Accept", "application/json")
	req.Header.Add("X-API-Key", m.container.GetConfig().MoralisApiKey)

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &history)
	if err != nil {
		return nil, err
	}

	return &history, nil
}

func (m *moralisService) weiToETH(wei string) (string, error) {
	if wei == "" {
		return "", nil
	}

	float, err := strconv.ParseFloat(wei, 64)
	if err != nil {
		return "", err
	}

	ethValue := float / weiPerETH

	eth := strconv.FormatFloat(ethValue, 'f', -1, 64)

	return eth, nil
}
