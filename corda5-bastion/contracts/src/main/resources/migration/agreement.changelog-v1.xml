<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <changeSet author="R3.Corda" id="create_agreement_state">
        <createTable tableName="agreement_states">
            <column name="output_index" type="INT"/>
            <column name="transaction_id" type="NVARCHAR(128)"/>
            <column name="avatar_id" type="NVARCHAR(255)"/>
            <column name="agreement_id" type="NVARCHAR(255)"/>
            <column name="investment" type="NVARCHAR(255)"/>
            <column name="fee" type="NVARCHAR(255)"/>
            <column name="status" type="NVARCHAR(255)"/>
            <column name="declined_by" type="NVARCHAR(255)"/>
            <column name="decline_reason" type="NVARCHAR(255)"/>
            <column name="agreement_date" type="date"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
