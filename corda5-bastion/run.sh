#!/bin/sh

echo "--Step 1: Building projects.--"
./gradlew clean build

echo "--Step 2: Creating cpb file.--"
cordapp-builder create --cpk contracts/build/libs/corda5-template-contracts-1.0-SNAPSHOT-cordapp.cpk --cpk workflows/build/libs/corda5-template-workflows-1.0-SNAPSHOT-cordapp.cpk -o c5bastion.cpb

echo "--Step 3: Configure the network.--"
corda-cli network config docker-compose c5bastion-network

echo "--Step 4: Creating docker compose yaml file and starting docker containers.--"
corda-cli network deploy -n c5bastion-network -f c5bastion.yaml | docker compose -f - up -d

echo "--Listening to the docker processes.--"
corda-cli network wait -n c5bastion-network

echo "--Step 5: Install the cpb file into the network.--"
corda-cli package install -n c5bastion-network c5bastion.cpb

echo "--Listening to the docker processes.--"
corda-cli network wait -n c5bastion-network

echo "++<PERSON><PERSON><PERSON> Setup Finished, Nodes Status: ++"
corda-cli network status -n c5bastion-network