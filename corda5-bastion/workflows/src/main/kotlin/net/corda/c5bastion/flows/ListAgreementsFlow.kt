package net.corda.c5bastion.flows

import net.corda.c5bastion.states.AgreementState
import net.corda.v5.application.flows.*
import net.corda.v5.application.flows.flowservices.FlowIdentity
import net.corda.v5.application.injection.CordaInject
import net.corda.v5.application.services.json.JsonMarshallingService
import net.corda.v5.application.services.json.parseJson
import net.corda.v5.application.services.persistence.PersistenceService
import net.corda.v5.base.annotations.Suspendable
import net.corda.v5.base.stream.Cursor
import net.corda.v5.base.util.seconds
import net.corda.v5.ledger.contracts.StateAndRef
import net.corda.v5.ledger.schemas.vault.VaultSchemaV1
import net.corda.v5.ledger.services.vault.IdentityContractStatePostProcessor
import net.corda.v5.ledger.services.vault.SetBasedVaultQueryFilter
import net.corda.v5.ledger.services.vault.StateStatus


@InitiatingFlow
@StartableByRPC
class ListAgreementsFlow @JsonConstructor constructor(private val params: RpcStartFlowRequestParameters) :
    Flow<List<String>> {
    @CordaInject
    lateinit var persistenceService: PersistenceService

    @CordaInject
    lateinit var jsonMarshallingService: JsonMarshallingService

    @CordaInject
    lateinit var flowIdentity: FlowIdentity

    @Suspendable
    override fun call(): List<String> {

        val mapOfParams: Map<String, String> = jsonMarshallingService.parseJson(params.parametersInJson)

        val avatarID = mapOfParams["avatarID"]
        val queryName: String
        val queryParams = mutableMapOf<String, String>()
        if (avatarID == null) {
            queryName = "AgreementSchemaV1.PersistentAgreement.FindAllUnconsumedOrCompleted"
        } else {
            queryName = "AgreementSchemaV1.PersistentAgreement.FindAllUnconsumedOrCompletedByAvatarID"
            queryParams["avatarID"] = avatarID
        }


        val cursor: Cursor<AgreementState> = persistenceService.query(
            queryName,
            queryParams,
            IdentityContractStatePostProcessor.POST_PROCESSOR_NAME
        )

        val accumulator = mutableListOf<AgreementState>()
        do {
            val poll = cursor.poll(100, 10.seconds)
            accumulator.addAll((poll.values))
        } while (!poll.isLastResult)

        return accumulator
            .map { agreement -> agreement.toJsonString() }
    }
}
