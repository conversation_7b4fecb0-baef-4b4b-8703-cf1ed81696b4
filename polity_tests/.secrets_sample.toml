# Rename this file to .secrets.toml and fill env values
[default]
JIRA_ISSUES_URL = "https://${jira_domain}/browse/"
DFNS_KEYS = {jwt_token=""}
SAFEHERON_KEYS = { public_key = "", private_key= "", api_key="", platform_public_key="", notif_platform_pub_key=""}
VAULT_TOKEN=""


[ENV_NAME]
ENVIROMENT = ""
JWT_SIGNATURE = ""
POLITY_VAULT_URL = ""
BWI_URL = ""
AUTH_GATEWAY_URL = ""
USER_INFRASTRUCTURE_URL = ""
POLITY_VAULT_DB = { port = "", host = "", database = "", user = "", password = "" }
BWI_DB = { port = "", host = "", database = "", user = "", password = "" }
VAULT_URL=""
AUTH_GATEWAY_DB = { port = "", host = "", database = "", user = "", password = "" }
USER_INFRASTRUCTURE_DB = { port = "", host = "", database = "", user = "", password = "" }

VAULT_URL = ""
VAULT_NAMESPACE=""
VAULT_ROLE_ID=""
VAULT_SECRET_ID=""


