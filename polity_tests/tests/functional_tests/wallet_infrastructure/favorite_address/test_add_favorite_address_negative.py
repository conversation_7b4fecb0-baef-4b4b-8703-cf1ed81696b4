from allure import story, description
from pytest import mark

from tests.functional_tests.wallet_infrastructure.favorite_address.conftest import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from tests.functional_tests.wallet_infrastructure.favorite_address.negative_cases import FavoriteAddressNegativeParams
from core.services.wallet_infrastructure.dtos.input.favorite_address_input import FavoriteAddressInput


@story("Add favorite address Negative")
class TestAddFavoriteAddressNegative(FavoriteAddressRunner):

    @description("Add favorite address negative")
    @mark.parametrize("input_body, expected_error", **FavoriteAddressNegativeParams().mark_params())
    def test_add_favorite_address_negative(self, jwt_dto, input_body, expected_error):
        resp = self.wallet_ctrl.add_favorite_address(jwt_dto.encode_jwt(), input_body, status_code=400)
        self.favorite_address_assert.assert_error(resp, expected_error)

    @description("Add duplicated favorite address negative")
    def test_add_duplicated_favorite_address(self, jwt_dto):
        input_body = FavoriteAddressInput.random()
        self.wallet_ctrl.add_favorite_address(jwt_dto.encode_jwt(), input_body)

        input_body.update(name='some_name')
        resp = self.wallet_ctrl.add_favorite_address(jwt_dto.encode_jwt(), input_body, 400)
        self.favorite_address_assert.assert_duplicated_values(resp)
