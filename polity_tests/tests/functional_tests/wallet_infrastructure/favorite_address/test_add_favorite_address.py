from allure import story, description
from pytest import mark

from tests.functional_tests.wallet_infrastructure.favorite_address.conftest import <PERSON><PERSON><PERSON><PERSON><PERSON>unner
from core.services.wallet_infrastructure.dtos.input.favorite_address_input import FavoriteAddressInput


@story("Add favorite address")
class TestAddFavoriteAddress(FavoriteAddressRunner):

    @description("Add favorite address")
    def test_add_favorite_address(self, jwt_dto):
        input_body = FavoriteAddressInput.random()
        self.wallet_ctrl.add_favorite_address(jwt_dto.encode_jwt(), input_body)

        self.favorite_address_assert.assert_favorite_address(user_id=jwt_dto.uuid, address_list=[input_body])

    @description("Add favorite address with the same address/assets")
    @mark.parametrize('input_bodies', [FavoriteAddressRunner._input_body_with_the_same_address(),
                                       FavoriteAddressRunner._input_body_with_the_same_asset()],
                      ids=['the same_address', 'the same assets'])
    def test_add_favorite_address_same_parameters(self, jwt_dto, input_bodies):
        self.wallet_ctrl.add_favorite_address(jwt_dto.encode_jwt(), input_bodies[0])
        self.wallet_ctrl.add_favorite_address(jwt_dto.encode_jwt(), input_bodies[1])

        self.favorite_address_assert.assert_favorite_address(user_id=jwt_dto.uuid, address_list=input_bodies)
