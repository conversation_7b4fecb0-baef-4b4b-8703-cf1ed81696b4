from allure import story, description

from core.services.wallet_infrastructure.dtos.input.delete_favorite_address_input import DeleteFavoriteAddressInput
from core.utils.helpers.polity_jwt import J<PERSON><PERSON>ncode
from tests.functional_tests.wallet_infrastructure.favorite_address.conftest import FavoriteAddressRunner


@story("Delete favorite address")
class TestDeleteFavoriteAddress(FavoriteAddressRunner):

    @description("Delete favorite address")
    def test_delete_favorite_address(self, adviser_jwt):
        favorites = self.add_favorites_addresses_to_user(jwt=adviser_jwt.encode_jwt(), count=2)
        first = favorites.pop(0)
        second = favorites.pop(0)

        # drop one favorite and check one is left
        input_body = DeleteFavoriteAddressInput(first.address, first.asset_symbol)
        self.wallet_ctrl.delete_favorite_address(jwt=adviser_jwt.encode_jwt(), input_body=input_body)
        self.favorite_address_assert.assert_favorite_address(user_id=adviser_jwt.uuid, address_list=[second])

        # drop second favorite address and check no one is left
        input_body = DeleteFavoriteAddressInput(second.address, second.asset_symbol)
        self.wallet_ctrl.delete_favorite_address(jwt=adviser_jwt.encode_jwt(), input_body=input_body)
        self.favorite_address_assert.assert_favorite_address(user_id=adviser_jwt.uuid, address_list=[second])
