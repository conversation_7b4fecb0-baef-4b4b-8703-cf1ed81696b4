from allure import story, description
from pytest import mark, fixture

from core.enums.provisioning_status import WalletProvisioningStatus
from core.enums.wallet_type import WalletType
from core.services.user_infrustructure.dtos.input.notifications_params import NotificationsParams
from core.services.user_infrustructure.dtos.notification_payloads.wallet_created_dto import WalletNotificationDTO
from core.services.user_infrustructure.enum.notification_types import NotificationTypes
from core.utils.generators.wallet_utils import WalletGeneratorUtils
from tests.functional_tests.wallet_infrastructure.user_notifications_integration.conftest import \
    NotificationIntegrationRunner


@story("Notifications webhook - Wallet is provisioned")
class TestNotificationsProvisionedWallet(NotificationIntegrationRunner):

    @description("Provision DFNS/Safeheron wallet and check notification webhook has been triggered")
    @mark.real_wallet
    @mark.parametrize("wallet_type", list(WalletType))
    def test_provision_wallet_check_notifications(self, avatar_jwt, wallet_type):
        request_id = WalletGeneratorUtils(avatar_jwt, wallet_type).new_wallet().with_api_keys() \
            .with_callback().wallet_request.request_id

        actual_notifications = self.user_infrastructure_ctrl.get_notifications_list(avatar_jwt.encode_jwt(),
                                                                                    NotificationsParams(isRead=False))
        self.notification_assert.assert_notification_details(
            actual_notifications, is_read=False, expected_meta={},
            expected_notification_type=NotificationTypes.WALLET_NOTIFICATION.value,
            expected_payload=WalletNotificationDTO(WalletProvisioningStatus.PROVISIONED.value,
                                                   request_id, 'aqa_wallet'))
