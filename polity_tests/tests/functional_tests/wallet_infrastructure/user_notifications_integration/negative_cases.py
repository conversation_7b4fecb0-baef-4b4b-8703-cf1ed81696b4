from core.services.wallet_infrastructure.enums.dfns_payment_status import DFNSPaymentStatus
from core.utils.parametrized_cases_utils import ParametrizedCasesBase


class DFNSPaymentStatusNegative(ParametrizedCasesBase):
    __default_err_keys = {"wallet_key": "TransactionRequest"}
    _data = [
        DFNSPaymentStatus.APPROVED, DFNSPaymentStatus.EXECUTED, DFNSPaymentStatus.INITIATED, DFNSPaymentStatus.REJECTED
    ]

    def _get_cases(self):
        return self._data

    def _get_names(self):
        return self._data
