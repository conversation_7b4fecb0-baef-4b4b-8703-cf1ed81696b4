from allure import story, description
from pytest import fixture, mark

from core.enums.asset_types import AssetTypes
from core.services.user_infrustructure.dtos.input.notifications_params import NotificationsParams
from core.services.wallet_infrastructure.dtos.input.payment_notification.payment_notification_input import \
    PaymentNotificationInput
from tests.functional_tests.conftest import faker
from tests.functional_tests.wallet_infrastructure.user_notifications_integration.conftest import \
    NotificationIntegrationRunner
from tests.functional_tests.wallet_infrastructure.user_notifications_integration.negative_cases import \
    DFNSPaymentStatusNegative


@story("Notifications webhook - when DFNS funds are received")
class TestNotificationsDFNSPaymentReceived(NotificationIntegrationRunner):

    @fixture(**DFNSPaymentStatusNegative().fixture_params())
    def payment_notification_input(self, avatar_jwt, dfns_assets_list, request):
        dfns_asset = next(filter(lambda a: a['asset_symbol'] == AssetTypes.Ethereum.value, dfns_assets_list))
        yield PaymentNotificationInput.get_input(asset_symbol=AssetTypes.Ethereum.value,
                                                 asset_account_id=dfns_asset["account_id"],
                                                 org_id=dfns_asset['organization_id'],
                                                 receiver_address=faker.pystr(),
                                                 status=request.param)

    @description("Send DFNS payment notification request with wrong status and check notifications list is empty")
    @mark.real_wallet
    def test_dfns_transaction_notifications_negative(self, avatar_jwt, provisioned_dfns_wallet_id, payment_notification_input):
        self.payment_ctrl.send_dfns_payment_notification(payment_notification_input)
        self.actual_notifications = self.user_infrastructure_ctrl.get_notifications_list(avatar_jwt.encode_jwt(),
                                                                                         NotificationsParams(isRead=False))
        self.notification_assert.assert_notifications_list_is_empty(self.actual_notifications)
