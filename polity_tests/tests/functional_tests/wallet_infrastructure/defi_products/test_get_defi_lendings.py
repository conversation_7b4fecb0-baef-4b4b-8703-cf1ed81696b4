from allure import story, description, link
from pytest import mark

from core.real_data.avatar_data import get_avatar
from core.utils.allure_report.allure_utils import jira_link
from tests.functional_tests.wallet_infrastructure.defi_products.conftest import DeFiProductsRunner


@story("Get defi lending")
class TestDefiLending(DeFiProductsRunner):

    @description("Get defi lending")
    def test_test_get_defi_lending(self, existed_avatar_jwt):
        response = self.defi_products_ctrl.get_defi_lending(jwt=existed_avatar_jwt.encode_jwt(),
                                                            request_id=get_avatar().wallets[0].request_id)
        self.defi_assert.defi_lending_asset(response=response)
