from allure import feature
from pytest import mark

from tests.functional_tests.wallet_infrastructure.conftest import <PERSON><PERSON><PERSON>unner
from core.services.wallet_infrastructure.assertions.defi_product_assert import DeFiAssert


@feature("Send webhook")
@mark.real_wallet
class DeFiProductsRunner(BWIRunner):

    defi_assert = DeFiAssert()

    def get_expected_defi_products(self):
        return {"defi_products": self.wallets_db.get_defi_products()}


