import uuid

from allure import story, description, link
from pytest import mark

from core.utils.allure_report.allure_utils import jira_link
from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.enums.errors_handling import Errors
from tests.functional_tests.wallet_infrastructure.assets.conftest import WalletAssetsRunner


@story("Assets list - Negative scenarios")
class TestAssetsListNegative(WalletAssetsRunner):

    @description("Get assets list for non-existed request id")
    @link(**jira_link("PLT-350"))
    @mark.xfail
    @mark.parametrize("wallet_type", list(WalletType))
    def test_assets_list_non_existed_request_id_negative(self, jwt_dto, wallet_type):
        err_msg = self.wallet_ctrl.get_assets_list(jwt_dto.encode_jwt(), request_id=str(uuid.uuid4()),
                                                   wallet_type=wallet_type,
                                                   status_code=404)
        self.asset_assert.assert_error_contains(err_msg, Errors.RECORD_NOT_FOUND_POLITY_VAULT.value)
