import uuid

from allure import story, description, link
from pytest import mark

from core.utils.allure_report.allure_utils import jira_link
from core.services.wallet_infrastructure.enums.asset_types import WalletsMap
from core.services.wallet_infrastructure.enums.errors_handling import Errors
from core.utils.helpers.polity_jwt import JWT
from tests.functional_tests.conftest import faker
from core.enums.wallet_type import WalletType
from tests.functional_tests.wallet_infrastructure.assets.conftest import WalletAssetsRunner


@story("Asset address - Negative scenarios")
class TestAssetsAddress(WalletAssetsRunner):

    def _get_error_message(self, wallet_type: WalletType):
        return {WalletType.DFNS: Errors.NO_ADDRESS_PROVIDED_ASSET_FOUND.get_msg(),
                WalletType.SAFEHERON: Errors.NO_ADDRESS_ASSET_SAFEHERON.get_msg(),
                }[wallet_type]

    @description("Get address for Safeheron/DFNS asset when request id not exists - negative")
    @link(**jira_link("PLT-350"))
    @mark.xfail
    def test_asset_address_random_request_id_negative(self, jwt_dto, parametrized_wallet_type):
        err_msg = self.wallet_ctrl.get_asset_address(jwt_dto.encode_jwt(),
                                                     request_id=str(uuid.uuid4()),
                                                     wallet_type=parametrized_wallet_type,
                                                     asset_symbol=getattr(WalletsMap, parametrized_wallet_type.name).values[0],
                                                     status_code=404)
        self.asset_assert.assert_error_contains(err_msg, Errors.RECORD_NOT_FOUND_POLITY_VAULT.get_msg())

    @description("Get address for Safeheron/DFNS asset when asset code not exists - negative")
    @mark.real_wallet
    def test_asset_address_random_asset_name_negative(self, jwt_dto, parametrized_wallet_id, parametrized_wallet_type):
        err_msg = self.wallet_ctrl.get_asset_address(jwt_dto.encode_jwt(),
                                                     request_id=parametrized_wallet_id,
                                                     wallet_type=parametrized_wallet_type,
                                                     asset_symbol=faker.pystr(),
                                                     status_code=404)
        self.asset_assert.assert_error_contains(err_msg, self._get_error_message(parametrized_wallet_type))

    @description("Get address for Safeheron/DFNS asset by another user - negative")
    @mark.real_wallet
    def test_asset_address_with_another_jwt_negative(self, jwt_dto, parametrized_wallet_id, parametrized_wallet_type):
        err_msg = self.wallet_ctrl.get_asset_address(JWT.random().encode_jwt(),
                                                     request_id=parametrized_wallet_id,
                                                     wallet_type=parametrized_wallet_type,
                                                     asset_symbol="ETH",
                                                     status_code=404)
        self.asset_assert.assert_error_contains(err_msg, Errors.NO_ADDRESS_PROVIDED_ASSET_FOUND.get_msg())
