from allure import story, description
from pytest import mark

from tests.functional_tests.wallet_infrastructure.wallet.conftest import WalletRunner
from tests.functional_tests.wallet_infrastructure.wallet.positive_cases import WalletCreateParams


@story("Create wallet")
class TestCreateWallet(WalletRunner):

    @description("Create DFNS/Safeheron wallet")
    @mark.parametrize("request_body", **WalletCreateParams().mark_params())
    def test_create_wallet(self, jwt_dto, request_body):
        self.wallet_ctrl.create_wallet(jwt_dto.encode_jwt(), request_body)
        actual_wallet = self.wallet_ctrl.get_wallet_details(jwt_dto.encode_jwt(),  request_body.request_id)
        self.wallet_assert.assert_wallet_created(actual_wallet, request_body, jwt_dto.uuid)
