from allure import story, description
from pytest import mark

from core.services.wallet_infrastructure.dtos.input.archive_wallet_input import ArchiveWalletInput
from core.services.wallet_infrastructure.enums.errors_handling import Errors
from core.utils.helpers.polity_jwt import JWT
from tests.functional_tests.wallet_infrastructure.wallet.conftest import <PERSON><PERSON><PERSON><PERSON><PERSON>
from tests.functional_tests.wallet_infrastructure.wallet.negative_cases import WalletArchiveNegativeParams


@story("Archive wallet negative")
class TestArchiveWalletNegative(WalletRunner):

    @description("Archive wallet negative")
    @mark.parametrize("key, value, message, status_code", **WalletArchiveNegativeParams().mark_params())
    def test_archive_wallet_negative(self, key, message, value, status_code, jwt_dto):
        input_body = ArchiveWalletInput.random().update(**{key: value})
        resp = self.wallet_ctrl.archive_wallet(jwt=jwt_dto.encode_jwt(), input_body=input_body, status_code=status_code)
        self.wallet_assert.assert_error_property_include_text(resp, message)

    @description("Archive wallet by another user - Negative")
    def test_archive_wallet_by_another_user_negative(self, parametrized_created_wallet):
        input_body = ArchiveWalletInput(request_id=parametrized_created_wallet.request_id)
        resp = self.wallet_ctrl.archive_wallet(jwt=JWT.random().encode_jwt(), input_body=input_body, status_code=404)
        self.wallet_assert.assert_error(resp, Errors.WALLET_NOT_FOUND.get_msg())
