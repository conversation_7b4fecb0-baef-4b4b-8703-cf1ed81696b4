from allure import story, description, link
from pytest import mark, fixture

from core.enums.provisioning_status import WalletProvisioningStatus
from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.dtos.input.webhook_input import WebhookInput
from core.utils.allure_report.allure_utils import jira_link
from tests.functional_tests.wallet_infrastructure.webhook.conftest import <PERSON>ho<PERSON><PERSON><PERSON><PERSON>
from tests.functional_tests.wallet_infrastructure.webhook.negative_cases import SendWebhookDfnsKeysNegativeParams


@story("Send webhook(callback) DFNS - Negative")
class TestSendWebhookNegative(WebhookRunner):

    @fixture
    def correct_input_body(self, created_dfns_wallet):
        yield WebhookInput(request_uuid=created_dfns_wallet.request_id,
                           status=WalletProvisioningStatus.PROVISIONED.value,
                           data=WalletType.DFNS.api_keys)

    @description("Send DFNS webhook with wrong/missed jwt_token key")
    @mark.parametrize("negative_data,expected_error,status_code",
                      **SendWebhookDfnsKeysNegativeParams().mark_params())
    def test_send_webhook_dfns_data_negative(self, jwt_dto, negative_data, expected_error, status_code,
                                             correct_input_body):
        # Prepare negative data
        input_body = correct_input_body.with_data(negative_data)

        # Send request
        error_msg = self.wallet_ctrl.send_webhook(input_body, status_code=status_code)
        self.webhook_assert.assert_error(error_msg, expected_error)
