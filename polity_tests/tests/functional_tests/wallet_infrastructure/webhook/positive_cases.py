from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.assertions.webhook_assert_dfns import WebhookDFNSAssert
from core.services.wallet_infrastructure.assertions.webhook_assert_safeheron import WebhookSafeheronAssert
from core.utils.parametrized_cases_utils import ParametrizedCasesBase


class WebhookWalletTypesParams(ParametrizedCasesBase):
    _data = [
        (WalletType.DFNS, WebhookDFNSAssert),
        (WalletType.SAFEHERON, WebhookSafeheronAssert)
    ]

    def _get_cases(self):
        return [(k[0], k[1]) for k in self._data]

    def _get_names(self):
        return [k[0] for k in self._data]
