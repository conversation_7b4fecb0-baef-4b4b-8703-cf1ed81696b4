from core.services.wallet_infrastructure.enums.errors_handling import Errors
from core.utils.parametrized_cases_utils import ParametrizedCasesBase
from tests.functional_tests.conftest import faker


class SendWebhookNegativeParams(ParametrizedCasesBase):
    __default_params = {"wallet_key": "WalletCreationStatusUpdate"}
    _data = [

        ("request_uuid", None, Errors.PROPERTY_REQUIRED_OBJ.get_msg(property_name="RequestID", **__default_params),
         400, "is not provided"),
        ("status", None, Errors.PROPERTY_REQUIRED_OBJ.get_msg(property_name="Status", **__default_params), 400,
         "is not provided"),
        ("request_uuid", faker.uuid4(), Errors.WALLET_DOESNT_EXIST_OBJ.get_msg(), 404, "is not exist"),
        ("request_uuid", faker.pystr(), Errors.VALIDATION_FAILED_OBJ.get_msg(
            property_name="RequestID", tag_name='request_id', **__default_params), 400, "is not correct uuid"),
        ("request_uuid", faker.pyint(), Errors.CANNOT_UNMARSHAL_OBJECT_PROPERTY.get_msg(
            property_name="request_uuid",
            actual_type="number",
            expected_type="string", **__default_params), 400, "is int"),
        ("request_uuid", faker.pybool(), Errors.CANNOT_UNMARSHAL_OBJECT_PROPERTY.get_msg(
            property_name="request_uuid",
            actual_type="bool",
            expected_type="string", **__default_params), 400, "is bool"),

        ("status", "Some_Status", "Unknown status for wallet: Some_Status ", 400, "is incorrect"),
        ("status", faker.pyint(), Errors.CANNOT_UNMARSHAL_OBJECT_PROPERTY.get_msg(property_name="status",
                                                                                  actual_type="number",
                                                                                  expected_type="vault.Status",
                                                                                  **__default_params), 400, "is int"),
        ("status", faker.pybool(), Errors.CANNOT_UNMARSHAL_OBJECT_PROPERTY.get_msg(property_name="status",
                                                                                   actual_type="bool",
                                                                                   expected_type="vault.Status",
                                                                                   **__default_params), 400, "is bool")
    ]

    def _get_cases(self):
        return [(k[0], k[1], k[2], k[3]) for k in self._data]

    def _get_names(self):
        return [f"{k[0]} {k[4]}" for k in self._data]


class SendWebhookDfnsKeysNegativeParams(ParametrizedCasesBase):
    __default_params = {"wallet_key": "DFNSCredentials"}
    _data = [
        # data object
        (faker.pystr(), Errors.CANNOT_UNMARSHAL_OBJECT_VALUE.get_msg(
            property_name="data",
            actual_type="string",
            expected_type="vault.DFNSCredentials", **__default_params), 400, "data is str"),
        (faker.pyint(), Errors.CANNOT_UNMARSHAL_OBJECT_VALUE.get_msg(
            property_name="data",
            actual_type="number",
            expected_type="vault.DFNSCredentials", **__default_params), 400, "data is int"),
        (faker.pybool(), Errors.CANNOT_UNMARSHAL_OBJECT_VALUE.get_msg(
            property_name="data",
            actual_type="bool",
            expected_type="vault.DFNSCredentials", **__default_params), 400, "data is bool"),
        # jwt token
        ({"jwt_token": None}, '{"message":"Request body is not valid: Set \'jwt_token\' for dfns client. "}\n',
         400, "jwt_token is not provided"),
        ({"jwt_token": faker.pystr()}, Errors.DFNS_UNAUTHORIZED.get_msg(), 400, "jwt_token is incorrect"),
        ({"jwt_token": faker.pyint()},
         Errors.CANNOT_UNMARSHAL_OBJECT_PROPERTY.get_msg(property_name="jwt_token",
                                                         actual_type="number",
                                                         expected_type="string", **__default_params),
         400, "jwt_token is int"),
        ({"jwt_token": faker.pybool()},
         Errors.CANNOT_UNMARSHAL_OBJECT_PROPERTY.get_msg(property_name="jwt_token",
                                                         actual_type="bool",
                                                         expected_type="string", **__default_params),
         400, "jwt_token is bool")
    ]

    def _get_cases(self):
        return [(k[0], k[1], k[2]) for k in self._data]

    def _get_names(self):
        return [k[3] for k in self._data]


class SendWebhookSafeheronKeysNegativeParams(ParametrizedCasesBase):
    __default_params = {"wallet_key": "SafeheronCredentials"}
    _data = [
        # data object
        (None, ("Key: 'WalletCreationStatusUpdate.Data' Error:Field validation for 'Data' failed on the 'data' tag",),
         400, "data is not provided"),
        (faker.pystr(),
         (Errors.CANNOT_UNMARSHAL_OBJECT_VALID.get_msg(property_name="data",
                                                 actual_type="string",
                                                 expected_type="vault.SafeheronCredentials"),), 400, "data is str"),
        (faker.pyint(),
         (Errors.CANNOT_UNMARSHAL_OBJECT_VALID.get_msg(property_name="data",
                                                 actual_type="number",
                                                 expected_type="vault.SafeheronCredentials"),), 400, "data is int"),
        (faker.pybool(), (Errors.CANNOT_UNMARSHAL_OBJECT_VALID.get_msg(property_name="data",
                                                                 actual_type="bool",
                                                                 expected_type="vault.SafeheronCredentials"),),
         400, "data is bool"),

        # Public key
        ({"public_key": None, "private_key": faker.pystr(), "api_key": faker.pystr()},
         (Errors.PROPERTY_REQUIRED.get_msg(property_name="PlatformPublicKey", **__default_params)),
         400, "public_key is not provided"),
        ({"public_key": faker.pyint()},
         (Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="number", expected_type="string",
                                               property_name="public_key", **__default_params)),
         400, "public_key is incorrect type (int)"),
        ({"public_key": faker.pybool()},
         (Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="bool", expected_type="string",
                                               property_name="public_key", **__default_params)),
         400, "public_key is incorrect type (bool)"),
        # Private key
        ({"private_key": None, "public_key": faker.pystr(), "api_key": faker.pystr()},
         (Errors.PROPERTY_REQUIRED.get_msg(property_name="PrivateKey", **__default_params)),
         400, "private_key is not provided"),
        ({"private_key": faker.pyint()},
         (Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="number", expected_type="string",
                                               property_name="public_key", **__default_params)), 400,
         "private_key is incorrect type (int)"),
        ({"private_key": faker.pybool()},
         (Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="bool", expected_type="string",
                                               property_name="public_key", **__default_params)), 400,
         "private_key is incorrect type (bool)"),
        # Api key
        ({"api_key": None, "public_key": faker.pystr(), "private_key": faker.pystr()},
         (Errors.PROPERTY_REQUIRED.get_msg(property_name="ApiKey", **__default_params)),
         400, "api_key is not provided"),
        ({"api_key": faker.pyint()}, (Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="number", expected_type="string",
                                                                           property_name="public_key",
                                                                           **__default_params)), 400,
         "api_key is incorrect type (int)"),
        ({"api_key": faker.pybool()}, (Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="bool", expected_type="string",
                                                                            property_name="public_key",
                                                                            **__default_params)), 400,
         "api_key is incorrect type (bool)")
    ]

    def _get_cases(self):
        return [(k[0], k[1], k[2]) for k in self._data]

    def _get_names(self):
        return [k[3] for k in self._data]
