from allure import story, description, link
from pytest import mark
from core.utils.allure_report.allure_utils import jira_link

from core.services.wallet_infrastructure.dtos.input.payment_last_input import PaymentLastInput
from tests.functional_tests.wallet_infrastructure.payment.conftest import PaymentRunner
from core.enums.wallet_type import WalletType


@story("Get last payments")
class TestGetLastPayments(PaymentRunner):

    @description("Last payment: avatar has trx")
    def test_last_payment_avatar_with_trx(self, existed_avatar_jwt):
        input_body = PaymentLastInput.random(wallet_type=WalletType.SAFEHERON, users_uuid=[existed_avatar_jwt.uuid])
        response = self.wallet_ctrl.get_last_payments(input_body=input_body)
        self.last_payment_assert.response_has_avatar_uuid(response)

    @mark.xfail
    @link(**jira_link("PLT-627"))
    @description("Last payment: avatar has no trx")
    def test_last_payment_avatar_without_trx(self, jwt_dto):
        input_body = PaymentLastInput.random(wallet_type=WalletType.SAFEHERON, users_uuid=[jwt_dto.uuid])
        response = self.wallet_ctrl.get_last_payments(input_body=input_body)
        self.last_payment_assert.response_has_avatar_uuid(response)
