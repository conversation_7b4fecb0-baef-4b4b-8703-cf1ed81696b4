from core.services.wallet_infrastructure.enums.errors_handling import Errors
from core.utils.parametrized_cases_utils import ParametrizedCasesBase
from tests.functional_tests.conftest import faker


class PaymentAmountNegativeParams(ParametrizedCasesBase):
    __default_err_keys = {"wallet_key": "TransactionRequest"}
    _data = [
        # Negative amount
        ({"value": faker.pyint()}, Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="number", expected_type="string",
                                                                        property_name="value", **__default_err_keys),
         "amount value is int"),
        ({"value": {"aaa": 123}}, Errors.CANNOT_UNMARSHAL_TYPE.get_msg(actual_type="object", expected_type="string",
                                                                       property_name="value", **__default_err_keys),
         "amount value is dict"),
        # Missed required properties
        ({"from_wallet": None}, Errors.PROPERTY_REQUIRED.get_msg(property_name="From", **__default_err_keys),
         "from is missed"),
        ({"to": None}, Errors.PROPERTY_REQUIRED.get_msg(property_name="To", **__default_err_keys),
         "to is missed"),
        ({"asset": None}, Errors.PROPERTY_REQUIRED.get_msg(property_name="Asset", **__default_err_keys),
         "asset is missed"),
        ({"value": None}, Errors.PROPERTY_REQUIRED.get_msg(property_name="Value", **__default_err_keys),
         "value is missed"),
    ]

    def _get_cases(self):
        return [(k[0], k[1]) for k in self._data]

    def _get_names(self):
        return [k[2] for k in self._data]
