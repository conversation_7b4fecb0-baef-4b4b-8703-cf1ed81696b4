from allure import feature, description, link
from pytest import mark

from core.utils.access_token_cases_utils import AuthorizationHeadersNegative
from core.utils.allure_report.allure_utils import jira_link
from tests.functional_tests.wallet_infrastructure.auth.negative_cases import EndpointsListNegative
from tests.functional_tests.wallet_infrastructure.conftest import BWIRunner


@feature("Authentication negative scenarios")
class TestCallEndpointsAuthenticationNegative(BWIRunner):

    @mark.xfail
    @link(**jira_link("PLT-314"))
    @description("Wallet infrastructure requests without provided/or with incorrect JWT token")
    @mark.parametrize("auth_header", **AuthorizationHeadersNegative().mark_params())
    @mark.parametrize("request_method,path", **EndpointsListNegative().mark_params())
    def test_call_wallet_infrastructure_with_wrong_jwt_negative(self, auth_header, request_method, path):
        self.wallet_ctrl.api_client.execute_request(request_method, path,
                                                    headers=auth_header,
                                                    expected_status_code=401)
