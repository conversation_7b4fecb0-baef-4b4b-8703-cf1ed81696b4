from allure import story, description
from pytest import mark

from core.services.polity_vault.dtos.output.wallet_creation_request_dto import WalletCreationRequestDTO
from core.enums.provisioning_status import WalletProvisioningStatus
from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.dtos.input.wallet_input import WalletInput
from tests.functional_tests.wallet_infrastructure.vault_integration.conftest import VaultIntegrationRunner


@story("Create wallet")
class TestCreateWallet(VaultIntegrationRunner):

    @description("Create DFNS/Safeheron wallet and check request is created on polity vault service")
    @mark.parametrize("wallet_type", list(WalletType))
    def test_create_wallet_polity_vault_integration(self, jwt_dto, wallet_type):
        input_body = WalletInput.random(wallet_type=wallet_type)
        self.wallet_ctrl.create_wallet(jwt_dto.encode_jwt(), input_body)

        vault_creation_requests_list = self.creation_request_ctrl.get_creation_requests_list(jwt_dto.encode_jwt())
        self.polity_vault_assert.assert_creation_request(vault_creation_requests_list,
                                                         expected_request_body=WalletCreationRequestDTO(
                                                             user_uuid=jwt_dto.uuid,
                                                             user_name=jwt_dto.username,
                                                             user_role=jwt_dto.role,
                                                             status=WalletProvisioningStatus.PENDING.value,
                                                             type=input_body.type,
                                                             request_uuid=input_body.request_id))
