from allure import feature, description, link
from pytest import mark

from core.utils.access_token_cases_utils import AuthorizationHeadersNegative
from core.utils.allure_report.allure_utils import jira_link
from tests.functional_tests.user_infrustructure.auth.negative_cases import EndpointsListNegative
from tests.functional_tests.user_infrustructure.conftest import UserInfrastructureRunner


@feature("Authentication negative scenarios")
class TestCallEndpointsAuthenticationNegative(UserInfrastructureRunner):

    @mark.xfail
    @link(**jira_link("PLT-313"))
    @description("Polity vault requests without provided/or with incorrect JWT token")
    @mark.parametrize("auth_header", **AuthorizationHeadersNegative().mark_params())
    @mark.parametrize("request_method,endpoint,api_params", **EndpointsListNegative().mark_params())
    def test_call_account_controller_with_wrong_jwt_negative(self, auth_header, request_method, endpoint, api_params):
        self.user_infrastructure_ctrl.api_client \
            .execute_request(request_method, endpoint.path(), headers=auth_header, expected_status_code=401,
                             **api_params)
