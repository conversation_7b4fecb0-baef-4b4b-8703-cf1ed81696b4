from allure import story

from core.services.user_infrustructure.dtos.input.send_notification import SendNotificationInput
from tests.functional_tests.user_infrustructure.notification.conftest import NotificationRunner


@story("Send notifications")
class TestSendNotification(NotificationRunner):

    def test_send_notification(self, adviser_jwt, parametrized_notification_type):
        n_input = SendNotificationInput.random(notificationType=parametrized_notification_type,
                                               uuid=adviser_jwt.uuid)
        response = self.user_infrastructure_ctrl.send_notification(input_body=n_input)

        self.send_notif_assert.assert_send_notification(request_body=n_input, response_body=response)
