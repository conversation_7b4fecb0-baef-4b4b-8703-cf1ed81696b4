from random import choice

from allure import story, description

from core.services.user_infrustructure.dtos.input.notifications_params import NotificationsParams
from tests.functional_tests.user_infrustructure.notification.conftest import NotificationRunner


@story("Notifications list")
class TestReadNotification(NotificationRunner):

    @description("Read notification")
    def test_read_notification(self, notifications_list, adviser_jwt):
        notification_to_read = choice(notifications_list)

        response = self.user_infrastructure_ctrl.read_notification(adviser_jwt.encode_jwt(), [notification_to_read.id])
        unread_notifications = self.user_infrastructure_ctrl.get_notifications_list(adviser_jwt.encode_jwt(),
                                                                                    NotificationsParams(isRead=False))

        self.send_notif_assert.assert_notifications_is_read(read_notification_response=response,
                                                            notification_to_read=notification_to_read,
                                                            unread_notifications=unread_notifications)

    @description("Read multiply notifications")
    def test_read_multiply_notification(self, notifications_list, adviser_jwt):
        notification_to_read = notifications_list[:2]

        self.user_infrastructure_ctrl.read_notification(adviser_jwt.encode_jwt(), list(map(lambda n: n.id, notification_to_read)))
        unread_notifications = self.user_infrastructure_ctrl.get_notifications_list(adviser_jwt.encode_jwt(),
                                                                                    NotificationsParams(isRead=False))

        self.send_notif_assert.assert_notifications_list_does_not_contain_dto(unread_notifications,
                                                                              *notification_to_read)
