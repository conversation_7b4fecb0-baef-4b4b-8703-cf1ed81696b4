from allure import story, description
from pytest import fixture

from core.services.user_infrustructure.dtos.input.notifications_params import NotificationsParams
from core.utils.helpers.dict_utils import DictUtils
from tests.functional_tests.user_infrustructure.notification.conftest import NotificationRunner


@story("Notifications list")
class TestNotificationList(NotificationRunner):

    @fixture(autouse=True)
    def send_get_notifications_first_time(self, adviser_jwt):
        """ Send request GET notifications to check that response won't be modified """
        self.user_infrastructure_ctrl.get_notifications_list(adviser_jwt.encode_jwt(), NotificationsParams(isRead=False))

    @description("Get unread notifications list. Assert response")
    def test_notification_unread_list(self, notifications_list, parametrized_notification_dto, adviser_jwt):
        response = self.user_infrastructure_ctrl.get_notifications_list(adviser_jwt.encode_jwt(),
                                                                        NotificationsParams(isRead=False))

        self.send_notif_assert.assert_notifications_list_contains_dto(response, parametrized_notification_dto, is_read=False)
        self.send_notif_assert.assert_all_notifications_read_unread(response, are_notifications_read=False,
                                                                    user_id=adviser_jwt.uuid)
