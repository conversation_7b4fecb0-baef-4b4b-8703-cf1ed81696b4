from allure import feature, description
from pytest import mark

from core.utils.access_token_cases_utils import AuthorizationHeadersNegative
from tests.functional_tests.auth_gateway.auth.negative_cases import EndpointsListNegative
from tests.functional_tests.auth_gateway.conftest import AuthGatewayRunner


@feature("Authentication negative scenarios")
class TestCallEndpointsAuthenticationNegative(AuthGatewayRunner):

    @description("AuthGateway requests without provided/or with incorrect JWT token")
    @mark.parametrize("auth_header", **AuthorizationHeadersNegative().mark_params())
    @mark.parametrize("request_method,path", **EndpointsListNegative().mark_params())
    def test_call_update_statistics_with_wrong_jwt_negative(self, auth_header, request_method, path):
        self.wallet_statistics_ctrl.api_client.execute_request(request_method, path.value, headers=auth_header,
                                                               expected_status_code=401)
