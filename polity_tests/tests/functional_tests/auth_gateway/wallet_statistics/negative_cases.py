from core.services.auth_gateway.dtos.input.update_statistics import UpdateStatisticsInput
from core.utils.parametrized_cases_utils import ParametrizedCasesBase
from tests.functional_tests.conftest import faker


class UpdateStatisticNegativeCases(ParametrizedCasesBase):

    _data = [
        (UpdateStatisticsInput.random().update(**{'walletType': faker.pystr()}), 'wallet_type is invalid'),
        (UpdateStatisticsInput.random().update(**{'walletType': faker.pybool()}), 'wallet_type is bool'),
        (UpdateStatisticsInput.random().update(**{'walletType': faker.pyint()}), 'wallet_type is int'),
        (UpdateStatisticsInput.random().update(**{'walletType': None}), 'wallet_type is None'),
        (UpdateStatisticsInput.random().delete_property('walletType'), 'wallet_type is doesn\'t exist'),

        (UpdateStatisticsInput.random().update(**{'count': faker.pystr()}), 'count is string'),
        (UpdateStatisticsInput.random().update(**{'count': faker.pybool()}), 'count is bool'),
        (UpdateStatisticsInput.random().update(**{'count': 0}), 'count is 0'),
        (UpdateStatisticsInput.random().update(**{'count': faker.pyint(min_value=-10, max_value=-1)}), 'count is negative'),
        (UpdateStatisticsInput.random().update(**{'count': None}), 'count is None'),
        (UpdateStatisticsInput.random().delete_property('count'), 'count is doesn\'t exist'),
    ]

    def _get_cases(self):
        return [k[0] for k in self._data]

    def _get_names(self):
        return [k[1] for k in self._data]