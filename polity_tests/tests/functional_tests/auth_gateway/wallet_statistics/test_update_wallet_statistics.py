from pytest import mark
from allure import story, description
from core.enums.wallet_type import WalletType
from core.utils.generators.wallet_utils import WalletGeneratorUtils

from tests.functional_tests.auth_gateway.wallet_statistics.conftest import UpdateWalletStatisticsRunner


@story("Update wallet Statistics")
class TestUpdateWalletWalletStatisticsNegative(UpdateWalletStatisticsRunner):

    @description("Update wallet statistics for DFNS/Safeheron")
    def test_update_wallet_statistics(self, existed_avatar_jwt, update_statistics_first_time,
                                      update_statistic_to_old_value):
        input_body, expected_data = update_statistics_first_time

        self.wallet_statistics_ctrl.update_statistics(jwt=existed_avatar_jwt.encode_jwt(), input_body=input_body)
        self.update_statistics_assert.update_statistic_assert(expected_data=expected_data)

    @description("Update wallet statistics after provision")
    @mark.skipif(reason='Need implementation of sequential tests')  # TODO: implement sequential
    @mark.sequential
    @mark.parametrize("wallet_type, update_count", [(WalletType.DFNS, 4), (WalletType.SAFEHERON, 1)],
                             ids=['dfns', 'safeheron'])
    def test_update_wallet_statistics_after_provision(self, jwt_dto, wallet_type, update_count):
        current_count = self.auth_gateway_db.get_wallet_statistics(wallet_type.value)[0].count
        WalletGeneratorUtils(jwt_dto, wallet_type).new_wallet().with_api_keys().with_callback()

        self.update_statistics_assert.update_statistic_assert(expected_data=
                                                              {wallet_type.value: current_count+update_count})
