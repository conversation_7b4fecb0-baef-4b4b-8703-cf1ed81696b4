from allure import link
from allure import story
from pytest import mark
from core.enums.user_roles import Roles

from core.services.auth_gateway.dtos.input.avatar_login import AvatarLoginInput
from core.utils.allure_report.allure_utils import jira_link
from tests.functional_tests.auth_gateway.registration_and_login.conftest import RegistrationLoginRunner


@story("Login avatar user negative")
class TestAvatarLogin(RegistrationLoginRunner):

    @mark.xfail
    @link(**jira_link("PLT-545"))
    def test_avatar_login_incorrect_id(self, register_avatar):
        investor_jwt, avatar_id, avatar_name = register_avatar

        response = self.auth_ctrl.avatar_login(jwt=investor_jwt, input_body=AvatarLoginInput(avatar_id+100),
                                               status_code=400)
        self.login_assert.assert_error(response=response.get('messaage'), expected_error="Route hasn't been found")

    @mark.xfail
    @link(**jira_link("PLT-545"))
    def test_avatar_login_another_investor(self, register_avatar):
        investor_jwt, avatar_id, avatar_name = register_avatar

        response = self.auth_ctrl.avatar_login(jwt=investor_jwt, input_body=AvatarLoginInput(avatar_id-2),
                                               status_code=401)

    @mark.parametrize('jwt', ['asd', None], ids=['jwt is incorrect', 'jwt is None'])
    def test_avatar_login_incorrect_jwt(self, jwt, register_avatar):
        investor_jwt, avatar_id, avatar_name = register_avatar

        response = self.auth_ctrl.avatar_login(jwt=jwt, input_body=AvatarLoginInput(avatar_id), status_code=401)

