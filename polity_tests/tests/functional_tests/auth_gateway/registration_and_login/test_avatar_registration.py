from urllib3 import encode_multipart_formdata
from pytest import mark
from allure import story, link
from urllib3 import encode_multipart_formdata

from core.services.auth_gateway.dtos.input.avatar_registration import AvatarRegistrationInput
from core.utils.allure_report.allure_utils import jira_link
from tests.functional_tests.auth_gateway.registration_and_login.conftest import RegistrationLoginRunner


@story("Registration avatar user")
class TestAvatarLogin(RegistrationLoginRunner):


    def test_avatar_registration(self, get_investor_jwt_for_avatar):

        avatar_reg_input = AvatarRegistrationInput.random()
        avatar_reg_body, avatar_header = encode_multipart_formdata(avatar_reg_input.__dict__)

        self.avatars_names.append(avatar_reg_input.name)

        response = self.auth_ctrl.avatar_registration(jwt=get_investor_jwt_for_avatar,
                                                      input_body=avatar_reg_body, content_type=avatar_header)
        self.avatar_registration_assert.assert_avatar_was_created(response=response, reg_input=avatar_reg_input)

