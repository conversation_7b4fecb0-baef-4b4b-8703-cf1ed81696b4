from allure import story
from pytest import mark

from core.enums.user_roles import Roles
from core.services.auth_gateway.dtos.input.registration import RegistrationInput
from tests.functional_tests.auth_gateway.registration_and_login.conftest import RegistrationLoginRunner


@story("Create user")
class TestCreateUser(RegistrationLoginRunner):

    # avatars must be registered by using another api
    @mark.parametrize("user_role", [role for role in list(Roles) if role != Roles.AVATAR])
    def test_create_user(self, user_role):
        input_body = RegistrationInput.random(role=user_role.value)
        self.user_name = input_body.username
        response = self.auth_ctrl.registration(input_body=input_body)

        self.registration_assert.assert_user_was_created(response=response, registration_input=input_body)
