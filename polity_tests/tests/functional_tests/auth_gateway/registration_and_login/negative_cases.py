from core.services.auth_gateway.dtos.input.registration import RegistrationInput
from core.services.auth_gateway.enums.errors_handling import Errors
from core.utils.parametrized_cases_utils import ParametrizedCasesBase


class RegistrationNegativeParams(ParametrizedCasesBase):
    _data = [
        (RegistrationInput.random().update(**{'username': None}),
         Errors.MISSING_PROPERTY.get_msg(property_name="username"),
         'username is None'),
        (RegistrationInput.random().update(**{'username': ""}),
         Errors.EMPTY_STRING.get_msg(),
         'username is empty string'),

        (RegistrationInput.random().update(**{'role': None}),
         Errors.MISSING_PROPERTY.get_msg(property_name="role"),
         'role is None'),
        (RegistrationInput.random().update(**{'role': "Bad value"}),
         Errors.BAD_ENUM_VALUE.get_msg(),
         'role is bad value'),
        (RegistrationInput.random().update(**{'role': ""}),
         Errors.EMPTY_STRING.get_msg(),
         'role is empty role'),

        (RegistrationInput.random().update(**{'password': None}),
         Errors.MISSING_PROPERTY.get_msg(property_name="password"),
         'password is None'),
        (RegistrationInput.random().update(**{'password': ""}),
         Errors.EMPTY_STRING.get_msg(),
         'password is empty string')
    ]

    def _get_cases(self):
        return [(k[0], k[1]) for k in self._data]

    def _get_names(self):
        return [k[2] for k in self._data]


class LoginNegativeParams(ParametrizedCasesBase):
    _data = [
        ({'username': None}, Errors.MISSING_PROPERTY.get_msg(property_name="username"), 'username is None'),
        ({'username': ""}, Errors.LOGIN_EMPTY_STRING.get_msg(), 'username is empty string'),
        ({'username': "asd"}, Errors.BAD_CREDENTIALS.get_msg(), 'username is random string'),

        ({'role': None}, Errors.MISSING_PROPERTY.get_msg(property_name="role"), 'role is None'),
        ({'role': "Bad value"}, Errors.BAD_ENUM_VALUE.get_msg(), 'role is bad value'),
        ({'role': ""}, Errors.EMPTY_STRING.get_msg(), 'role is empty role'),

        ({'password': None}, Errors.MISSING_PROPERTY.get_msg(property_name="password"), 'password is None'),
        ({'password': "Bad value"}, Errors.BAD_CREDENTIALS.get_msg(), 'password is bad value'),
        ({'password': ""}, Errors.BAD_CREDENTIALS.get_msg(), 'password is empty string'),

    ]

    def _get_cases(self):
        return [(k[0], k[1]) for k in self._data]

    def _get_names(self):
        return [k[2] for k in self._data]
