from allure import epic
from pytest import fixture

from core.enums.wallet_type import WalletType
from core.services.polity_vault.controllers.creation_request_ctrl import CreationRequestCtrl
from core.services.polity_vault.utils.wallet_utils import WalletUtils
from tests.functional_tests.conftest import BaseRunner


@epic("Polity Vault")
class PolityVaultRunner(BaseRunner):
    creation_request_ctrl = CreationRequestCtrl()

    @fixture(scope='class')
    def creation_request(self, jwt_dto):
        creation_request = WalletUtils(wallet_type=WalletType.random().value, jwt_dto=jwt_dto).creation_request
        yield creation_request
        self.creation_request_ctrl.delete_creation_request(jwt_dto.encode_jwt(), creation_request.id)

    @fixture
    def parametrized_creation_request(self, jwt_dto, parametrized_wallet_type):
        creation_request = WalletUtils(wallet_type=parametrized_wallet_type.value, jwt_dto=jwt_dto).creation_request
        yield creation_request
        self.creation_request_ctrl.delete_creation_request(jwt_dto.encode_jwt(), creation_request.id)
