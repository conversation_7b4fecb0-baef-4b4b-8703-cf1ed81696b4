from allure import story, description
from pytest import fixture

from core.services.polity_vault.dtos.db.wallet_creds_db_dto import WalletCredentialsDTO
from core.services.polity_vault.enums.errors_handling import Errors
from tests.functional_tests.polity_vault.wallet_info.conftest import WalletInfoRunner


@story("Get wallet info - Negative")
class TestWalletInfoNegative(WalletInfoRunner):

    @fixture(autouse=True)
    def delete_secret_from_vault(self, parametrized_creation_request, parametrized_wallet_type):
        from core.services.polity_vault.db.wallet_credentials_info_db import WalletCredentialsInfoDb
        WalletCredentialsInfoDb().insert_credentials(parametrized_wallet_type, WalletCredentialsDTO(
            credentials_path='', creation_request_id=parametrized_creation_request.id))
        yield

    @description("Get wallet list for DFNS/Safeheron when vault secrets is deleted for some request id")
    def test_wallet_credentials_list_without_secret(self, jwt_dto, parametrized_wallet_type,
                                                    parametrized_creation_request):
        err_msg = self.wallet_info_ctrl.get_wallet_credentials_list(jwt_dto.encode_jwt(), parametrized_wallet_type,
                                                                    status_code=404)
        self.wallet_info_assert.assert_errors_dict_by_key(err_msg, "detail",
                                                          Errors.SECRET_NOT_FOUND_FOR_REQUEST.get_msg(
                                                              request_id=parametrized_creation_request.request_uuid))

    @description("Get wallet credentials for DFNS/Safeheron when vault secrets is deleted")
    def test_wallet_info_without_secret(self, parametrized_wallet_type, parametrized_creation_request):
        err_msg = self.wallet_info_ctrl.get_wallet_credentials_by_id(parametrized_creation_request.request_uuid,
                                                                     parametrized_wallet_type, status_code=404)
        self.wallet_info_assert.assert_errors_dict_by_key(err_msg, "detail",
                                                          Errors.SECRET_NOT_FOUND_FOR_REQUEST.get_msg(
                                                              request_id=parametrized_creation_request.request_uuid))
