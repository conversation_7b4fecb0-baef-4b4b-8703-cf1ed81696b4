from allure import story, description
from pytest import fixture

from core.services.polity_vault.assertions.wallet_assert_interface import IWalletAssert
from core.services.polity_vault.utils.wallet_utils import WalletUtils
from tests.functional_tests.polity_vault.wallet_info.conftest import Wall<PERSON><PERSON>n<PERSON><PERSON>unner
from tests.functional_tests.polity_vault.wallet_info.positive_cases import WalletTypes


@story("Get wallet info")
class TestWalletInfo(WalletInfoRunner):
    wallet_assert: IWalletAssert

    @fixture(**WalletTypes().fixture_params())
    def wallet_info(self, jwt_dto, request) -> WalletUtils:
        self.wallet_type, self.wallet_assert = request.param[0], request.param[1]()

        wallet_utils = WalletUtils(self.wallet_type.value, jwt_dto)
        wallet_utils.generate_wallet_info()
        yield wallet_utils
        self.creation_request_ctrl.delete_creation_request(jwt_dto.encode_jwt(), wallet_utils.creation_request.id)

    @description("Get wallet credentials for DFNS/Safeheron")
    def test_wallets_list(self, jwt_dto, wallet_info):
        res = self.wallet_info_ctrl.get_wallet_credentials_list(jwt_dto.encode_jwt(), self.wallet_type)
        self.wallet_assert.assert_wallet_credentials_contains_only(res, expected=wallet_info.get_wallet_info())

    @description("Get wallet credentials for DFNS/Safeheron by request id without jwt token")
    def test_wallet_info(self, wallet_info):
        res = self.wallet_info_ctrl.get_wallet_credentials_by_id(wallet_info.creation_request.request_uuid,
                                                                 self.wallet_type)
        self.wallet_assert.assert_wallet_credentials_equals_to(res, expected=wallet_info.get_wallet_info())
