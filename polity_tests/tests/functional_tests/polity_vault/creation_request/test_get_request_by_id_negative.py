import uuid

from allure import story, description

from tests.functional_tests.polity_vault.creation_request.conftest import CreationRequestRunner


@story("Get request by ID Negative")
class TestGetRequestByIdNegative(CreationRequestRunner):

    @description("Get creation request by id")
    def test_get_creation_request_by_id_negative(self, jwt_dto):
        self.creation_request_ctrl.get_creation_requests_by_id(jwt=jwt_dto.encode_jwt(),
                                                               request_id=str(uuid.uuid4()), status_code=404)
