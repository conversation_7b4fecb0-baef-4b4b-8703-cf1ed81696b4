from core.enums.wallet_type import WalletType
from core.utils.parametrized_cases_utils import ParametrizedCasesBase
from tests.functional_tests.conftest import faker


class CreationRequestParams(ParametrizedCasesBase):
    _data = [
        # incorrect type
        ({"type": None}, "type property not provided"),
        ({"type": f"{WalletType.DFNS}S"}, "type property is not from enum"),
        ({"type": faker.pyint()}, "type property is int"),
        ({"type": faker.pydict(allowed_types=["str"])}, "type property is dict"),

        # incorrect request_uuid
        ({"request_uuid": None}, "request_uuid property not provided"),
        ({"request_uuid": faker.pystr()}, "request_uuid property is uuid"),
        ({"request_uuid": faker.pydict(allowed_types=["str"])}, "request_uuid property is dict"),
    ]
