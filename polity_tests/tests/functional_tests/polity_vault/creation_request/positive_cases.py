import uuid

from core.enums.provisioning_status import WalletProvisioningStatus
from core.enums.wallet_type import WalletType
from core.utils.parametrized_cases_utils import ParametrizedCasesBase
from tests.functional_tests.conftest import faker


class CreationRequestUpdateParams(ParametrizedCasesBase):
    _data = [
        ({"status": WalletProvisioningStatus.PROVISIONED.value}, "Change status to success"),
        ({"status": WalletProvisioningStatus.FAILED.value}, "Change status to failed"),
        ({"status": WalletProvisioningStatus.IN_PROGRESS.value}, "Change status to in_progress"),
        ({"user_name": f"{faker.name()}_updated", "user_role": faker.pystr(), "request_uuid": str(uuid.uuid4())},
         "Change username,role,request_uuid"),
        ({"type": WalletType.SAFEHERON.value}, "Change type"),
        ({"user_uuid": str(uuid.uuid4())}, "With new user uuid"),
    ]
