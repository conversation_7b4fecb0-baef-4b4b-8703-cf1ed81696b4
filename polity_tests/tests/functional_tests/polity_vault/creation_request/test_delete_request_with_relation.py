from allure import story, description
from pytest import fixture

from core.enums.wallet_type import WalletType
from core.services.polity_vault.utils.wallet_utils import WalletUtils
from tests.functional_tests.polity_vault.creation_request.conftest import CreationRequestRunner


@story("Delete creation request")
class TestDeleteRequest(CreationRequestRunner):

    @fixture
    def dfns_creation_request(self, jwt_dto):
        yield WalletUtils(wallet_type=WalletType.DFNS.value, jwt_dto=jwt_dto).creation_request

    @description("Delete creation request with different jwt")
    def test_delete_creation_request_with_relation(self, jwt_dto, link_wallet_info, dfns_creation_request):
        self.creation_request_ctrl.delete_creation_request(jwt_dto.encode_jwt(), dfns_creation_request.id)
        # TODO clarify exp result and add assertions
