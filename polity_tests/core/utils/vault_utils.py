import hvac

from core.utils.config_manager import settings


class VaultUtils:
    """
    self.client = hvac.Client(url=settings.VAULT_URL, token=settings.VAULT_TOKEN)
    """
    def __init__(self):
        self.__vault_url = settings.VAULT_URL
        self.__role_id = settings.VAULT_ROLE_ID
        self.__secret_id = settings.VAULT_SECRET_ID
        self.__namespace = settings.VAULT_NAMESPACE
        self.client = None

    def __enter__(self):
        self.client = hvac.Client(url=self.__vault_url, namespace=self.__namespace)
        self.client.auth._approle.login(role_id=self.__role_id, secret_id=self.__secret_id)
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.client.logout(revoke_token=True)

    def get_secret(self, secret_path: str) -> dict:
        return self.client.secrets.kv.v2.read_secret_version(path=secret_path)["data"]["data"]

    def set_secret(self, secret_path: str, secret_data: dict) -> None:
        self.client.secrets.kv.v2.create_or_update_secret(path=secret_path, secret=secret_data)
