import uuid
from typing import TypeVar

from marshmallow import Schema, fields, post_load

from core.services.polity_vault.dtos import faker
from core.utils.config_manager import settings
from core.utils.helpers.datetime_utils import DateTimeUtils
from core.utils.helpers.dict_utils import DictUtils

_T = TypeVar("_T")


class JWTSchema(Schema):
    username = fields.Str()
    role = fields.Str()
    iat = fields.Int()
    exp = fields.Int()
    uuid = fields.Str()
    sub = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return JWT(**data)


class JWT:

    def __init__(self, username, role, iat, exp, uuid, sub, **kwargs):
        self.sub = sub
        self.username = username
        self.role = role
        self.iat = iat
        self.exp = exp
        self.uuid = uuid

    def encode_jwt(self):
        from jwt import encode
        return encode(self.serialize(), settings.JWT_SIGNATURE, algorithm="HS512")

    @classmethod
    def decode_jwt(cls: _T, encoded_jwt: str) -> _T:
        jwt_dict = DictUtils.jwt_to_dict(encoded_jwt)
        return JWTSchema().load(jwt_dict)

    @classmethod
    def random(cls, role=None):
        return JWT(
            sub="73", username=faker.name(),
            role=role or "ADVISER",
            iat=DateTimeUtils.utc_now_move_n_time(hours=-1).timestamp(),
            exp=DateTimeUtils.utc_now_move_n_time(days=1).timestamp(),
            uuid=str(uuid.uuid4()))

    def serialize(self) -> dict:
        return JWTSchema().dump(self)

    def __repr__(self):
        return JWTSchema().dumps(self)

    def with_uuid(self, uuid):
        self.uuid = uuid
        return self

    def with_deleted_uuid(self):
        self.__dict__.pop("uuid")
        return self

    def with_expiration_date_now_move_n_time(self, **time_kwargs):
        self.exp = DateTimeUtils.utc_now_move_n_time(**time_kwargs).timestamp()
        return self


class JWTEncode(JWT):

    def __init__(self, access_token: str):
        self.access_token = access_token
        super().__init__(**DictUtils.jwt_to_dict(self.access_token))

    def encode_jwt(self):
        return self.access_token
