import logging

import requests
from allure import step, attach, attachment_type
from assertpy import assert_that
from requests.adapters import HTTPAdapter
from urllib3 import Retry

from core.utils.rest.curlify_response import CurlifyResponse
from core.utils.rest.rest_methods import RestMethods


class RestImplementation:
    logger = logging.getLogger(__name__)

    def __init__(self, base_address):
        self.base_address = base_address

    @step("{method} request to: {path}")
    def execute_request(self, method: RestMethods, path="/", expected_status_code=None, **kwargs):
        # Prepare base url
        url = f"{self.base_address}/{path}"
        # Get requests session
        session = self.__get_http_session()
        # Execute a call
        self.logger.info(f'{method.name} request to: {url}')
        response = getattr(session, method.value)(url=url, **kwargs)
        return self.processing_response(response, expected_status_code, **kwargs)

    def processing_response(self, response, expected_status_code, **kwargs):
        self._attach_request_call_to_report(response, **kwargs)
        self._attach_response_to_report(response)
        self._assert_status_code(expected_status_code, response)
        return response

    def __get_http_session(self):
        session = requests.Session()
        retry = Retry(connect=3, backoff_factor=0.5)
        adapter = HTTPAdapter(max_retries=retry)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        return session

    def _assert_status_code(self, expected_status_code, response):
        with step(f'Status code: {response.status_code}, Response time: {response.elapsed}.'):
            if expected_status_code:
                assert_that(response.status_code) \
                    .described_as(f"[{response.request.method} {response.request.path_url}] Status code") \
                    .is_equal_to(expected_status_code)

    def _attach_request_call_to_report(self, response, **kwargs):
        curl_info = CurlifyResponse.to_curl(response.request, kwargs.get("json") or kwargs.get("data"))
        self.logger.debug(curl_info)
        attach(body=curl_info, name="Request")

    def _attach_response_to_report(self, response):
        self.logger.debug(f'Status code is: {response.status_code}')
        self.logger.debug(f'Response body: {response.text}')
        attach(response.text, "Response body", attachment_type=attachment_type.JSON)
        attach(str(response.headers), "Response headers", attachment_type=attachment_type.JSON)
