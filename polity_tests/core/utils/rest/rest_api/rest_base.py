from core.utils.rest.irest_utils import IRest
from core.utils.rest.rest_api import RestImplementation
from core.utils.rest.rest_methods import RestMethods


class ApiClient(IRest, RestImplementation):

    def __init__(self, base_address):
        super(ApiClient, self).__init__(base_address)

    def get(self, path="/", expected_status_code=None, **kwargs):
        return super().execute_request(method=RestMethods.GET, path=path, expected_status_code=expected_status_code,
                                       **kwargs)

    def post(self, path="/", expected_status_code=None, **kwargs):
        return super().execute_request(method=RestMethods.POST, path=path, expected_status_code=expected_status_code,
                                       **kwargs)

    def put(self, path="/", expected_status_code=None, **kwargs):
        return super().execute_request(method=RestMethods.PUT, path=path, expected_status_code=expected_status_code,
                                       **kwargs)

    def patch(self, path="/", expected_status_code=None, **kwargs):
        return super().execute_request(method=RestMethods.PATCH, path=path, expected_status_code=expected_status_code,
                                       **kwargs)

    def delete(self, path="/", expected_status_code=None, **kwargs):
        return super().execute_request(method=RestMethods.DELETE, path=path, expected_status_code=expected_status_code,
                                       **kwargs)
