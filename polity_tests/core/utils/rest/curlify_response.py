import json

from shlex import quote


class CurlifyResponse:
    __excluded_curl_headers = ("Content-Length", "Accept-Encoding")

    @classmethod
    def to_curl(cls, request, body=None):
        parts = [('curl', None), ('-X', request.method)]

        for k, v in sorted(request.headers.items()):
            if k not in cls.__excluded_curl_headers:
                parts += [('-H', f'{k}: {v}')]

        if body:
            try:
                parts += [('-d', json.dumps(body, indent=4))]
            except TypeError:
                parts += [(None, str(body))]

        parts += [(None, str(request.url))]

        flat_parts = []
        for k, v in parts:
            if k:
                flat_parts.append(quote(k))
            if v:
                flat_parts.append(quote(v))

        return ' '.join(flat_parts)
