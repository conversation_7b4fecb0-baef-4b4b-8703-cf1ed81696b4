from enum import Enum

from requests import JSONDecodeError


class ApiUtils:

    def authorization_headers(self, access_token: str):
        return {'Authorization': f"Bearer {access_token}"} if access_token else {}

    def _deserialize_response(self, response, schema=None, error_schema=None):
        if response.status_code >= 400:
            return self.__load_error_dto(response, error_schema) if error_schema else self.__extract_response(response)
        else:
            return schema.load(response.json()) if schema else self.__extract_response(response)

    def __extract_response(self, response):
        try:
            return response.json() if self.__is_body_json(response) else response.text
        except JSONDecodeError:
            return response.text

    def __load_error_dto(self, response, error_schema):
        return error_schema.load(response.json())

    def __is_body_json(self, response):
        return bool(response.text) and response.headers.get('Content-Type') == 'application/json'


class EndpointsUtils(Enum):

    def path(self, **kwargs):
        res = f'{self._prefix_path}/{self.value}'
        for k, v in kwargs.items():
            res = res.replace('{%s}' % k, str(v))
        return res

    @property
    def _prefix_path(self):
        return "api/v1"

    def __str__(self):
        return self.value

    def __repr__(self):
        return self.value
