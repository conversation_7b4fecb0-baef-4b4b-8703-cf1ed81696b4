from allure import step
from assertpy import soft_assertions, assert_that
from marshmallow import EXCLUDE

from core.services.auth_gateway.db.auth_gateway_db import AuthGatewayDb
from core.services.auth_gateway.dtos.db.custome_route_table import CustomerRouteSchema
from core.services.auth_gateway.dtos.input.registration import RegistrationInput
from core.services.auth_gateway.dtos.output.registration import RegistrationOutputDTO
from core.services.auth_gateway.utils.redirect_url_utils import get_redirect_url_by_role
from core.utils.assert_utils import BaseAssert


class RegistrationAssert(BaseAssert):

    auth_gateway_db = AuthGatewayDb()

    @step("Assert user was created")
    def assert_user_was_created(self, response: RegistrationOutputDTO, registration_input: RegistrationInput):

        with soft_assertions():
            assert_that(response.serialize()).is_equal_to({'message': "User registered successfully"})
            self.assert_customer_route_db_row(registration_input=registration_input)

    @step("Assert DB: user was created")
    def assert_customer_route_db_row(self, registration_input: RegistrationInput):

        users = self.auth_gateway_db.get_user_by_name(registration_input.username)
        if len(users) == 1:
            db_row = CustomerRouteSchema().load(users[0], unknown=EXCLUDE)
            expected_route = get_redirect_url_by_role(registration_input.role)

            assert_that(db_row.uuid).described_as("DB row").is_not_none()
            assert_that(db_row.role).described_as("DB row").is_equal_to(registration_input.role)
            assert_that(db_row.username).described_as("DB row").is_equal_to(registration_input.username)
            assert_that(db_row.route).described_as("DB row").is_equal_to(expected_route)
        else:
            assert_that(users).described_as(
                f"DB has more than 1 user with the same name {registration_input.username}") \
                .is_length(1)
