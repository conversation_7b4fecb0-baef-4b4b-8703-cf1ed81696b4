from allure import step
from assertpy import soft_assertions, assert_that
from marshmallow import EXCLUDE

from core.services.auth_gateway.db.auth_gateway_db import AuthGatewayDb
from core.services.auth_gateway.dtos.db.custome_route_table import CustomerRouteSchema
from core.services.auth_gateway.dtos.input.avatar_registration import AvatarRegistrationInput
from core.services.auth_gateway.dtos.output.avatar_registration import AvatarRegistrationOutputDTO
from core.services.auth_gateway.enums.route_by_role import RouteByRole
from core.enums.user_roles import Roles
from core.utils.assert_utils import BaseAssert


class AvatarRegistrationAssert(BaseAssert):

    auth_gateway_db = AuthGatewayDb()

    @step("Assert avatar was created")
    def assert_avatar_was_created(self, response: AvatarRegistrationOutputDTO, reg_input: AvatarRegistrationInput):

        with soft_assertions():
            self.assert_avatar_response(response=response, reg_input=reg_input)
            self.assert_customer_route_db_row(response=response, reg_input=reg_input)

    @step("Assert response: user was created")
    def assert_avatar_response(self, response: AvatarRegistrationOutputDTO, reg_input: AvatarRegistrationInput):

        assert_that(response.route).described_as("response").is_equal_to(RouteByRole.AVATAR.value)

        assert_that(response.role).described_as("response").is_equal_to(Roles.AVATAR.value)
        assert_that(response.username).described_as("response").is_equal_to(reg_input.name)
        assert_that(response.hasAcquiredNode).described_as("response").is_false()
        assert_that(response.element_id).described_as("response").is_equal_to(reg_input.element_id)

    @step("Assert DB: user was created")
    def assert_customer_route_db_row(self, response: AvatarRegistrationOutputDTO, reg_input: AvatarRegistrationInput):

        users = self.auth_gateway_db.get_user_by_name(reg_input.name)
        if len(users) == 1:
            db_row = CustomerRouteSchema().load(users[0], unknown=EXCLUDE)

            assert_that(db_row.uuid).described_as("DB row").is_not_none()
            assert_that(db_row.role).described_as("DB row").is_equal_to(Roles.AVATAR.value)
            assert_that(db_row.username).described_as("DB row").is_equal_to(reg_input.name)
            assert_that(db_row.route).described_as("DB row").is_equal_to(RouteByRole.AVATAR.value)
            assert_that(db_row.profile_picture).described_as("DB row").is_equal_to(response.photo)
            assert_that(db_row.element_id).described_as("DB row").is_equal_to(reg_input.element_id)
        else:
            assert_that(users).described_as(
                f"DB has more than 1 user with the same name {reg_input.name}") \
                .is_length(1)
