from datetime import timezone

from allure import step
from assertpy import soft_assertions, assert_that

from core.services.auth_gateway.db.auth_gateway_db import AuthGatewayDb
from core.services.auth_gateway.dtos.db.refresh_token_table import RefreshTokenSchema
from core.services.auth_gateway.dtos.input.login import LoginInput
from core.services.auth_gateway.dtos.output.login import LoginOutputDTO
from core.services.auth_gateway.utils.redirect_url_utils import get_redirect_url_by_role
from core.utils.assert_utils import BaseAssert
from core.utils.helpers.datetime_utils import DateTimeUtils


class LoginAssert(BaseAssert):

    auth_gateway_db = AuthGatewayDb()

    @step("Assert user was logged")
    def assert_user_was_logged(self, response: LoginOutputDTO, login_input: LoginInput = None,
                               user_name: str = None, role: str = None):

        role = login_input.role if login_input else role
        user_name = login_input.username if login_input else user_name

        expected_redirect_url = get_redirect_url_by_role(role)
        with soft_assertions():
            assert_that(response.access_token).described_as("Response access token").is_not_empty()
            assert_that(response.refresh_token).described_as("Response refresh token").is_not_empty()
            assert_that(response.redirect_url).described_as("Response redirect url").is_equal_to(expected_redirect_url)

            self.assert_refresh_token_db_row(user_name=user_name, response=response)

    @step("Assert DB: user was logged")
    def assert_refresh_token_db_row(self, user_name: str, response: LoginOutputDTO):
        token_row = self.auth_gateway_db.get_refresh_token_row_by_user_name(user_name)
        if len(token_row) == 1:
            db_row = RefreshTokenSchema().load(token_row[0])
            expiry_date = db_row.expiry_date.replace(tzinfo=timezone.utc)

            assert_that(db_row.token).described_as("DB row").is_equal_to(response.refresh_token)
            DateTimeUtils.compare_dates(actual_date=expiry_date,
                                        expected_date=DateTimeUtils.utc_now_move_n_time(hours=24),
                                        description_prefix="Db row", margin_of_error_seconds=60)
        else:
            assert_that(token_row).described_as(
                f"DB has {len(token_row)} rows in refresh_token table for {user_name}, but should be only one.") \
                .is_length(1)
