from core.utils.dto_utils import InputBase
from core.enums.wallet_type import WalletType
from random import randint
import json


class UpdateStatisticsInput(InputBase):

    def __init__(self, walletType: str, count: int):
        self.walletType = walletType
        self.count = count

    @staticmethod
    def random(walletType=None, count=None):
        return UpdateStatisticsInput(
            walletType=walletType.value.upper() if walletType else WalletType.random().value.upper(),
            count=count or randint(1, 5))

    def __repr__(self):
        return json.dumps(self.serialize())
