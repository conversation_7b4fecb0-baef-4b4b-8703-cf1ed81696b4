from datetime import datetime

from core.utils.dto_utils import InputBase
from core.enums.user_roles import Roles
import json


class RegistrationInput(InputBase):

    def __init__(self, username: str, role: str, password: str):
        self.username = username
        self.role = role
        self.password = password

    @staticmethod
    def random(role=None):
        from faker import Faker
        faker = Faker()
        return RegistrationInput(username=f"{faker.first_name()}_{datetime.now().timestamp()}",
                                 role=role or Roles.ADVISER.value,
                                 password=faker.pystr())

    def __repr__(self):
        return json.dumps(self.serialize())
