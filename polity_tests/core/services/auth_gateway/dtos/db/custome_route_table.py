from marshmallow import Schema, fields, post_load, validate
from core.enums.user_roles import Roles


class CustomerRouteSchema(Schema):
    uuid = fields.Str()
    role = fields.Str(validate=validate.OneOf([k.value for k in list(Roles)]))
    route = fields.Str(allow_none=True)
    profile_picture = fields.Str(allow_none=True)
    element_id = fields.Str(allow_none=True)
    username = fields.Str()
    customer_id = fields.Int()

    @post_load
    def deserialize(self, data, **kwargs):
        return CustomerRouteDTO(**data)


class CustomerRouteDTO:

    def __init__(self, uuid, role, route, username, customer_id, profile_picture, element_id):
        self.uuid = uuid
        self.role = role
        self.route = route
        self.username = username
        self.customer_id = customer_id
        self.profile_picture = profile_picture
        self.element_id = element_id

    def serialize(self):
        return CustomerRouteSchema().dump(self)

    def __repr__(self):
        return CustomerRouteSchema().dumps(self)
