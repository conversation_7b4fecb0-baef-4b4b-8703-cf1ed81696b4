import logging

from allure import step

from core.services.user_infrustructure.api_base import ApiBase
from core.services.user_infrustructure.controllers import UserInfrastructureEndpoints
from core.services.user_infrustructure.dtos.input.notifications_params import NotificationsParams
from core.services.user_infrustructure.dtos.input.send_notification import SendNotificationInput
from core.services.user_infrustructure.dtos.output.notificatiion_dto import NotificationSchema, \
    NotificationDTO
from core.services.user_infrustructure.utils.notification_type_utils import get_output_schema_by_notification_type


class UserInfrastructureCtrl(ApiBase):

    @step("Send notification")
    def send_notification(self, input_body: SendNotificationInput, status_code=200):
        response = self.api_client.post(path=UserInfrastructureEndpoints.SEND_NOTIFICATION.path(),
                                        expected_status_code=status_code,
                                        json=input_body.serialize())
        schema = get_output_schema_by_notification_type(input_body.notificationType)
        return self._deserialize_response(response, schema())

    @step("Get notifications")
    def get_notifications_list(self, token, params: NotificationsParams, status_code=200) -> list[NotificationDTO]:
        response = self.api_client.get(path=UserInfrastructureEndpoints.NOTIFICATIONS.path(),
                                       expected_status_code=status_code,
                                       params=params.serialize(), headers=self.authorization_headers(token))
        return self._deserialize_response(response, NotificationSchema(many=True))

    @step("Read notification")
    def read_notification(self, token, ids=list[int], status_code=200) -> list[NotificationDTO]:
        response = self.api_client.post(path=UserInfrastructureEndpoints.NOTIFICATIONS.path(),
                                        expected_status_code=status_code,
                                        json=ids, headers=self.authorization_headers(token))
        return self._deserialize_response(response, NotificationSchema(many=True))
