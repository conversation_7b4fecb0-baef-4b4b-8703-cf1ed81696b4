from allure import step

from core.services.user_infrustructure.db.db_base import DBBase


class KeyExchangeDB(DBBase):

    def __init__(self):
        super().__init__()
        self.table = "public.key_exchange"

    @step("Get key exchange record for customer: {customer_id}")
    def get_record_by_user_id(self, customer_id: str) -> dict:
        query = f"select * from {self.table} where customer_id = '{customer_id}'"
        result = self._execute_query_to_dict(query)
        return result[0] if result else None

    @step("Delete key exchange records for customer: {customer_id}")
    def delete_records_by_customer_id(self, customer_id: int) -> None:
        query = f"DELETE from {self.table} where customer_id = '{customer_id}' RETURNING 1;"
        self._execute(query)
