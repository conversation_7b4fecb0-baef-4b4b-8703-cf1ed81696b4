import uuid

from marshmallow import Schema, fields, post_load

from core.enums.provisioning_status import WalletProvisioningStatus
from core.services.user_infrustructure.dtos.output.notificatiion_dto import notification_schema
from core.utils.dto_utils import InputBase


@notification_schema
class WalletNotificationSchema(Schema):
    status = fields.Str()
    request_uuid = fields.Str()
    wallet_name = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return WalletNotificationDTO(**data)


class WalletNotificationDTO(InputBase):

    def __init__(self, status, request_uuid, wallet_name):
        self.request_uuid = request_uuid
        self.status = status
        self.wallet_name = wallet_name

    @classmethod
    def random(cls, **kwargs):
        return WalletNotificationDTO(
            request_uuid=str(uuid.uuid4()),
            status=WalletProvisioningStatus.PROVISIONED.value,
            wallet_name='aqa_test_wallet').update(**kwargs)
