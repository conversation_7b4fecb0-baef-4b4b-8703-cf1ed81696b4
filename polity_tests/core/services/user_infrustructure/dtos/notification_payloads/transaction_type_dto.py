from core.services.user_infrustructure.dtos.output.notificatiion_dto import notification_schema
from core.utils.dto_utils import InputBase
from core.utils.dto_utils import faker
from marshmallow import Schema, fields, post_load
import uuid


@notification_schema
class TransactionNotificationSchema(Schema):
    declinedBy = fields.Str()
    asset = fields.Str()
    amount = fields.Str()
    transactionId = fields.Str()
    destinationWalletAddress = fields.Str()
    status = fields.Str()
    merchant = fields.Str()
    sourceAccount = fields.Str()
    destinationAccount = fields.Str()
    declineReason = fields.Str()
    sourceWalletAddress = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return TransactionNotificationDTO(**data)


class TransactionNotificationDTO(InputBase):

    def __init__(self, asset, amount, destinationWalletAddress, status, merchant,sourceWalletAddress,
                 destinationAccount, transactionId="", declinedBy="", sourceAccount="", declineReason=""):
        self.sourceWalletAddress = sourceWalletAddress
        self.declinedBy = declinedBy
        self.asset = asset
        self.amount = amount
        self.transactionId = transactionId
        self.destinationWalletAddress = destinationWalletAddress
        self.status = status
        self.merchant = merchant
        self.sourceAccount = sourceAccount
        self.destinationAccount = destinationAccount
        self.declineReason = declineReason

    @staticmethod
    def random(**kwargs):
        return TransactionNotificationDTO(
            declinedBy=faker.first_name(),
            asset=f"USDC",
            amount=f"{faker.pyint(min_value=5, max_value=10)}",
            transactionId=str(uuid.uuid4()),
            destinationWalletAddress=str(uuid.uuid4()),
            status='Status_var',
            merchant=faker.first_name(),
            sourceAccount=str(uuid.uuid4()),
            sourceWalletAddress=str(uuid.uuid4()),
            destinationAccount=str(uuid.uuid4()),
            declineReason='declineReason_var',
        ).update(**kwargs)
