from marshmallow import Schema, fields, post_load


class KeyExchangeSchema(Schema):
    customerId = fields.Str()
    pubKey = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return KeyExchangeDTO(**data)


class KeyExchangeDTO:

    def __init__(self, customerId, pubKey):
        self.customerId = customerId
        self.pubKey = pubKey
    
    def __repr__(self):
        return KeyExchangeSchema().dumps(self)
