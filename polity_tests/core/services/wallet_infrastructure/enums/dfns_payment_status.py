from enum import Enum


class DFNSPaymentStatus(Enum):
    APPROVED = "Approved"  # Payment is approved to be executed, it will soon be broadcasted
    CONFIRMED = "Confirmed"  # Payment is considered Confirmed when it's part of a given block and contains block information
    EXECUTED = "Executed"  # Payment is executed and fed to a blockchain node
    INITIATED = "Initiated"  # Payment just got created, policies are being executed
    REJECTED = "Rejected"  # Payment was rejected by policy engine

    def __repr__(self):
        return self.value

    def __str__(self):
        return self.value
