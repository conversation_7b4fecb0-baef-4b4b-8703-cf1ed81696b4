from marshmallow import Schema, fields, post_load


class PaymentLastTransactionSchema(Schema):
    date = fields.DateTime()
    request_id = fields.Str()
    uuid = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return PaymentLastTransactionDTO(**data)


class PaymentLastTransactionDTO:

    def __init__(self, date, request_id, uuid):
        self.date = date
        self.request_id = request_id
        self.uuid = uuid

    def serialize(self):
        return PaymentLastTransactionSchema().dump(self)

    def __repr__(self):
        return PaymentLastTransactionSchema().dumps(self, default=str)
