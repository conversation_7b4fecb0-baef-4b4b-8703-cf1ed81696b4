from marshmallow import Schema, fields, post_load


class ErrorSchema(Schema):
    message = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return ErrorDTO(**data)


class ErrorDTO:

    def __init__(self, message):
        self.message = message

    def serialize(self):
        return ErrorSchema().dump(self)

    def __repr__(self):
        return ErrorSchema().dumps(self)
