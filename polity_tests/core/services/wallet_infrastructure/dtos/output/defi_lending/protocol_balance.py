from dataclasses import dataclass
from typing import Optional

from marshmallow import Schema, fields, post_load

from core.services.wallet_infrastructure.dtos.output.defi_lending.position import PositionSchema


class ProtocolBalanceSchema(Schema):
    total_borrowed = fields.Float(required=False)
    total_net_worth = fields.Float(required=False)
    total_rewarded = fields.Float(required=False)
    total_supplied = fields.Float(required=False)
    slug = fields.Str()
    name = fields.Str()
    positions = fields.List(fields.Nested(PositionSchema, partial=True))

    @post_load
    def deserialize(self, data, **kwargs):
        return ProtocolBalanceDTO(**data)


@dataclass
class ProtocolBalanceDTO:

    name: str
    slug: str
    positions: list
    total_borrowed: Optional[float] = 0
    total_rewarded: Optional[float] = 0
    total_supplied: Optional[float] = 0
    total_net_worth: Optional[float] = 0

    def serialize(self):
        return ProtocolBalanceSchema().dump(self)

    def __repr__(self):
        return ProtocolBalanceSchema().dumps(self)
