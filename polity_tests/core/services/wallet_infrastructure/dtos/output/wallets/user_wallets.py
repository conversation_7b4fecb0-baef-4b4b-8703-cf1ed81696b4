from marshmallow import Schema, fields, post_load


class UserWalletsSchema(Schema):
    request_id = fields.Str()
    wallet_name = fields.Str()
    status = fields.Str()
    created_at = fields.Raw()

    @post_load
    def deserialize(self, data, **kwargs):
        return UserWalletsDTO(**data)


class UserWalletsDTO:

    def __init__(self, request_id, wallet_name, status, created_at):
        self.request_id = request_id
        self.wallet_name = wallet_name
        self.status = status
        self.created_at = created_at

    def serialize(self):
        return UserWalletsSchema().dump(self)

    def __repr__(self):
        return UserWalletsSchema().dumps(self, default=str)
