from marshmallow import Schema, fields, post_load
from core.services.wallet_infrastructure.dtos.output.defi_products.defi_product import DeFiProductSchema


class DeFiProductsSchema(Schema):
    defi_products = fields.List(fields.Nested(DeFiProductSchema))

    @post_load
    def deserialize(self, data, **kwargs):
        return DeFiProductDTO(**data)


class DeFiProductDTO:

    def __init__(self, defi_products):
        self.defi_products = defi_products

    def serialize(self):
        return DeFiProductSchema().dump(self)

    def __repr__(self):
        return DeFiProductSchema().dumps(self)
