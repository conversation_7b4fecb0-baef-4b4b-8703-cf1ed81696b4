from marshmallow import Schema, fields, post_load


class DeFiAssetSchema(Schema):
    name = fields.Str()
    display_name = fields.Str()
    asset_symbol = fields.Str()
    address = fields.Str()
    chain_id = fields.Str()
    icon = fields.URL()
    price = fields.Float()
    balance = fields.Float()
    balance_usd = fields.Float()

    @post_load
    def deserialize(self, data, **kwargs):
        return DeFiAssetDTO(**data)


class DeFiAssetDTO:

    def __init__(self, name, display_name, asset_symbol, address, chain_id, icon, price, balance, balance_usd):
        self.name = name
        self.display_name = display_name
        self.asset_symbol = asset_symbol
        self.address = address
        self.chain_id = chain_id
        self.icon = icon
        self.price = price
        self.balance = balance
        self.balance_usd = balance_usd

    def serialize(self):
        return DeFiAssetSchema().dump(self)

    def __repr__(self):
        return DeFiAssetSchema().dumps(self)
