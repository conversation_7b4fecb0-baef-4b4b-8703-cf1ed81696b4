from marshmallow import Schema, fields, post_load


class AssetAddressSchema(Schema):
    asset_address = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return AssetAddressDTO(**data)


class AssetAddressDTO:

    def __init__(self, asset_address):
        self.asset_address = asset_address

    def serialize(self):
        return AssetAddressSchema().dump(self)

    def __repr__(self):
        return AssetAddressSchema().dumps(self)
