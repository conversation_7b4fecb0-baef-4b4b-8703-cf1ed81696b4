from marshmallow import Schema, fields, post_load


class AssetSchema(Schema):
    asset_symbol = fields.Str()
    balance = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return AssetDTO(**data)


class AssetDTO:

    def __init__(self, asset_symbol, balance):
        self.balance = balance
        self.asset_symbol = asset_symbol

    def serialize(self):
        return AssetSchema().dump(self)

    def __repr__(self):
        return AssetSchema().dumps(self)
