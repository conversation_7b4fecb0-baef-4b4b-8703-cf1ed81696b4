import random
import uuid

from core.enums.asset_types import AssetTypes
from core.utils.dto_utils import InputBase


class DeleteFavoriteAddressInput(InputBase):

    def __init__(self, address, asset_symbol):
        self.address = address
        self.asset_symbol = asset_symbol

    @classmethod
    def random(cls, address: str = None, asset_symbol: str = None):
        return cls(address=address or str(uuid.uuid4()),
                   asset_symbol=asset_symbol or random.choice(list(AssetTypes)).value)

