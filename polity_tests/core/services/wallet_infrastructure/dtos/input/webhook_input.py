import uuid
from typing import Union

from core.services.wallet_infrastructure.dtos.input.webhook_input_data import WebhookDfnsDataInput, \
    WebhookSafeheronDataInput
from core.utils.dto_utils import InputBase


class WebhookInput(InputBase):

    def __init__(self, request_uuid: uuid, status: str,
                 data: Union[WebhookDfnsDataInput, WebhookSafeheronDataInput, dict]):
        self.request_uuid = request_uuid
        self.status = status
        self.data = data

    def with_data(self, data):
        self.data = data
        return self
