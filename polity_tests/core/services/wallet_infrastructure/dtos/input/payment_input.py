from marshmallow import Schema, fields

from core.utils.dto_utils import InputBase


class PaymentInput(InputBase):
    class PaymentInputSchema(Schema):
        from_wallet = fields.Str(data_key="from", allow_none=True)
        to = fields.Str(allow_none=True)
        asset = fields.Str(allow_none=True)
        value = fields.Raw(allow_none=True)

    def __init__(self, from_wallet: str, to: str, asset: str, value: str):
        self.from_wallet = from_wallet
        self.to = to
        self.asset = asset
        self.value = value

    def serialize(self):
        return PaymentInput.PaymentInputSchema().dump(self)
