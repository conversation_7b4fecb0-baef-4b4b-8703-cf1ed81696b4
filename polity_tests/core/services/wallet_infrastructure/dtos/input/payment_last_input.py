from core.enums.wallet_type import WalletType
from core.utils.dto_utils import InputBase, faker


class PaymentLastInput(InputBase):

    def __init__(self, wallet_type, users_uuid):
        self.wallet_type = wallet_type
        self.users = self.users_list(users_uuid)

    @staticmethod
    def users_list(users_uuid):
        return [{"uuid": user_uuid} for user_uuid in users_uuid]

    @classmethod
    def random(cls, wallet_type: WalletType = WalletType.random(), users_uuid: list[str] = None):
        return cls(wallet_type=wallet_type.value, users_uuid=users_uuid if users_uuid else [faker.pystr()])
