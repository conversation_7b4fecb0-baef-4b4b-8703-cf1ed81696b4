import uuid
import random
from core.utils.dto_utils import InputBase, faker
from core.enums.asset_types import AssetTypes


class FavoriteAddressInput(InputBase):

    def __init__(self, address, asset_symbol, name):
        self.address = address
        self.asset_symbol = asset_symbol
        self.name = name

    @classmethod
    def random(cls, address: str = None, asset_symbol: str = None, name: str = None):
        return cls(address=address or str(uuid.uuid4()),
                   asset_symbol=asset_symbol or random.choice(list(AssetTypes)).value,
                   name=name or str(faker.name()))

    def update(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)
        return self