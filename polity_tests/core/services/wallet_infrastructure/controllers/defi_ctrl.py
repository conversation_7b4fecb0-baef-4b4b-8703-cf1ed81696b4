from allure import step

from core.services.wallet_infrastructure.api_base import ApiBase
from core.services.wallet_infrastructure.controllers import BWIEndpoints
from core.services.wallet_infrastructure.dtos.output.defi_products.defi_products import DeFiProductsSchema
from core.services.wallet_infrastructure.dtos.output.defi_products.defi_asset import DeFiAssetSchema
from core.services.wallet_infrastructure.dtos.output.defi_lending.defi_lending import DeFi<PERSON>endingSchema, DeFiLendingDTO



class DEFICtrl(ApiBase):

    @step("Get products")
    def get_defi_products(self, jwt, status_code=200):
        response = self.api_client.get(path=BWIEndpoints.DEFI_PRODUCTS.path(),
                                       expected_status_code=status_code,
                                       headers=self.authorization_headers(jwt))
        return self._deserialize_response(response, schema=DeFiProductsSchema())

    @step("Get DeFi assets")
    def get_defi_assets(self, jwt, request_id, status_code=200):
        response = self.api_client.get(path=BWIEndpoints.DEFI_ASSETS.path(request_id=request_id),
                                       expected_status_code=status_code,
                                       headers=self.authorization_headers(jwt))
        return self._deserialize_response(response, schema=DeFiAssetSchema(many=True))

    @step("Get DeFi lending")
    def get_defi_lending(self, jwt, request_id, status_code=200) -> DeFiLendingDTO:
        response = self.api_client.get(path=BWIEndpoints.DEFI_LENDING.path(request_id=request_id),
                                       expected_status_code=status_code,
                                       headers=self.authorization_headers(jwt))
        return self._deserialize_response(response, schema=DeFiLendingSchema())
