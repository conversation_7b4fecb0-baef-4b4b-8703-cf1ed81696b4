from core.utils.rest.api_utils import EndpointsUtils


class BWIEndpoints(EndpointsUtils):
    PAYMENT = "payment/{mpc_type}/"
    PAYMENT_DFNS_NOTIFICATION = "payment/dfns/notification/"
    WALLET = "wallet/"
    WALLET_ADDRESS = "wallet/{mpc_type}/{request_id}/{asset_symbol}/address/"
    WALLET_ASSET = "wallet/{mpc_type}/asset/"
    WALLET_ASSETS = "wallet/{mpc_type}/{request_id}/assets/"
    WALLET_DETAILS = "wallet/{request_id}/"
    WALLET_WEBHOOK = "wallet/webhook/"
    WALLET_FAVORITE_ADDRESS = "favourites/address/"
    DEFI_PRODUCTS = "defi/products/"
    DEFI_ASSETS = "defi/{request_id}/assets/"
    DEFI_LENDING = "defi/{request_id}/lending/"
    TRANSACTION_HISTORY = "wallet/{request_id}/{asset_symbol}/transactions"
    USER_WALLETS = "wallets/{mpc_type}/"
    PAYMENT_LAST = "payment/last/"
