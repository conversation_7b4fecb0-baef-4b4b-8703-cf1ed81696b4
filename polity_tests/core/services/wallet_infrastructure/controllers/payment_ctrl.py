from allure import step

from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.api_base import ApiBase
from core.services.wallet_infrastructure.controllers import BWIEndpoints
from core.services.wallet_infrastructure.dtos.input.payment_input import PaymentInput
from core.services.wallet_infrastructure.dtos.input.payment_last_input import PaymentLastInput
from core.services.wallet_infrastructure.dtos.output.paymetns.payment_last import (PaymentLastDTO,
                                                                                   PaymentLastTransactionSchema)
from core.services.wallet_infrastructure.dtos.input.payment_notification.payment_notification_input import \
    PaymentNotificationInput
from core.services.wallet_infrastructure.dtos.output.error_dto import ErrorSchema


class PaymentCtrl(ApiBase):

    @step("Make {wallet_type} payment")
    def make_payment(self, jwt, wallet_type: WalletType, input_body: PaymentInput, status_code=200):
        response = self.api_client.post(path=BWIEndpoints.PAYMENT.path(mpc_type=wallet_type.value),
                                        expected_status_code=status_code, json=input_body.serialize(),
                                        headers=self.authorization_headers(jwt))

        return self._deserialize_response(response, error_schema=ErrorSchema())

    @step("Send DFNS notification")
    def send_dfns_payment_notification(self, input_body: PaymentNotificationInput, status_code=200):
        response = self.api_client.post(path=BWIEndpoints.PAYMENT_DFNS_NOTIFICATION.path(),
                                        expected_status_code=status_code, json=input_body.serialize())

        return self._deserialize_response(response)

    @step("Get last payments")
    def get_last_users_payments(self, input_body: PaymentLastInput, status_code=200) -> PaymentLastDTO:
        response = self.api_client.get(path=BWIEndpoints.PAYMENT_LAST.path(), json=input_body.serialize(),
                                       expected_status_code=status_code)
        return self._deserialize_response(response, schema=PaymentLastTransactionSchema())
