from assertpy import assert_that, soft_assertions

from core.services.wallet_infrastructure.db.wallets_db import WalletsDb
from core.services.wallet_infrastructure.dtos.db.favorites_address import FavoritesAddressDTO
from core.services.wallet_infrastructure.dtos.input.favorite_address_input import FavoriteAddressInput
from core.services.wallet_infrastructure.dtos.output.favorites_addresses.favorites_addresses import \
    FavoritesAddressesDTO
from core.services.wallet_infrastructure.enums.errors_handling import Errors
from core.services.wallet_infrastructure.utils.avatar_default_favorite_addresses import avatars_addresses
from core.utils.assert_utils import BaseAssert
from core.utils.helpers.datetime_utils import DateTimeUtils


class FavoriteAddressAssert(BaseAssert):
    wallet_db = WalletsDb()

    def assert_error(self, response, expected_error: str):
        assert_that(response.message).described_as("Error message").is_equal_to(expected_error)

    def assert_get_favorite_assert(self, user_id: str, response: FavoritesAddressesDTO, is_avatar=False):
        with soft_assertions():
            if is_avatar:
                assert_that(response.static_addresses) \
                    .described_as("static_addresses") \
                    .is_equal_to(avatars_addresses)
            else:
                assert_that(response.static_addresses).described_as("static_addresses").is_none()

            self.assert_favorite_address(user_id=user_id, address_list=response.dynamic_addresses)

    @staticmethod
    def assert_duplicated_values(actual_error):
        with soft_assertions():
            assert_that(actual_error.message).\
                described_as('validation error').\
                contains(Errors.DUPLICATED_VALUES.get_msg())

    def assert_favorite_address(self, user_id: str, address_list: list[FavoriteAddressInput]):
        db_wallets = self.wallet_db.get_favorite_address_by_user_id(user_id=user_id)
        with soft_assertions():
            if address_list:
                self.assert_favorite_address_list(actual_favorites=address_list, db_favorites=db_wallets)
            else:
                self.assert_db_user_has_no_active_favorites(user_id=user_id)

    @classmethod
    def assert_favorite_address_list(cls, actual_favorites: list[FavoriteAddressInput],
                                     db_favorites: list[FavoritesAddressDTO]):

        for f_address in actual_favorites:
            db_favorite_address = list(filter(lambda n:
                                              n.address == f_address.address and
                                              n.asset_symbol == f_address.asset_symbol, db_favorites))

            assert_that(len(db_favorite_address)).is_equal_to(1)
            assert_that(f_address.name).described_as("name").is_equal_to(db_favorite_address[0].name)
            cls.assert_favorite_db_row(db_row=db_favorite_address[0])

    def assert_db_user_has_no_active_favorites(self, user_id: str):
        all_favorites = self.wallet_db.get_favorite_address_by_user_id(user_id=user_id)
        filtered_favorites = list(filter(lambda n: n.deleted_at is None, all_favorites))

        assert_that(filtered_favorites).described_as('DB data').is_empty()

    @classmethod
    def assert_favorite_db_row(cls, db_row):
        DateTimeUtils().compare_datetime_with_now(actual_date=db_row.created_at, description_prefix='DB created_at',
                                                  margin_of_error_seconds=10)
