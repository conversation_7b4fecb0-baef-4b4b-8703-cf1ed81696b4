from abc import ABC, abstractmethod

from allure import step

from core.services.wallet_infrastructure.db.wallets_db import WalletsDb
from core.services.wallet_infrastructure.dtos.input.wallet_input import WalletInput
from core.services.wallet_infrastructure.dtos.output.assets.asset_dto import AssetDTO
from core.services.wallet_infrastructure.dtos.output.wallets.wallet_details import WalletDetailsDTO
from core.services.wallet_infrastructure.enums.asset_types import WalletsMap
from core.utils.assert_utils import BaseAssert


class WebhookAssert(BaseAssert, ABC):
    wallet_db = WalletsDb()

    @abstractmethod
    def assert_successful_callback(self, request_id, actual_assets: list[AssetDTO],
                                   actual_wallet_details: WalletDetailsDTO, wallet_input: WalletInput):
        pass

    @step("Assert GET /assets response")
    def _assert_assets_response(self, actual_assets: list[AssetDTO], wallets_map: WalletsMap):
        from core.services.wallet_infrastructure.assertions.assets_assert import AssetsAssert
        AssetsAssert.assert_assets_list_contains_only(actual_assets, *wallets_map.values)
