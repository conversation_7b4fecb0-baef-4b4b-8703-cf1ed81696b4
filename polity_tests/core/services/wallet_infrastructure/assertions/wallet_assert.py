from allure import step
from assertpy import soft_assertions, assert_that
from datetime import datetime, timezone

from core.enums.provisioning_status import WalletProvisioningStatus
from core.services.wallet_infrastructure.dtos.input.wallet_input import WalletInput
from core.services.wallet_infrastructure.dtos.output.wallets.wallet_details import WalletDetailsDTO
from core.services.wallet_infrastructure.dtos.output.wallets.user_wallets import UserWalletsDTO
from core.utils.assert_utils import BaseAssert
from core.utils.helpers.datetime_utils import DateTimeUtils
from core.services.wallet_infrastructure.db.wallets_db import WalletsDb


class WalletAssert(BaseAssert):
    wallets_db = WalletsDb()

    @step("Assert error msg is equal to: {expected_error}")
    def assert_error(self, response, expected_error: str):
        assert_that(response.message).described_as("Error message").is_equal_to(expected_error)

    def assert_wallet_created(self, actual_wallet_details: WalletDetailsDTO, wallet_input: WalletInput, user_id: str):
        with soft_assertions():
            self.assert_wallet_details(actual_wallet_details, wallet_input, WalletProvisioningStatus.PENDING)
            self._assert_new_wallet_db_record(wallet_input, user_id)

    def assert_user_wallets(self, expected: list[WalletInput], actual: list[UserWalletsDTO]):
        with soft_assertions():

            expected_wallets = [k for k in expected if k.is_archived is False]
            assert_that(len(actual)).described_as('Length').is_equal_to(len(expected_wallets))

            for expected_wallet in expected_wallets:
                actual_wallet = [k for k in actual if k.request_id == expected_wallet.request_id][0]

                assert_that(actual_wallet.status).described_as('type').is_equal_to(expected_wallet.status)
                assert_that(actual_wallet.wallet_name)\
                    .described_as('wallet name')\
                    .is_equal_to(expected_wallet.wallet_name)
                DateTimeUtils.compare_datetime_with_now(
                    datetime.strptime(actual_wallet.created_at, "%Y-%m-%dT%H:%M:%S.%f%z"),
                    description_prefix="created_at",
                    margin_of_error_seconds=30)


    @classmethod
    def assert_wallet_details(cls, actual_wallet_details: WalletDetailsDTO, wallet_input: WalletInput,
                              expected_status: WalletProvisioningStatus):
        err_prefix = "wallet details:"
        (assert_that(actual_wallet_details.request_id).described_as(f"{err_prefix} request_id")
         .is_equal_to(wallet_input.request_id))
        assert_that(actual_wallet_details.type).described_as(f"{err_prefix} type").is_equal_to(wallet_input.type)
        assert_that(actual_wallet_details.address).described_as(f"{err_prefix} address") \
            .is_equal_to(wallet_input.address)
        assert_that(actual_wallet_details.wallet_name).described_as(f"{err_prefix} wallet_name") \
            .is_equal_to(wallet_input.wallet_name)
        assert_that(actual_wallet_details.status).described_as(f"{err_prefix} status") \
            .is_equal_to(expected_status.value)
        DateTimeUtils.compare_datetime_with_now(actual_wallet_details.created_at,
                                                description_prefix=f"{err_prefix} created_at",
                                                margin_of_error_seconds=30)
        assert_that(DateTimeUtils.compare_dates(actual_date=actual_wallet_details.archived_at,
                                                expected_date=datetime(1, 1, 1, tzinfo=timezone.utc),
                                                margin_of_error_seconds=0,
                                                description_prefix=f"{err_prefix} archived_at"))

    @step("Assert wallet DB record is created correctly")
    def _assert_new_wallet_db_record(self, create_wallet_body: WalletInput, user_id):
        actual_db_obj = self.wallets_db.get_wallet(create_wallet_body.request_id)
        with soft_assertions():
            assert_that(actual_db_obj.get("user_id")).described_as("user_id").is_equal_to(user_id)
            assert_that(actual_db_obj.get("type")).described_as("type").is_equal_to(create_wallet_body.type)
            assert_that(actual_db_obj.get('referencing_address')).described_as("address") \
                .is_equal_to(create_wallet_body.address)
            assert_that(actual_db_obj.get('wallet_name')).described_as("wallet_name") \
                .is_equal_to(create_wallet_body.wallet_name)
            assert_that(actual_db_obj.get('status')).described_as("status") \
                .is_equal_to(WalletProvisioningStatus.PENDING.value)
            DateTimeUtils.compare_datetime_with_now(actual_db_obj.get('created_at'), "created_at", 10)

    @step("Assert wallet DB record is archived")
    def assert_wallet_archived(self, request_id: str):
        wallet_db = self.wallets_db.get_wallet(request_id=request_id)

        with soft_assertions():
            DateTimeUtils().compare_datetime_with_now(actual_date=wallet_db.get("archived_at"),
                                                      description_prefix='DB archive_time',
                                                      margin_of_error_seconds=30)
