from datetime import datetime

import pytz
from allure import step
from assertpy import soft_assertions, assert_that

from core.enums.provisioning_status import WalletProvisioningStatus
from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.assertions.wallet_assert import WalletAssert
from core.services.wallet_infrastructure.assertions.webhook_assert import <PERSON>hookAssert
from core.services.wallet_infrastructure.dtos.input.wallet_input import WalletInput
from core.services.wallet_infrastructure.dtos.output.assets.asset_dto import AssetDTO
from core.services.wallet_infrastructure.dtos.output.wallets.wallet_details import WalletDetailsDTO
from core.services.wallet_infrastructure.enums.asset_types import WalletsMap


class WebhookSafeheronAssert(WebhookAssert):

    def assert_successful_callback(self, request_id, actual_assets: list[AssetDTO],
                                   actual_wallet_details: WalletDetailsDTO, wallet_input: WalletInput):
        with soft_assertions():
            self.__assert_safeheron_db_assets(request_id)
            self._assert_assets_response(actual_assets, WalletsMap.SAFEHERON)
            WalletAssert.assert_wallet_details(actual_wallet_details, wallet_input,
                                               WalletProvisioningStatus.PROVISIONED)

    @step("Assert assets DB records are created correctly")
    def __assert_safeheron_db_assets(self, request_id):
        error_prefix = "DB record:"
        actual_db_list = self.wallet_db.get_asset_accounts_by_request_uuid(WalletType.SAFEHERON, request_id)
        assets = sorted([wallet['asset_symbol'] for wallet in actual_db_list])

        assert_that(assets).described_as(f"{error_prefix} assets symbols") \
            .is_equal_to(sorted(WalletsMap.SAFEHERON.values))

        for wallet in actual_db_list:
            assert_that(wallet["safeheron_wallet"]).described_as(f"{error_prefix} safeheron_wallet").starts_with(
                'account')
            assert_that(wallet["archived_at"]).described_as(f"{error_prefix} archived_at") \
                .is_equal_to(datetime.min.replace(tzinfo=pytz.UTC))
