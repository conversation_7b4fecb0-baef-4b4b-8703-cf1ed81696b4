import logging

import polling2

from core.enums.wallet_type import WalletType
from core.services.wallet_infrastructure.controllers.wallet_ctrl import WalletCtrl
from core.services.wallet_infrastructure.dtos.output.assets.asset_address import AssetAddressDTO
from core.services.wallet_infrastructure.dtos.output.assets.asset_list_dto import AssetListDTO


class WaitAssetAddressUtil:
    logger = logging.getLogger(__name__)
    wallet_ctrl = WalletCtrl()

    @classmethod
    def wait_until_address_created_for_assets_list(cls, jwt, request_id, wallet_type: WalletType, *asset_symbols):
        for asset_symbol in asset_symbols:
            cls.wait_until_asset_address_created(jwt, request_id, wallet_type, asset_symbol)

    @classmethod
    def wait_until_asset_address_created(cls, jwt, request_id, wallet_type: WalletType, asset_symbol) \
            -> list[AssetAddressDTO]:
        cls.logger.info(f"Wait until {wallet_type} address is created for the asset: {asset_symbol}")

        def is_asset_address_generated(response):
            return all(len(asset.asset_address) > 0 for asset in response)

        try:
            return polling2.poll(
                lambda: cls.wallet_ctrl.get_asset_address(jwt.encode_jwt(), request_id, wallet_type,
                                                          asset_symbol),
                check_success=is_asset_address_generated,
                step=1,
                timeout=15)
        except polling2.TimeoutException:
            cls.logger.error(f"{wallet_type} address is not created for the asset: {asset_symbol}")
            return cls.wallet_ctrl.get_asset_address(jwt.encode_jwt(), request_id, wallet_type, asset_symbol)

    @classmethod
    def wait_assets_list(cls, jwt, request_id, wallet_type: WalletType, *asset_symbols) -> AssetListDTO:
        def are_all_assets_created(response):
            return len(response.assets) == len(asset_symbols)

        try:
            return polling2.poll(
                lambda: cls.wallet_ctrl.get_assets_list(jwt.encode_jwt(), request_id, wallet_type),
                check_success=are_all_assets_created,
                step=1,
                timeout=15)
        except polling2.TimeoutException:
            cls.logger.error(f"Not all {len(asset_symbols)} {wallet_type} assets are created")
            return cls.wallet_ctrl.get_assets_list(jwt.encode_jwt(), request_id, wallet_type)
