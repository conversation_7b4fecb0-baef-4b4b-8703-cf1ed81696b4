import uuid

from marshmallow import Schema, fields, post_load, INCLUDE


class WalletCredentialsSchema(Schema):
    credentials_path = fields.Str()
    creation_request_id = fields.Str()

    class Meta:
        unknown = INCLUDE
        ordered = True

    @post_load
    def deserialize(self, data, **kwargs):
        return WalletCredentialsDTO(**data)


class WalletCredentialsDTO:

    def __init__(self, credentials_path, creation_request_id: int, **kwargs):
        self.credentials_path = credentials_path
        self.creation_request_id = creation_request_id

    @classmethod
    def random(cls, creation_request_id: int):
        return WalletCredentialsDTO(credentials_path=str(uuid.uuid4()), creation_request_id=creation_request_id)

    def __repr__(self):
        return WalletCredentialsSchema().dumps(self)

    def serialize(self) -> dict:
        return WalletCredentialsSchema().dump(self)
