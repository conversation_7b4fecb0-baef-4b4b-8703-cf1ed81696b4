from marshmallow import fields, post_load

from core.services.polity_vault.dtos.output.wallet_creation_request_dto import WalletCreationRequestSchema
from core.services.polity_vault.dtos.output.wallet_credentials_info.wallet_info_dto import WalletCredentialsBaseSchema, WalletCredentialsBaseDTO


class WalletDFNSCredentialsSchema(WalletCredentialsBaseSchema):
    jwt_token = fields.Str()
    creation_request = fields.Nested(WalletCreationRequestSchema)

    @post_load
    def deserialize(self, data, **kwargs):
        return WalletDFNSCredentialsDTO(**data)


class WalletDFNSCredentialsDTO(WalletCredentialsBaseDTO):

    def __init__(self, jwt_token, creation_request, id):
        super().__init__(creation_request, id)
        self.jwt_token = jwt_token
        self.creation_request = creation_request

    def __repr__(self):
        return WalletDFNSCredentialsSchema().dumps(self)
