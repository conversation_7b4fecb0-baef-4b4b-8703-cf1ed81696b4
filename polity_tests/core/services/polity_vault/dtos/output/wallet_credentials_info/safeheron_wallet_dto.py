from marshmallow import fields, post_load

from core.services.polity_vault.dtos.output.wallet_creation_request_dto import WalletCreationRequestSchema
from core.services.polity_vault.dtos.output.wallet_credentials_info.wallet_info_dto import WalletCredentialsBaseSchema, \
    WalletCredentialsBaseDTO


class WalletSafeheronCredentialsSchema(WalletCredentialsBaseSchema):
    public_key = fields.Str()
    private_key = fields.Str()
    api_key = fields.Str()
    platform_public_key = fields.Str()
    notif_platform_pub_key = fields.Str()
    creation_request = fields.Nested(WalletCreationRequestSchema)

    @post_load
    def deserialize(self, data, **kwargs):
        return WalletSafeheronCredentialsDTO(**data)


class WalletSafeheronCredentialsDTO(WalletCredentialsBaseDTO):

    def __init__(self, public_key, private_key, api_key, creation_request, id, platform_public_key,
                 notif_platform_pub_key):
        super().__init__(creation_request, id)
        self.notif_platform_pub_key = notif_platform_pub_key
        self.platform_public_key = platform_public_key
        self.public_key = public_key
        self.private_key = private_key
        self.api_key = api_key
        self.creation_request = creation_request

    def __repr__(self):
        return WalletSafeheronCredentialsSchema().dumps(self)
