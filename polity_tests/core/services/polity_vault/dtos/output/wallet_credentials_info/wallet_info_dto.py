from marshmallow import Schema, fields, post_load

from core.services.polity_vault.dtos.output.wallet_creation_request_dto import WalletCreationRequestSchema


class WalletCredentialsBaseSchema(Schema):
    id = fields.Int()
    creation_request = fields.Nested(WalletCreationRequestSchema)

    @post_load
    def deserialize(self, data, **kwargs):
        return WalletCredentialsBaseDTO(**data)


class WalletCredentialsBaseDTO:

    def __init__(self, creation_request, id):
        self.id = id
        self.creation_request = creation_request

    def __repr__(self):
        return WalletCredentialsBaseSchema().dumps(self)
