from marshmallow import Schema, fields, post_load


class WalletCreationRequestSchema(Schema):
    user_uuid = fields.Str()
    user_name = fields.Str()
    user_role = fields.Str()
    status = fields.Str()
    id = fields.Int()
    type = fields.Str()
    request_uuid = fields.Str()

    @post_load
    def deserialize(self, data, **kwargs):
        return WalletCreationRequestDTO(**data)


class WalletCreationRequestDTO:

    def __init__(self, user_uuid, user_name, user_role, status, type, request_uuid, id=None):
        self.request_uuid = request_uuid
        self.type = type
        self.id = id
        self.user_uuid = user_uuid
        self.user_name = user_name
        self.user_role = user_role
        self.status = status

    def serialize(self):
        return WalletCreationRequestSchema().dump(self)

    def __repr__(self):
        return WalletCreationRequestSchema().dumps(self)
