from core.utils.dto_utils import InputBase


class WalletCreationRequestUpdateInput(InputBase):

    def __init__(self, user_uuid, user_name, user_role, status, type, request_uuid, **kwargs):
        self.request_uuid = request_uuid
        self.type = type
        self.user_uuid = user_uuid
        self.user_name = user_name
        self.user_role = user_role
        self.status = status

    def with_user_uuid(self, user_uuid):
        self.user_uuid = user_uuid
        return self
