from core.services.polity_vault.dtos import faker
from core.utils.dto_utils import InputBase


class SafeheronCredentialsDTO(InputBase):

    def __init__(self, public_key, private_key, api_key, platform_public_key, notif_platform_pub_key):
        self.notif_platform_pub_key = notif_platform_pub_key
        self.platform_public_key = platform_public_key
        self.public_key = public_key
        self.private_key = private_key
        self.api_key = api_key

    @classmethod
    def random(cls):
        return SafeheronCredentialsDTO(faker.pystr(), faker.pystr(), faker.pystr(), faker.pystr(), faker.pystr())
