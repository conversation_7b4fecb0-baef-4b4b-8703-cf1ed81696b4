from allure import step

from core.services.polity_vault.api_base import ApiBase
from core.services.polity_vault.controllers import PolityVaultEndpoints
from core.services.polity_vault.dtos.output.wallet_credentials_info.dfns_wallet_dto import WalletDFNSCredentialsSchema, \
    WalletDFNSCredentialsDTO
from core.services.polity_vault.dtos.output.wallet_credentials_info.safeheron_wallet_dto import \
    WalletSafeheronCredentialsSchema, WalletSafeheronCredentialsDTO
from core.enums.wallet_type import WalletType


class WalletInfoCtrl(ApiBase):

    @step("Get {wallet_type} wallet credentials")
    def get_wallet_credentials_list(self, jwt, wallet_type: WalletType, status_code=200) -> \
            list[WalletSafeheronCredentialsDTO] | list[WalletDFNSCredentialsDTO]:
        response = self.api_client.get(path=PolityVaultEndpoints.WALLET_CREDENTIALS.path(mpc_type=wallet_type.value),
                                       expected_status_code=status_code, headers=self.authorization_headers(jwt))

        schema = WalletDFNSCredentialsSchema if wallet_type == WalletType.DFNS else WalletSafeheronCredentialsSchema
        return self._deserialize_response(response, schema=schema(many=True))

    @step("Get {wallet_type} wallet credentials for request id: {request_id}")
    def get_wallet_credentials_by_id(self, request_id, wallet_type: WalletType, status_code=200) -> \
            WalletSafeheronCredentialsDTO | WalletDFNSCredentialsDTO:
        response = self.api_client.get(path=PolityVaultEndpoints.WALLET_CREDENTIALS_ID.path(mpc_type=wallet_type.value,
                                                                                            request_id=request_id),
                                       expected_status_code=status_code)

        schema = WalletDFNSCredentialsSchema if wallet_type == WalletType.DFNS else WalletSafeheronCredentialsSchema
        return self._deserialize_response(response, schema=schema())
