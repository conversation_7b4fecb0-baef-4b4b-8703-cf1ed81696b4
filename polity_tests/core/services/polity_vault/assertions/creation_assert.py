from typing import List

from allure import step
from assertpy import soft_assertions, assert_that, soft_fail

from core.services.polity_vault.dtos.input.wallet_creation_request_body import WalletCreationRequestInput
from core.services.polity_vault.dtos.output.wallet_creation_request_dto import WalletCreationRequestD<PERSON>
from core.utils.helpers.polity_jwt import JWT
from core.enums.provisioning_status import WalletProvisioningStatus
from core.utils.assert_utils import BaseAssert


class CreationAssert(BaseAssert):

    @step("Assert creation requests list contains only pending requests")
    def assert_creation_request_pending(self, creation_requests: List[WalletCreationRequestDTO], jwt_dto: JWT,
                                        *create_request_bodies: WalletCreationRequestInput):
        with soft_assertions():
            assert_that(creation_requests).is_length(len(create_request_bodies))
            for create_request_body in create_request_bodies:
                self.assert_creation_request(creation_requests,
                                             expected_request_body=WalletCreationRequestDTO(
                                                 user_uuid=jwt_dto.uuid,
                                                 user_name=jwt_dto.username,
                                                 user_role=jwt_dto.role,
                                                 status=WalletProvisioningStatus.PENDING.value,
                                                 type=create_request_body.type,
                                                 request_uuid=create_request_body.request_uuid))

    @step("Assert creation request details")
    def assert_creation_request(self, actual_creation_requests, expected_request_body):
        actual_creation_request = list(
            filter(lambda r: r.request_uuid == expected_request_body.request_uuid, actual_creation_requests))
        self.__assert_creation_request_details(actual_creation_request, expected_request_body)

    def __assert_creation_request_details(self, actual_creation_request: List[WalletCreationRequestDTO],
                                          expected_creation_request: WalletCreationRequestDTO):
        if len(actual_creation_request) > 0:
            actual_creation_request = actual_creation_request[0]
            err_prefix = f"[Request id={actual_creation_request.id}]:"
            assert_that(actual_creation_request.status).described_as(f"{err_prefix} status") \
                .is_equal_to(expected_creation_request.status)
            assert_that(actual_creation_request.user_uuid).described_as(f"{err_prefix} user_uuid") \
                .is_equal_to(expected_creation_request.user_uuid)
            assert_that(actual_creation_request.user_name).described_as(f"{err_prefix} user_name") \
                .is_equal_to(expected_creation_request.user_name)
            assert_that(actual_creation_request.user_role).described_as(f"{err_prefix} user_role") \
                .is_equal_to(expected_creation_request.user_role)
            assert_that(actual_creation_request.request_uuid).described_as(f"{err_prefix} request_uuid") \
                .is_equal_to(expected_creation_request.request_uuid)
            assert_that(actual_creation_request.type).described_as(f"{err_prefix} user_role") \
                .is_equal_to(expected_creation_request.type)
            assert_that(actual_creation_request.id).described_as(f"{err_prefix} id").is_greater_than(0)
        else:
            soft_fail(
                f"Creation request list doesn't include object with request_uuid={expected_creation_request.request_uuid}")

    @step("Assert requests list is empty")
    def assert_creation_requests_list_empty(self, actual: List[WalletCreationRequestDTO]):
        assert_that(actual).is_empty()

    @step("Assert requests in requests list")
    def assert_requests_are_exists(self, actual_list: list[WalletCreationRequestDTO],
                                   expected_list: list[WalletCreationRequestDTO]):

        for expected_request in expected_list:
            actual_requests = list(filter(lambda x: x.id == expected_request.id, actual_list))
            if len(actual_requests) != 1:
                assert_that(actual_requests).described_as(f"requests with id={expected_request.id}").is_length(1)
                return

            assert_that(expected_request.serialize()) \
                .described_as("Comparing") \
                .is_equal_to(actual_requests[0].serialize())
