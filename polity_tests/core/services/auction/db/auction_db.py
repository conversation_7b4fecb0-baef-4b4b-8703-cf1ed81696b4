import logging
from datetime import datetime

from allure import step

from core.services.auction.db.db_base import DBBase


class AuctionDb(DBBase):

    won_lots_table = "public.won_lots"
    tiers_table = "public.tiers"
    permissions_table = "public.permissions"
    products_table = "public.products"

    @classmethod
    def insert_product(cls, name: str, category: str, merchant: str, min_price: int, description: str = None):

        params = {'name': name, 'category': category,
                  'merchant': merchant, 'min_price': min_price, 'description': description,
                  'created_at': datetime.now(), 'updated_at': datetime.now()}

        with step(f"Insert {name} product to auction"):
            columns = ", ".join((k for k in params.keys() if params[k] is not None))
            values = tuple(str(v) for v in params.values() if v is not None)

            query = f"insert into {cls.products_table}({columns}) \n" \
                    f"values {values} RETURNING *;"
            print(query)
            return cls._execute_query_to_dict(query)

    @classmethod
    def insert_permission(cls, category: str, action: str):

        params = {'category': category, 'action': action, 'created_at': datetime.now(), 'updated_at': datetime.now()}

        with step(f"Insert {category} permission to auction"):
            columns = ", ".join((k for k in params.keys() if params[k] is not None))
            values = tuple(str(v) for v in params.values() if v is not None)

            query = f"insert into {cls.permissions_table}({columns}) \n" \
                    f"values {values} RETURNING *;"
            return cls._execute_query_to_dict(query)

    @classmethod
    def insert_tier(cls, name: str, product_quantity: int, buy_now_price: int, product_id: int, permission_id: int):

        params = {'product_quantity': product_quantity, 'name': name, 'buy_now_price': buy_now_price,
                  'product_id': product_id, 'permission_id': permission_id,
                  'created_at': datetime.now(), 'updated_at': datetime.now()}

        with step(f"Insert {name} tier to auction"):
            columns = ", ".join((k for k in params.keys() if params[k] is not None))
            values = tuple(str(v) for v in params.values() if v is not None)

            query = f"insert into {cls.tiers_table}({columns}) \n" \
                    f"values {values} RETURNING *;"
            return cls._execute_query_to_dict(query)

    @classmethod
    def insert_won_lot(cls, price: int, user_id: str, status: str, tier_id: int, agreement_id: int):

        params = {'price': price, 'user_id': user_id, 'status': status,
                  'tier_id': tier_id, 'agreement_id': agreement_id,
                  'created_at': datetime.now(), 'updated_at': datetime.now(), 'close_time': datetime.now()}

        with step(f"Insert {tier_id} tier to won_lots to auction"):
            columns = ", ".join((k for k in params.keys() if params[k] is not None))
            values = tuple(str(v) for v in params.values() if v is not None)

            query = f"insert into {cls.won_lots_table}({columns}) \n" \
                    f"values {values} RETURNING *;"
            return cls._execute_query_to_dict(query)
