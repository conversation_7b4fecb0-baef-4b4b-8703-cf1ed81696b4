package com.dcs.authgateway.config

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.web.client.RestTemplate

@Configuration
class RestTemplateConfiguration {

    @Bean
    fun slackTechnicalChanel(): RestTemplate {
        return RestTemplate()
    }

    @Bean
    fun polityVaultRestTemplate(): RestTemplate {
        return RestTemplateBuilder().build()
    }
}