package com.dcs.authgateway.repository

import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.entity.CustomerRoute
import com.dcs.authgateway.dto.networkOperator.RoleCount
import com.dcs.authgateway.dto.networkOperator.RouteCount
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.util.UUID

interface CustomerRouteRepository : JpaRepository<CustomerRoute, Long> {

    fun findAllByCustomerIdAndRole(customerId: Long, role: Role): List<CustomerRoute>?
    fun existsByFullName(fullName: String): Boolean
    fun findByUuid(uuid: UUID): CustomerRoute?
    fun findByUsername(username: String): CustomerRoute?
    fun existsCustomerRouteByUsername(username: String): Boolean
    fun existsCustomerRouteByElementId(elementId: String): Boolean
    fun findByUsernameContainingAndRole(
        @Param("username") username: String,
        @Param("role") role: Role
    ): List<CustomerRoute>


    fun findByRole(role: Role?): List<CustomerRoute>



    @Query(
        value = "SELECT new com.dcs.authgateway.dto.networkOperator.RoleCount(c.role, COUNT(c.role))" +
                " FROM CustomerRoute AS c GROUP BY c.role"
    )
    fun countCustomersByRole(): List<RoleCount>

    @Query(
        value = "SELECT new com.dcs.authgateway.dto.networkOperator.RouteCount(ava.route, count(ava)) from CustomerRoute" +
                " AS ava WHERE ava.role = 'AVATAR' AND ava.route is not null GROUP BY ava.route"
    )
    fun avatarsByRoute(): List<RouteCount>
}