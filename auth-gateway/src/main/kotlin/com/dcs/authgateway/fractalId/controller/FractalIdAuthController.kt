package com.dcs.authgateway.fractalId.controller

import com.dcs.authgateway.controller.dto.request.AuthenticationRequest
import com.dcs.authgateway.controller.dto.request.RegisterRequest
import com.dcs.authgateway.controller.dto.response.AuthenticationResponse
import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.fractalId.dto.TokenResponse
import com.dcs.authgateway.fractalId.dto.UserInfoResponse
import com.dcs.authgateway.repository.UserRepository
import com.dcs.authgateway.service.*
import jakarta.servlet.http.HttpServletRequest
import org.apache.coyote.BadRequestException
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.*
import org.springframework.web.bind.annotation.*
import org.springframework.web.client.RestTemplate
import java.util.*

@RestController
@RequestMapping("/api/v1/fractal-auth")
class FractalIdAuthController(
    private val stageAuthDomain: RestTemplate,
    private val stageResourceDomain: RestTemplate,
    private val codeToUUIDService: CodeToUUIDService,
    private val customerRouteService: CustomerRouteService,
    private val authenticationService: AuthenticationService,
    private val userRepository: UserRepository
    ) {


    @Value("\${fractal-id.client-id}")
    val clientId = ""

    @Value("\${fractal-id.client-secret}")
    val clientSecret = ""

    @Value("\${fractal-id.redirect-uri}")
    val redirectUri = ""

    @Value("\${urls.redirectUrl}")
    var redirectUrl: String? = ""

    @Value("\${urls.networkOperatorUrl}")
    var networkOperatorUrl: String? = ""


    @PostMapping("/obtain-token/")
    @ResponseBody
    fun obtainToken(@RequestParam code: String): ResponseEntity<TokenResponse> {

        val map: MutableMap<String, String> = HashMap()
        map["client_id"] = clientId
        map["client_secret"] = clientSecret
        map["code"] = code
        map["grant_type"] = "authorization_code"
        map["redirect_uri"] = redirectUri

        val request: HttpEntity<MutableMap<String, String>> = HttpEntity<MutableMap<String, String>>(map)

        return stageAuthDomain.postForEntity("/oauth/token", request, TokenResponse::class.java)
    }

    @PostMapping("/registration/")
    @ResponseBody
    fun registration(@RequestBody registrationRequest: RegistrationRequest): AuthenticationResponse{
        val userInfo = getUserInfo(registrationRequest.token).body

        when(codeToUUIDService.verifyCode(registrationRequest.code)) {
            true -> {
                return when(registrationRequest.role) {
                    Role.ADVISER -> {
                        val customerRoute = customerRouteService.findByUsernameAndFetchRoutesEagerly("Bastion Cooperative")
                        val request = AuthenticationRequest(
                            username = customerRoute.username,
                            password = null
                        )

                        authenticationService.authenticate(customerRoute, request, customerRoute.route)
                    }
                    Role.NETWORK_OPERATOR -> {
                        val customerRoute = customerRouteService.findByUsernameAndFetchRoutesEagerly("basic-OPERATOR")
                        val request = AuthenticationRequest(
                            username = customerRoute.username,
                            password = null
                        )

                        authenticationService.authenticate(customerRoute, request, customerRoute.route)
                    }
                    Role.INVESTOR -> {
                        val username = userInfo?.person?.full_name + "-" + userInfo?.uid

                        val customerRoute = customerRouteService.save(
                            username,
                            null,
                            Role.INVESTOR,
                            redirectUrl = getRedirectUrlDependOnTheRole(Role.INVESTOR),
                            uuid = UUID.fromString(userInfo?.uid),
                            element_id = null,
                            fullName = userInfo?.person?.full_name!!
                        )
                        customerRoute.customerId = customerRoute.id
                        customerRouteService.update(customerRoute)

                        val request = RegisterRequest(
                            username = customerRoute.username,
                            password = null,
                            role = Role.INVESTOR
                        )

                        if (userInfo != null) {
                            codeToUUIDService.setUUIDToCode(registrationRequest.code, userInfo.uid)
                        }
                        authenticationService.register(customerRoute, request, customerRoute.route)
                    }
                    else -> {
                        throw IllegalArgumentException("Unsupported argument")
                    }
                }

            }
            false -> {
                return loginInvestor(registrationRequest.token, Role.INVESTOR)
            }
        }
    }

    @PostMapping("/login/")
    @ResponseBody
    fun authenticateUser(@RequestBody loginRequest: LoginRequest): AuthenticationResponse {
        return when(loginRequest.role) {
            Role.NETWORK_OPERATOR -> {
                login("basic-OPERATOR", Role.NETWORK_OPERATOR)
            }
            Role.ADVISER -> {
                login("Bastion Cooperative", Role.ADVISER)
            }
            Role.INVESTOR -> {
                loginInvestor(loginRequest.token, Role.INVESTOR)
            }
            else -> {
                throw IllegalArgumentException("Unsupported argument")
            }
        }
    }

    @PostMapping("/refresh-token/")
    fun refreshToken(request: HttpServletRequest): AuthenticationResponse? {
        return authenticationService.refreshToken(request)
    }

    class RegistrationRequest(
        val code: Int,
        val token: String,
        val role: Role
    )

    class LoginRequest(
        val token: String,
        val role: Role
    )


    private fun loginInvestor(token: String, role: Role): AuthenticationResponse {
        val userInfo = getUserInfo(token).body!!
        val username = userInfo.person?.full_name + "-" + userInfo.uid
        return login(username, role, userInfo)
    }

    private fun login(username: String, role: Role, userInfoResponse: UserInfoResponse? = null): AuthenticationResponse {

        // TODO: Delete this check when users updated tokens
        if (!userRepository.existsByUsername(username)) {

            if (customerRouteService.isExistsByUsername(username)) {
                val customerRoute = customerRouteService.getByUsername(username)

                val request = RegisterRequest(
                    username = customerRoute.username,
                    password = null,
                    role = role
                )
                return authenticationService.register(customerRoute, request, customerRoute.route)
            }

            if (customerRouteService.existsByFullName(userInfoResponse!!.person!!.full_name!!)) {
                val customerRoute = customerRouteService.save(
                    username,
                    null,
                    Role.INVESTOR,
                    redirectUrl = getRedirectUrlDependOnTheRole(Role.INVESTOR),
                    uuid = UUID.fromString(userInfoResponse.uid),
                    fullName = userInfoResponse.person!!.full_name!!,
                    element_id = null
                )

                val request = RegisterRequest(
                    username = customerRoute.username,
                    password = null,
                    role = Role.INVESTOR
                )
                return authenticationService.register(customerRoute, request, customerRoute.route)
            }

        } else {

            val customerRoute = customerRouteService.getByUsername(username)
            val request = AuthenticationRequest(
                password = null,
                username = username
            )

            return authenticationService.authenticate(customerRoute, request, customerRoute.route)
        }
        throw BadRequestException("Something went wrong")
    }

    private fun getUserInfo(token: String): ResponseEntity<UserInfoResponse> {
        val headers = HttpHeaders()
        headers.add("Authorization", "Bearer $token")

        val request = HttpEntity<String>(headers)

        return stageResourceDomain.exchange(
            "/users/me", HttpMethod.GET, request, UserInfoResponse::class.java
        )
    }

    private fun getRedirectUrlDependOnTheRole(role: Role): String? {
        return when (role) {
            Role.ADVISER -> redirectUrl
            Role.NETWORK_OPERATOR -> networkOperatorUrl
            else -> null
        }
    }
}