package com.dcs.authgateway.fractalId.config

import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.web.client.RestTemplate

@Configuration
class FractalIdConfig {

    @Bean
    fun stageFrontendDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://developer.staging.sandbox.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }

    @Bean
    fun productionFrontendDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://app.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }

    @Bean
    fun stageAuthDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://auth.staging.sandbox.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }

    @Bean
    fun productionAuthDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://auth.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }

    @Bean
    fun stageResourceDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://resource.staging.sandbox.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .build()
    }

    @Bean
    fun productionResourceDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://resource.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }

    @Bean
    fun stageVerifierDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://verifier.next.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }

    @Bean
    fun productionVerifierDomain(): RestTemplate {
        return RestTemplateBuilder()
            .rootUri("https://verifier.fractal.id")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
//            .defaultHeader(HttpHeaders.AUTHORIZATION, createBamboraAuthorizationHeader(bamboraProperties))
            .build()
    }
}