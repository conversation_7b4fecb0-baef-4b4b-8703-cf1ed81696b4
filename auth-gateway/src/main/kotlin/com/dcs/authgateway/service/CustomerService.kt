//package com.dcs.authgateway.service
//
//import com.dcs.authgateway.controller.UsernameAlreadyTakenException
//import com.dcs.authgateway.dto.CustomerRegistrationDto
//import com.dcs.authgateway.entity.enum.Role
//import com.dcs.authgateway.fractalId.dto.UserInfoResponse
//import com.dcs.authgateway.entity.Customer
//import com.dcs.authgateway.entity.CustomerRoute
//import org.springframework.security.core.userdetails.UserDetails
//import org.springframework.security.core.userdetails.UsernameNotFoundException
//import org.springframework.stereotype.Service
//import com.dcs.authgateway.repository.CustomerRepository
//import org.springframework.beans.factory.annotation.Value
//import org.springframework.security.core.GrantedAuthority
//import org.springframework.security.core.userdetails.UserDetailsService
//import org.springframework.security.crypto.password.PasswordEncoder
//import org.springframework.transaction.annotation.Transactional
//import java.util.UUID
//
//@Service
//class CustomerService(
//    private val customerRepository: CustomerRepository,
//    private val customerRouteService: CustomerRouteService
//) : UserDetailsService {
//    @Value("\${urls.redirectUrl}")
//    var redirectUrl: String? = ""
//
//    @Value("\${urls.networkOperatorUrl}")
//    var networkOperatorUrl: String? = ""
//
//    @Transactional
//    fun save(registrationDto: CustomerRegistrationDto, passwordEncoder: PasswordEncoder): Customer? {
//        val username = registrationDto.username
//        checkIfUsernameExists(username)
//        val password = passwordEncoder.encode(registrationDto.password)
//        var customer = Customer(
//            password,
//        )
//        customer = customerRepository.save(customer)
//        val redirectUrl = when (registrationDto.role) {
//            Role.ADVISER -> redirectUrl
//            Role.NETWORK_OPERATOR -> networkOperatorUrl
//            else -> null
//        }
//        customerRouteService.save(
//            username,
//            null,
//            customer,
//            registrationDto.role,
//            redirectUrl = redirectUrl,
//            uuid = UUID.randomUUID(),
//            element_id = null
//        )
//        return customer
//    }
//
//    @Transactional
//    fun saveWithFractalId(userInfoResponse: UserInfoResponse, role: Role): Customer? {
//        val username = userInfoResponse.person?.full_name + "-" + userInfoResponse.uid
//        checkIfFractalIdExists(username)
//        val customer = customerRepository.save(Customer())
//        val redirectUrl = when (role) {
//            Role.ADVISER -> redirectUrl
//            Role.NETWORK_OPERATOR -> networkOperatorUrl
//            else -> null
//        }
//        customerRouteService.save(
//            username,
//            null,
//            customer,
//            role,
//            redirectUrl = redirectUrl,
//            uuid = UUID.fromString(userInfoResponse.uid),
//            element_id = null
//        )
//        return customer
//    }
//
//    private fun checkIfUsernameExists(username: String) {
//        customerRouteService.getByUsername(username)?.let {
//            throw UsernameAlreadyTakenException("Username already taken")
//        }
//    }
//
//    private fun checkIfFractalIdExists(username: String) {
//        customerRouteService.getByUsername(username)?.let {
//            throw UsernameAlreadyTakenException("User connected to this Fractal.id account is already existing")
//
//        }
//    }
//
//
//    @Throws(UsernameNotFoundException::class)
//    override fun loadUserByUsername(username: String): UserDetails {
//        val profile = customerRouteService.findByUsernameAndFetchRoutesEagerly(username)
//            ?: throw UsernameNotFoundException("Invalid username or password.")
//        return org.springframework.security.core.userdetails.User(
//            profile.username, profile.customer!!.password?: "test", listOf(
//                GrantedAuthority { profile.role.toString() }
//            )
//        )
//    }
//
//    fun loadProfileByUsername(username: String): CustomerRoute {
//        return customerRouteService.findByUsernameAndFetchRoutesEagerly(username)
//            ?: throw UsernameNotFoundException("User doesn't exists")
//    }
//
//    fun isExistsByUsername(username: String): Boolean {
//        return customerRouteService.isExistsByUsername(username)
//    }
//
//    fun loadCustomerByUsername(username: String): Customer? {
//        val profile = customerRouteService.findByUsernameAndFetchRoutesEagerly(username)
//            ?: throw UsernameNotFoundException("Invalid username or password.")
//        return profile.customer
//    }
//
//    fun loadCustomerByUuid(uuid: UUID): Customer? {
//        val profile = customerRouteService.findByUuidAndFetchRoutesEagerly(uuid)
//            ?: throw UsernameNotFoundException("Invalid i or password.")
//        return profile.customer
//    }
//
//    fun loadProfileByUuid(uuid: UUID): CustomerRoute {
//        return customerRouteService.findByUuidAndFetchRoutesEagerly(uuid)
//            ?: throw UsernameNotFoundException("Invalid username or password.")
//    }
//
//    fun loadProfileByUsernameOrNull(username: String): CustomerRoute? {
//        return customerRouteService.findByUsernameAndFetchRoutesEagerly(username)
//    }
//
//}