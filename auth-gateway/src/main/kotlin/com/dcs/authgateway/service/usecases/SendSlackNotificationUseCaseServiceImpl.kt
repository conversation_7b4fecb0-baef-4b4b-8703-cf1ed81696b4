package com.dcs.authgateway.service.usecases

import com.dcs.authgateway.entity.slack.SlackNotificationType
import com.dcs.authgateway.service.SlackNotificationService
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.*
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.net.URI
import java.util.HashMap

@Service
class SendSlackNotificationUseCaseServiceImpl(
    private val slackTechnicalChanel: RestTemplate,
    private val slackNotificationService: SlackNotificationService,
    @Value("\${slack.webhook.tech}")
    private val SLACK_WEBHOOK_FOR_TECHNICAL: String,
    @Value("\${slack.webhook.business}")
    private val SLACK_WEBHOOK_FOR_BUSINESS: String
) : SendSlackNotificationUseCaseService {


    override fun sendSlackBusinessNotification(
        investorName: String, avatarName: String, adviserName: String
    ): HttpStatusCode {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val map: MutableMap<String, String> = HashMap()
        map["text"] = "Pending connection: Investor: $investorName with Avatar: $avatarName -> Adviser: $adviserName"

        val request: HttpEntity<MutableMap<String, String>> = HttpEntity<MutableMap<String, String>>(map, headers)

        val uri = URI.create(SLACK_WEBHOOK_FOR_BUSINESS)

        val response = slackTechnicalChanel.postForEntity(uri, request, String::class.java)

        if (response.statusCode == HttpStatus.OK) {
            slackNotificationService.saveSlackNotification(SlackNotificationType.NON_TECHNICAL, map["text"]!!)
        }
        return response.statusCode
    }

    override fun sendSlackTechNotification(
        avatarName: String, request_uuid: String, wallet_type: String
    ): HttpStatusCode {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val map: MutableMap<String, String> = HashMap()
        map["text"] = "User: $avatarName created wallet $wallet_type with id: $request_uuid "

        val request: HttpEntity<MutableMap<String, String>> = HttpEntity<MutableMap<String, String>>(map, headers)

        val uri = URI.create(SLACK_WEBHOOK_FOR_TECHNICAL)

        val response = slackTechnicalChanel.postForEntity(uri, request, String::class.java)

        if (response.statusCode == HttpStatus.OK) {
            slackNotificationService.saveSlackNotification(SlackNotificationType.TECHNICAL, map["text"]!!)
        }
        return response.statusCode
    }
}