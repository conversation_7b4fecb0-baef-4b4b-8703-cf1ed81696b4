package com.dcs.authgateway.service

import com.dcs.authgateway.controller.dto.request.AuthenticationRequest
import com.dcs.authgateway.controller.dto.request.RegisterRequest
import com.dcs.authgateway.controller.dto.response.AuthenticationResponse
import com.dcs.authgateway.entity.CustomerRoute
import com.dcs.authgateway.entity.TokenEntity
import com.dcs.authgateway.entity.UserEntity
import com.dcs.authgateway.entity.enum.TokenType
import com.dcs.authgateway.repository.TokenRepository
import com.dcs.authgateway.repository.UserRepository
import jakarta.servlet.http.HttpServletRequest
import org.springframework.http.HttpHeaders
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import java.util.*

@Service
class AuthenticationService(
    private val userRepository: UserRepository,
    private val passwordEncoder: PasswordEncoder,
    private val jwtService: JwtService,
    private val authenticationManager: AuthenticationManager,
    private val tokenRepository: TokenRepository,
    private val customerRouteService: CustomerRouteService
) {

    fun register(customerRoute: CustomerRoute, request: RegisterRequest, redirectUrl: String?): AuthenticationResponse {
        val userEntity = with(request) {
            UserEntity(
                username = username,
                password = passwordEncoder.encode(password ?: ""),
                role = role
            )
        }

        val savedUser = userRepository.save(userEntity)
        val jwtToken = jwtService.generateToken(customerRoute, userEntity)
        val refreshToken = jwtService.generateRefreshToken(customerRoute, userEntity)
        saveUserToken(savedUser, jwtToken)
        return AuthenticationResponse(jwtToken, refreshToken, redirectUrl)
    }

    fun authenticate(
        customerRoute: CustomerRoute, request: AuthenticationRequest, redirectUrl: String?
    ): AuthenticationResponse {
        with(request) {
            authenticationManager.authenticate(UsernamePasswordAuthenticationToken(username, password ?: ""))

            val user = userRepository.findByUsername(username)!!
            val jwtToken = jwtService.generateToken(customerRoute, user)
            val refreshToken = jwtService.generateRefreshToken(customerRoute, user)
            revokeAllUserTokens(user)
            saveUserToken(user, jwtToken)
            return AuthenticationResponse(jwtToken, refreshToken, redirectUrl)
        }
    }

    private fun saveUserToken(userEntity: UserEntity, jwtToken: String) {
        println(jwtToken)
        val tokenEntity = TokenEntity(
            user = userEntity,
            token = jwtToken,
            tokenType = TokenType.BEARER,
            expired = false,
            revoked = false
        )
        tokenRepository.save(tokenEntity)
    }

    private fun revokeAllUserTokens(userEntity: UserEntity) {
        val validUserTokens = tokenRepository.findAllValidTokensByUserId(userEntity.id!!)

        if (validUserTokens.isEmpty()) return

        validUserTokens.map { token ->
            token.apply {
                expired = true
                revoked = true
            }
        }

        tokenRepository.saveAll(validUserTokens)
    }

    fun refreshToken(request: HttpServletRequest): AuthenticationResponse? {
        val authHeader = request.getHeader(HttpHeaders.AUTHORIZATION)
        if (authHeader == null || !authHeader.startsWith("Bearer ")) return null

        val refreshToken = authHeader.substring(7)
        val userEmail = jwtService.getUserName(refreshToken)
        val uuid = jwtService.getClaims(refreshToken)["uuid"].toString()
        val customerRoute = customerRouteService.findByUuid(UUID.fromString(uuid))
        if (userEmail != null) {
            val user = userRepository.findByUsername(userEmail) ?: throw Exception()
            if (jwtService.isTokenValid(refreshToken, user)) {
                val accessToken = jwtService.generateToken(customerRoute, user)
                revokeAllUserTokens(user)
                saveUserToken(user, accessToken)

                return AuthenticationResponse(accessToken, refreshToken, customerRoute.route)
            }
        }
        return null
    }
}

