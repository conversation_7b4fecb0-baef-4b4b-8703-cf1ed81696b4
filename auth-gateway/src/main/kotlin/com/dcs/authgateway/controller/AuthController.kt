package com.dcs.authgateway.controller

import com.dcs.authgateway.controller.dto.request.AuthenticationRequest
import com.dcs.authgateway.controller.dto.request.RegisterRequest
import com.dcs.authgateway.controller.dto.response.AuthenticationResponse
import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.service.AuthenticationService
import com.dcs.authgateway.service.CustomerRouteService
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.*
import java.util.*


@RestController
@RequestMapping("/api/v1/auth")
class AuthController(
    private val authenticationService: AuthenticationService,
    private val customerRouteService: CustomerRouteService
) {

    @Value("\${urls.redirectUrl}")
    var redirectUrl: String? = ""

    @Value("\${urls.networkOperatorUrl}")
    var networkOperatorUrl: String? = ""

    @PostMapping("/login")
    fun authenticate(@RequestBody request: AuthenticationRequest): AuthenticationResponse {
        val customerRoute = customerRouteService.findByUsernameAndFetchRoutesEagerly(request.username)
        return authenticationService.authenticate(customerRoute, request, customerRoute.route)
    }

    @PostMapping("/registration")
    @ResponseBody
    fun register(@RequestBody request: RegisterRequest): AuthenticationResponse {
        val redirectUrl = when (request.role) {
            Role.ADVISER -> redirectUrl
            Role.NETWORK_OPERATOR -> networkOperatorUrl
            else -> null
        }
        val customerRoute = customerRouteService.save(
            request.username,
            null,
            request.role,
            redirectUrl = redirectUrl,
            uuid = UUID.randomUUID(),
            element_id = null
        )
        customerRoute.customerId = customerRoute.id
        customerRouteService.update(customerRoute)
        return authenticationService.register(customerRoute, request, customerRoute.route)
    }

    @PostMapping("/refresh-token")
    fun refreshToken(request: HttpServletRequest): AuthenticationResponse? {
        return authenticationService.refreshToken(request)
    }
}

class UsernameAlreadyTakenException (message: String) : RuntimeException(message)
