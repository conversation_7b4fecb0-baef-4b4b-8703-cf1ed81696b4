package com.dcs.authgateway.controller.investor

import com.dcs.authgateway.service.AuthenticationService
import com.dcs.authgateway.service.CustomerRouteService
import com.dcs.authgateway.service.JwtService
import com.dcs.authgateway.service.usecases.SendSlackNotificationUseCaseService
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController
@RequestMapping("/api/v1/investor/")
class InvestorController(
    private val customerRouteService: CustomerRouteService,
    private val sendSlackNotificationUseCaseService: SendSlackNotificationUseCaseService,
    private val jwtService: JwtService
) {

    @PostMapping
    fun sendSlackNotificationAvatarConnectWithAdviser(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody avatarToAdviserRequest: AvatarToAdviserRequest
    ): HttpStatusCode {
        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getClaims(jwt)
        val uuid = decodedJwt["uuid"] as String
        val investor = customerRouteService.findByUuid(UUID.fromString(uuid))

        val adviser = customerRouteService.getCustomerRouteById(avatarToAdviserRequest.adviserId)
        val avatar = customerRouteService.getCustomerRouteById(avatarToAdviserRequest.avatarId)

        return sendSlackNotificationUseCaseService.sendSlackBusinessNotification(
            investorName = investor.username,
            avatarName = avatar.username,
            adviserName = adviser.username
        )
    }

    class AvatarToAdviserRequest(
        val avatarId: Long,
        val adviserId: Long
    )
}