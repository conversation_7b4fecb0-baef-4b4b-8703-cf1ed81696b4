package com.dcs.authgateway.controller.investor

import com.dcs.authgateway.dto.AdviserResponseDto
import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.service.*
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.util.*


@RestController
@RequestMapping("/api/v1/investor/advisers/")
class InvestorAdviserController(
    private val customerRouteService: CustomerRouteService
) {


    @GetMapping
    @ResponseBody
    fun getAdviserList(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestParam q: String?
    ): ResponseEntity<List<AdviserResponseDto>> {
        val adviserList = q?.let {
            customerRouteService.findCustomerRoutesByRoleContainingName(q)
        } ?: run {
            customerRouteService.findCustomerRoutesByRole(Role.ADVISER)
        }
        val adviserListDTOs = adviserList.map { customerRouteService.entityToAdviserDto(it) }
        return ResponseEntity(
            adviserListDTOs,
            HttpStatus.OK
        )
    }

    @GetMapping("{id}/")
    fun getAvatar(@PathVariable(value = "id") adviserId: Long): ResponseEntity<AdviserResponseDto>? {
        val adviser = customerRouteService.getCustomerRouteById(adviserId)

        return ResponseEntity(
            customerRouteService.entityToAdviserDto(adviser),
            HttpStatus.OK
        )
    }
}

