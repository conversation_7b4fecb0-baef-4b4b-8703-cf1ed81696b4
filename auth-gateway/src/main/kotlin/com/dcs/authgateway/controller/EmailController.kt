package com.dcs.authgateway.controller

import com.dcs.authgateway.entity.enum.EmailTemplate
import com.dcs.authgateway.exception.EmailTemplateException
import com.dcs.authgateway.service.EmailService
import com.sendgrid.Response
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

@RestController
@RequestMapping("/api/v1/email")
class EmailController(
    private val emailService: EmailService
) {

    @PostMapping("/send")
    fun sendEmail(
        @RequestBody emailRequest: EmailRequest
    ): ResponseEntity<Response> {
        if ((emailRequest.emailTemplate == EmailTemplate.NETWORK_OPERATOR && emailRequest.passCode.isNullOrEmpty()) ||
            (emailRequest.emailTemplate == EmailTemplate.ADVISER && emailRequest.passCode.isNullOrEmpty())) {
            throw EmailTemplateException("passCode must be defined")
        } else {
            val response = emailService.sendEmail(
                emailRequest.name, emailRequest.emailTemplate, emailRequest.sendTo, emailRequest.passCode
            )
            return ResponseEntity.ok(response)
        }
    }

    class EmailRequest(
        val emailTemplate: EmailTemplate,
        val sendTo: String,
        val name: String,
        val passCode: String?
    )
}