package com.dcs.authgateway.controller

import com.dcs.authgateway.service.CustomerRouteService
import com.dcs.authgateway.service.JwtService
import com.dcs.authgateway.service.usecases.SendSlackNotificationUseCaseService
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController
@RequestMapping("/api/v1/slack-notification")
class SlackNotificationController(
    private val sendSlackNotificationUseCaseService: SendSlackNotificationUseCaseService,
    private val customerRouteService: CustomerRouteService,
    private val jwtService: JwtService
) {

    @PostMapping("/webhook")
    fun sendSlackNotificationWalletCreated(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody notificationRequest: NotificationRequest
    ): HttpStatusCode {
        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getClaims(jwt)
        val uuid = decodedJwt["uuid"] as String
        val avatar = customerRouteService.findByUuid(UUID.fromString(uuid))

        return sendSlackNotificationUseCaseService.sendSlackTechNotification(
            avatarName = avatar.username,
            request_uuid = notificationRequest.request_uuid,
            wallet_type = notificationRequest.wallet_type
        )
    }

    class NotificationRequest(
        val request_uuid: String,
        val uuid: String,
        val wallet_type: String
    )
}