spring.datasource.url=jdbc:postgresql://${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
spring.datasource.username=${POSTGRES_USER}
spring.datasource.password=${POSTGRES_PASSWORD}

logging.level.root=DEBUG

spring.liquibase.change-log=classpath:db/changelog-master.xml
spring.liquibase.enabled=true
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=update

fractal-id.client-id=${CLIENT_ID}
fractal-id.client-secret=${CLIENT_SECRET}
fractal-id.redirect-uri=${REDIRECT_URI}

spring.data.redis.repositories.enabled=true
spring.data.redis.host=${REDIS_HOST}
spring.data.redis.port=${REDIS_PORT}
spring.data.redis.password=${REDIS_PASSWORD}

slack.webhook.tech=${SLACK_WEBHOOK_FOR_TECHNICAL}
slack.webhook.business=${SLACK_WEBHOOK_FOR_BUSINESS}

springdoc.swagger-ui.path=/swagger-ui.html

jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.refreshExpirationMs=86400000
jwt.expirationMs=3600000

urls.redirectUrl=${POLITY_HOST:cabinet-stg.polity.network/}
urls.authGatewayUrl=${AUTH_HOST:https://mvp-stg.polity.network}
urls.networkOperatorUrl=${POLITY_OPERATOR_HOST:polity-stage-operator.quantumobile.com}