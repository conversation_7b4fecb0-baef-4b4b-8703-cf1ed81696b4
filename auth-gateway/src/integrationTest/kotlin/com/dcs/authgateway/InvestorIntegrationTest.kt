//package com.dcs.authgateway
//
//import com.dcs.authgateway.dto.*
//import com.dcs.authgateway.entity.enum.Role
//import com.dcs.authgateway.payload.response.LoginResponse
//import com.dcs.authgateway.service.CustomerRouteService
//import com.dcs.authgateway.service.CustomerService
//import com.dcs.authgateway.service.JwtService
//import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
//import io.mockk.mockk
//import net.sf.jsqlparser.util.validation.metadata.DatabaseException
//import org.junit.jupiter.api.*
//import org.junit.jupiter.api.Assertions.assertEquals
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.boot.test.web.client.TestRestTemplate
//import org.springframework.core.ParameterizedTypeReference
//import org.springframework.core.io.FileSystemResource
//import org.springframework.http.*
//import org.springframework.jdbc.core.JdbcTemplate
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
//import org.springframework.test.context.ActiveProfiles
//import org.springframework.test.jdbc.JdbcTestUtils
//import org.springframework.util.LinkedMultiValueMap
//import org.springframework.util.MultiValueMap
//import org.springframework.web.multipart.MultipartFile
//
//
//@ActiveProfiles("test")
//@SpringBootTest(
//    classes = arrayOf(AuthGatewayApplication::class),
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
//)
//class InvestorIntegrationTest {
//
//    @Autowired
//    lateinit var restTemplate: TestRestTemplate
//
//    @Autowired
//    final lateinit var customerService: CustomerService
//
//    @Autowired
//    lateinit var customerRouteService: CustomerRouteService
//
//    @Autowired
//    lateinit var passwordEncoder: BCryptPasswordEncoder
//
//    @Autowired
//    lateinit var jdbcTemplate: JdbcTemplate
//
//    @Autowired
//    lateinit var jwtService: JwtService
//
//    val file = mockk<MultipartFile>().apply {
//        val avatarDto = AvatarCreationDto("JOHN SMITH", this)
//    }
//    var persistedCustomer: Customer? = null
//    val mapper = jacksonObjectMapper()
//    val customerDto = CustomerRegistrationDto("JOHN SMITH", Role.INVESTOR, "123ING456")
//
//    @BeforeEach
//    @Throws(DatabaseException::class)
//    fun fixture() {
//        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "refresh_token", "customer_route", "customer")
//        persistedCustomer = customerService.save(customerDto, passwordEncoder)!!
//    }
//
//    @Test
//    fun whenRegistered_thenShouldReturn200nAvatarCreation() {
//
//        val headers = HttpHeaders()
//
//        val jwtToken = jwtService.generateToken(customerRouteService.findCustomerRoutesByRole(Role.INVESTOR).first())
//        headers.set("Authorization", "Bearer $jwtToken")
//        headers.setContentType(MediaType.MULTIPART_FORM_DATA)
//        val body: MultiValueMap<String, Any> = LinkedMultiValueMap()
//        body.add("name", "avatar")
//        body.add("photo", getTestFile())
//        val requestEntity = HttpEntity<MultiValueMap<String, Any>>(body, headers)
//
//        val serverUrl = "/api/investor/avatars"
//
//        val response = restTemplate.postForEntity(serverUrl, requestEntity, AvatarResponseDto::class.java)
//        assertEquals(HttpStatus.OK, response?.statusCode)
//        assertEquals(1, customerRouteService.findCustomerRoutesByRoleAndCustomer(Role.AVATAR, persistedCustomer!!).size)
//    }
//
//    private fun getTestFile(): FileSystemResource {
//        return FileSystemResource("src/integrationTest/resources/index.jpg")
//    }
//
//
//    @Test
//    fun whenListAvatars_thenShouldReturnListOfAvatars() {
//        customerRouteService.save("name", "path", persistedCustomer!!, Role.AVATAR, element_id = "")
//        customerRouteService.save("name2", "path2", persistedCustomer!!, Role.AVATAR, element_id = "")
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_JSON
//        val jwtToken = jwtService.generateToken(customerRouteService.findCustomerRoutesByRole(Role.INVESTOR).first())
//
//        headers.set("Authorization", "Bearer $jwtToken")
//
//        val request = HttpEntity<Void>(headers)
//        val result = restTemplate.exchange(
//            "/api/investor/avatars", HttpMethod.GET, request,
//            object : ParameterizedTypeReference<List<AvatarResponseDto>>() {}
//        )
//        val body: List<AvatarResponseDto>? = result.body
//
//        assertEquals(HttpStatus.OK, result.statusCode)
//        assertEquals(2, body!!.size)
//        assertEquals(false, body.first().hasAcquiredNode)
//    }
//
//
//    @Test
//    fun whenLoginAsAvatar_thenShouldChangeIdentity() {
//        val avatar = customerRouteService.save("name", "path", persistedCustomer!!, Role.AVATAR, element_id = "")
//        val jwtToken = jwtService.generateToken(customerRouteService.findCustomerRoutesByRole(Role.INVESTOR).first())
//
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_JSON
//
//        headers.set("Authorization", "Bearer $jwtToken")
//        val request = HttpEntity(mapper.writeValueAsString(AvatarLoginDto(avatar.id!!)), headers)
//        val result = restTemplate.postForEntity("/api/investor/avatars/login", request, LoginResponse::class.java)
//        assertEquals(HttpStatus.FOUND, result.statusCode)
//        assert(refreshTokenService.existsByToken(result.body!!.refresh_token))
//    }
//
//
//    @Test
//    fun whenListAdvisers_thenShouldReturnListOfAvatars() {
//        customerRouteService.save("name", "path", persistedCustomer!!, Role.ADVISER, element_id = "")
//        customerRouteService.save("2eman", "path2", persistedCustomer!!, Role.ADVISER, element_id = "")
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_JSON
//        val jwtToken = jwtService.generateToken(customerRouteService.findCustomerRoutesByRole(Role.INVESTOR).first())
//
//        headers.set("Authorization", "Bearer $jwtToken")
//
//        val request = HttpEntity<Void>(headers)
//        var result = restTemplate.exchange(
//            "/api/investor/advisers", HttpMethod.GET, request,
//            object : ParameterizedTypeReference<List<AdviserResponseDto>>() {}
//        )
//        assertEquals(HttpStatus.OK, result.statusCode)
//        assertEquals(2, result.body!!.size)
//
//
//        result = restTemplate.exchange(
//            "/api/investor/advisers?q=name", HttpMethod.GET, request,
//            object : ParameterizedTypeReference<List<AdviserResponseDto>>() {}
//        )
//        assertEquals(HttpStatus.OK, result.statusCode)
//        assertEquals(1, result.body!!.size)
//
//
//        result = restTemplate.exchange(
//            "/api/investor/advisers?q=zxc", HttpMethod.GET, request,
//            object : ParameterizedTypeReference<List<AdviserResponseDto>>() {}
//        )
//        assertEquals(HttpStatus.OK, result.statusCode)
//        assertEquals(0, result.body!!.size)
//    }
//}